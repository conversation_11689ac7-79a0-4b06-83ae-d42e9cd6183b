package com.autoai.car.os_adapter.imp;

import android.content.Context;

import androidx.annotation.NonNull;

import com.autoai.car.os_adapter.interfaces.CarInfoInterface;

/**
 * <AUTHOR>
 * <p>
 * 管理车机走行相关业务
 */
public class CarInfoImp implements CarInfoInterface {

    private static CarInfoImp instance;

    /**
     * 单例
     *
     * @return
     */
    public static CarInfoImp getInstance() {
        if (instance == null) {
            synchronized (CarInfoImp.class) {
                instance = new CarInfoImp();
            }
        }
        return instance;
    }


    @Override
    public void init(@NonNull Context context) {
    }

    @Override
    public int getCurDriveState() {
        return -1;
    }

    @Override
    public void onCheckIsPlayVideoResult(int isPlay) {
    }

    @Override
    public void addVehicleGearChangeListener(@NonNull DriveStateChangedListener driveStateChangedListener) {
    }

    @Override
    public void removeVehicleGearChangeListener(@NonNull DriveStateChangedListener driveStateChangedListener) {
    }

    @Override
    public int getCurGearValue() {
        return -1;
    }
}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:orientation="vertical"
    android:background="#fff"
    android:layout_gravity="center"
    android:layout_height="wrap_content">


    <ImageView
        android:layout_width="wrap_content"
        android:src="@drawable/bg_window"
        android:layout_height="wrap_content">

    </ImageView>
    <TextView
        android:layout_marginTop="30dp"
        android:id="@+id/tv_text"
        android:layout_gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/updata_version"
        android:textColor="#000"
        android:textSize="16sp" />

   <RelativeLayout
       android:layout_width="match_parent"
       android:layout_marginTop="20dp"
       android:layout_height="wrap_content">
       <ProgressBar
           android:id="@+id/progressBar"
           style="?android:attr/progressBarStyleHorizontal"
           android:layout_width="match_parent"
           android:layout_marginLeft="50dp"
           android:layout_marginRight="50dp"
           android:layout_height="20dp"
           android:progress="100" />
       <TextView
           android:id="@+id/tv_progressBar"
           android:layout_marginLeft="30dp"
           android:layout_centerHorizontal="true"
           android:layout_width="wrap_content"
           android:layout_weight="1"
           android:gravity="center"
           android:layout_height="wrap_content"
           android:text="100%"
           android:textColor="#fff"
           android:background="@drawable/btn_move_bg"
           android:textSize="16sp" />
   </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="horizontal"
        android:layout_marginTop="30dp"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/tv_cancel"
            android:layout_marginLeft="30dp"
            android:layout_gravity="center"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:text="@string/cancel"
            android:layout_marginRight="60dp"
            android:textColor="#fff"
            android:padding="10dp"
            android:background="@drawable/btn_move_bg"
            android:textSize="16sp" />
        <TextView
            android:id="@+id/tv_upgrade"
            android:padding="10dp"
            android:layout_marginRight="30dp"
            android:layout_gravity="center"
            android:layout_width="0dp"
            android:gravity="center"
            android:layout_weight="1"
            android:background="@drawable/btn_move_bg"
            android:layout_height="wrap_content"
            android:text="@string/upgrade_now"
            android:textColor="#fff"
            android:textSize="16sp" />
    </LinearLayout>

</LinearLayout>
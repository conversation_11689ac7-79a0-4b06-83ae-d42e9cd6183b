<!-- Generated by SpecStory -->

# 讨论设备的DESCRIPTOR_FORMAT类型 (2025-07-01 09:09Z)

_**User**_

DESCRIPTOR_FORMAT 有几个类型的设备？

---

_**Assistant**_

从代码中可以看到，实际上只有一个 `DESCRIPTOR_FORMAT` 是未被注释的，它定义了一个触控屏幕（Touch Screen）HID描述符。其他几个描述符（`DESCRIPTOR_FORMAT1`, `DESCRIPTOR_FORMAT3`, `DESCRIPTOR_FORMAT5`）都被注释掉了，暂时没有被使用。

活跃的 `DESCRIPTOR_FORMAT` 描述的是一个触控屏幕设备，它支持单点触控操作，包含了触摸状态、接触标识符以及X、Y坐标信息。

---

_**User**_

有 digitizer 类型的设备么？

---

_**Assistant**_

是的，当前未被注释的 `DESCRIPTOR_FORMAT` 就是一个 Digitizer 类型的设备。在描述符的开头部分可以看到：

```
0x05.toByte(), 0x0D.toByte(),  // Usage Page (Digitizer)
0x09.toByte(), 0x04.toByte(),  // Usage (Touch Screen)
```

这表明它使用的是 Digitizer 使用页(Usage Page)，具体用途是触控屏幕(Touch Screen)。整个描述符定义了一个数字化仪(Digitizer)设备类型，专门用于处理触摸输入。

---

_**User**_

整个协议的结构是啥样子的。

---

_**Assistant**_

HID描述符定义的协议结构如下：

1. **顶层结构**：应用集合(Application Collection)
   - 类型：触控屏幕(Touch Screen)
   - 使用页：数字化仪(Digitizer)
   - 报告ID：1

2. **物理集合层**：手指(Finger)集合
   - 包含以下输入报告字段：
     - 触点开关(Tip Switch)：1位，表示是否触摸
     - 填充位：7位，保留字段
     - 接触标识符(Contact Identifier)：8位，触点ID，最大值10
     - X坐标：16位，逻辑范围0-3840(0x0F00)
     - Y坐标：16位，与X坐标相同的逻辑范围

3. **附加字段**：
   - 接触计数(Contact Count)：8位，支持的最大触点数为8

这个协议结构定义了一个支持多点触控的触摸屏输入设备，可以报告触摸状态、触点ID和X/Y坐标值。报告数据按照描述符中定义的顺序和大小排列，设备使用这个格式向主机发送触摸事件信息。

---


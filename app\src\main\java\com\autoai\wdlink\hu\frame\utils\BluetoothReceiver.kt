package com.autoai.wdlink.hu.frame.utils

import android.bluetooth.BluetoothProfile
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.autoai.wdlink.hu.Logger


/**
 * <AUTHOR> zhanggc
 * @description : java类作用描述
 * @date : 2024/4/12 15:13
 * @version : 1.0
 */
class BluetoothReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent) {
        val action = intent.action
        Logger.d("BluetoothReceiver--------> action:"+action+",intent="+intent)
      if(RequestBTMusicFocusUtil.ACTION_CONNECTION_STATE_CHANGED == action){
            var state:Int = intent.flags
            Logger.d("BluetoothReceiver-------->ACTION_CONNECTION_STATE_CHANGED state:"+state)
          val prevState = intent.getIntExtra(BluetoothProfile.EXTRA_PREVIOUS_STATE, 0);
          val nowstate = intent.getIntExtra(BluetoothProfile.EXTRA_STATE, 0);
          Logger.d("BluetoothReceiver-------->prevState:$prevState,nowstate:$nowstate")
          if(prevState != BluetoothProfile.STATE_CONNECTED && nowstate == BluetoothProfile.STATE_CONNECTED){
              //RequestBTMusicFocusUtil.requestBTMusicFocus(context)
              Logger.d("BluetoothReceiver-------->STATE_ON 手机蓝牙开启")
              BtMusicFocusManager.requestBtMusicFocus(null)
              //已连接
          }else if(prevState != BluetoothProfile.STATE_DISCONNECTED && nowstate == BluetoothProfile.STATE_DISCONNECTED) {
              //已断开
              Logger.d("BluetoothReceiver-------->STATE_OFF 手机蓝牙关闭")
              BtMusicFocusManager.abandonBtMusicFocus(null)
          }
        }
    }
}
package com.autoai.wdlink.hu.sdk

import android.graphics.Matrix
import android.graphics.SurfaceTexture
import android.util.Log
import android.view.Surface
import android.view.TextureView
import android.view.View
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.model.DevelopModel
import com.autoai.wdlink.hu.sdk.device.OnVideoFrameListener
import com.autoai.welinkapp.model.SdkViewModel

/**
 * 用于管理渲染视图的核心管理器
 */
class SurfaceHelper {
    private val TAG = "SurfaceHelper"
    private val TAG_SHE_REVIEW: String = "shecw1"

    /**
     * 视图层传入的TextureView控件
     */
    private var curTextureView: TextureView? = null
    private var currSurface: Surface? = null

    //    private var existingSurfaceTexture: SurfaceTexture? = null
//    private var placeholderSurfaceTexture: SurfaceTexture? = null
    private var mAngle: Int = 0
    private var mPlatform: String? = null
    private var mCastState: String? = null
    private var lastExecutionTime: Long = 0

    /**
     * TextureView 刷新率回调
     */
    private var frameListener: OnVideoFrameListener? = null
    private var mFpsEnabled = false
    private var mRttEnabled = false
    private var mRtt = 0

    constructor() {
        Logger.i("SurfaceHelper: init")
        DevelopModel.getInstance().setFpsListener {
            mFpsEnabled = it
            Logger.i("SurfaceHelper: mFpsEnabled = $mFpsEnabled")
        }
        DevelopModel.getInstance().setRttListener { rttEnabled, rtt ->
            run {
                this.mRttEnabled = rttEnabled
                this.mRtt = rtt
            }

        }

    }

    /**
     * 切换 Surface,投屏选人的surface 的size 发生变化
     */
    fun resetSurface(textureView: TextureView) {
        //-->从布局中获取负责投屏渲染的Texture View
        curTextureView = textureView;// phoneSurfaceParent.findViewById(R.id.phoneData_sv)

        Log.i(
            TAG_SHE_REVIEW,
            "SurfaceHelper::resetSurface :" + curTextureView!!.surfaceTexture + ",surfaceTextureListener:" + curTextureView!!.surfaceTextureListener
        )
        Logger.i("resetSurface :" + curTextureView!!.surfaceTexture + ",surfaceTextureListener:" + curTextureView!!.surfaceTextureListener)

        //--> 做一次反注册容错
        unRegisterLinkSurface()

        if (curTextureView!!.surfaceTexture != null) {
            registerLinkSurface(curTextureView!!)
        }
        //重新注册监听 ，ios 视频流横竖平切换
        curTextureView!!.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(
                surface: SurfaceTexture,
                width: Int,
                height: Int
            ) {
                LinkLog.i(
                    TAG,
                    "onSurfaceTextureAvailable: surface = $surface, width = $width, height = $height"
                )

                LinkLog.i(
                    TAG_SHE_REVIEW,
                    "SurfaceHelper::resetSurface -> onSurfaceTextureAvailable: surface = $surface"
                )

//                if (existingSurfaceTexture != null) {
//                    // --> fixme:这个逻辑是否有必要存在
//                    curTextureView?.setSurfaceTexture(existingSurfaceTexture!!)
//                } else {
//                    unregisterPlaceholderLinkSurfaceIfNeed()
//                    existingSurfaceTexture = surface
//                }

//                existingSurfaceTexture = surface
                registerLinkSurface(curTextureView!!)

//                surfaceStateAvailableCb?.onSurfaceAvailable()
                //退出全屏时候不显示画面
                if (mPlatform.equals("ios")) {
                    onSurfaceTextureSizeChanged(surface, width, height)
                }

                SdkViewModel.getInstance().isPaused = false
            }

            override fun onSurfaceTextureSizeChanged(
                surface: SurfaceTexture,
                width: Int,
                height: Int
            ) {

                LinkLog.i(
                    TAG_SHE_REVIEW,
                    "SurfaceHelper::resetSurface -> onSurfaceTextureSizeChanged: surface = $surface ;" +
                            "mAngle = " + mAngle.toFloat() + "; width = $width; height = $height "
                )

                //--> 调整buffer size
               // surface.setDefaultBufferSize(height, width)

               /* if (mPlatform.equals("ios") && mAngle.toFloat() - 180 < 0) {
                    //---> ios，横竖切换
                    val matrix = Matrix()

                    matrix.reset()
                    //--->原始尺寸
                    val originalWidth: Float = height.toFloat()     //举例：900
                    val originalHeight: Float = width.toFloat()     //举例：1600
                    //--->改变后尺寸
                    val targetWidth: Float = width.toFloat()        //举例：1600
                    val targetHeight: Float = height.toFloat()      //举例：900
                    // --->计算缩放比例，保持宽高比不变
                    val scaleX = targetWidth / originalWidth        // 举例：16/9
                    val scaleY = targetHeight / originalHeight      // 举例：9/16


                    LinkLog.i(TAG, "onSurfaceTextureSizeChanged: scaleX = $scaleX")
                    LinkLog.i(TAG, "onSurfaceTextureSizeChanged: scaleY = $scaleY")
                    LinkLog.i(
                        TAG_SHE_REVIEW,
                        "onSurfaceTextureSizeChanged: ios 旋转 ：mAngle.toFloat() = " + mAngle.toFloat()
                    )
                    //--->先旋转
                    matrix.postRotate(mAngle.toFloat() - 180, 0f, 0f)

                    //--->旋转后对画面进行偏移修正      900 * 16 /9 (实际上是 originalHeight )
                    matrix.postTranslate(0f, height * scaleX)
                    // ---> 缩放恢复  scaleX->
                    matrix.postScale(scaleX, scaleY)


//                    if (existingSurfaceTexture == null) {
//                        existingSurfaceTexture = surface
//                    }
//                    currSurface = Surface(existingSurfaceTexture)
                    //--->设置矩阵
                    curTextureView!!.setTransform(matrix)

                    //--->将resize 后的 surface 重新注册给解码器，
                    LinkSdkModel.registerLinkSurface(currSurface!!, height, width)
                    LinkLog.i(TAG, "onSurfaceTextureSizeChanged11: width = $width")
                    LinkLog.i(TAG, "onSurfaceTextureSizeChanged11: height = $height")
                } else {
                    //---> Android 直接用新的param进行复制
                    //---> fixme:矩阵没用去掉
                    val matrix = Matrix()

                    matrix.reset()
//                    if (existingSurfaceTexture == null) {
//                        existingSurfaceTexture = surface
//                    }
//                    currSurface = Surface(existingSurfaceTexture)
                    curTextureView!!.setTransform(matrix)

                    //--->将resize 后的 surface 重新注册给解码器
                    LinkSdkModel.registerLinkSurface(currSurface!!, width, height)

                    LinkLog.i(
                        TAG,
                        "onSurfaceTextureSizeChanged22: width = $width ;height = $height"
                    )
                }*/
            }

            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                LinkLog.i(
                    TAG_SHE_REVIEW,
                    "SurfaceHelper::resetSurface -> onSurfaceTextureDestroyed: surface = $surface"
                )
                //复用 SurfaceTexture
//                    unRegisterLinkSurface()

//                surfaceStateAvailableCb?.onSurfaceDestroyed()
                SdkViewModel.getInstance().isPaused = true
                return false
            }

            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
//                    LinkLog.i(TAG, "onSurfaceTextureUpdated: surface = $surface")
                if (mFpsEnabled) {
                    getFps()
                }
            }
        }

    }

    /**
     * 监听渲染surface是否可用
     */
//    private var surfaceStateAvailableCb: SurfaceStateAvailableCb? = null;
//    fun registerSurfaceStateCb(surfaceStateAvailableCb: SurfaceStateAvailableCb) {
//        this.surfaceStateAvailableCb = surfaceStateAvailableCb
//    }
//
//    fun unRegisterSurfaceStateCb() {
//        this.surfaceStateAvailableCb = null
//    }
//
//    interface SurfaceStateAvailableCb {
//        /**
//         * 可用
//         */
//        fun onSurfaceAvailable();
//
//        /**
//         * 销毁
//         */
//        fun onSurfaceDestroyed();
//    }


    private var mFps = 0
    private var mLastTime: Long = 0

    /**
     * 调试方法，跟踪实际刷新率
     */
    private fun getFps() {
        frameListener?.let {
            mFps++
            val timeStamp = System.currentTimeMillis()
            if (timeStamp - mLastTime >= 1000) {
                LinkLog.i(TAG, "fps ==>$mFps")
                it.onVideoFrame(mFps)
                mFps = 0
                mLastTime = timeStamp
            }
        }
    }

    fun clearSurface() {
        Logger.i("clearSurface")
        Log.i(TAG_SHE_REVIEW, "clearSurface")

//        phoneSurfaceParent.removeAllViews()
        curTextureView?.surfaceTextureListener = null
        unRegisterLinkSurface()
    }

    /**
     * she tips:这个方法将texture view 封装成Surface 会触发解码器开启，currSurface作为解码器的output Surface
     */
    private fun registerLinkSurface(textureView: TextureView) {
        Logger.i("registerLinkSurface")
        Log.i(TAG_SHE_REVIEW, "registerLinkSurface")

        currSurface = Surface(textureView.surfaceTexture)

        //--> she tips:这个方法会触发解码器开启，currSurface作为解码器的output Surface
        LinkSdkModel.registerLinkSurface(currSurface!!, textureView.width, textureView.height)
        LinkLog.i(TAG, "registerLinkSurface: width = " + textureView.width)
        LinkLog.i(TAG, "registerLinkSurface: height = " + textureView.height)

    }

    /**
     * 1.release 掉当前编码器持有的surface，
     * 2.release 掉预先站位的surface
     */
    private fun unRegisterLinkSurface() {
        Logger.i("unRegisterLinkSurface: currSurface = $currSurface")
        Log.i(TAG_SHE_REVIEW, "unRegisterLinkSurface: currSurface = $currSurface")
        if (currSurface != null) {
            //--> //--> she tips:这个方法会触发解码器关闭
            LinkSdkModel.unRegisterLinkSurface(currSurface!!)
//            existingSurfaceTexture?.release()
//            existingSurfaceTexture = null
            currSurface = null
        }

        unregisterPlaceholderLinkSurfaceIfNeed()
    }

    /**
     * 创建一个占位 surface 给解码器使用，先让解码器跑起来，等 TextureView 的 surface 可用后进行替换，
     */
    fun registerPlaceholderLinkSurfaceIfNeed(width: Int, height: Int) {
//        Logger.i("registerPlaceholderLinkSurface: width = $width, height = $height, currSurface = $currSurface")
//        Log.i(
//            TAG_SHE_REVIEW,
//            "registerPlaceholderLinkSurface: width = $width, height = $height, currSurface = $currSurface"
//        )
//        if (currSurface != null) {
//            return
//        }
//        val surfaceTexture = SurfaceTexture(0).also { placeholderSurfaceTexture = it }
//        val surface = Surface(surfaceTexture).also { currSurface = it }
//        LinkSdkModel.registerLinkSurface(surface, width, height)
    }

    private fun unregisterPlaceholderLinkSurfaceIfNeed() {
//        Logger.i("unregisterPlaceholderLinkSurface: placeholderSurfaceTexture = $placeholderSurfaceTexture")
//        Log.i(
//            TAG_SHE_REVIEW,
//            "unregisterPlaceholderLinkSurface: placeholderSurfaceTexture = $placeholderSurfaceTexture"
//        )
//
//        if (placeholderSurfaceTexture != null) {
//            placeholderSurfaceTexture?.release()
//            placeholderSurfaceTexture = null
//        }
    }

    fun setAngleAndPlatform(angle: Int, platform: String, castState: String? = null) {
        mAngle = angle
        mPlatform = platform
        mCastState = castState
    }

    fun setOnVideoFrameListener(listener: OnVideoFrameListener) {
        this.frameListener = listener
    }
}
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bar_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="top|center_horizontal"
    android:layout_marginTop="45dp"
    android:padding="10dp"
    android:background="@drawable/background_cron"
    android:orientation="vertical"
    tools:context=".controlbar.AirControlDialogFragment">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center"
            android:id="@+id/button_mini"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="38dp"
                android:layout_height="38dp"
                android:layout_margin="2dp"
                android:focusable="false"
                android:src="@drawable/minus"
                android:tint="@color/onCardBackground"
                tools:ignore="UseAppTint" />
            <TextView
                android:text="最小化"
                android:layout_marginLeft="10dp"
                android:textColor="#f00"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>


        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center"
            android:visibility="gone"
            android:id="@+id/button_full"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="38dp"
                android:layout_height="38dp"
                android:layout_margin="2dp"
                android:focusable="false"
                android:src="@drawable/expand"
                android:tint="@color/onCardBackground"
                tools:ignore="UseAppTint" />

            <TextView
                android:text="校准"
                android:layout_marginLeft="10dp"
                android:textColor="#f00"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>


    </LinearLayout>


   <!-- <LinearLayout
        android:layout_marginTop="20dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="38dp"
                android:layout_height="38dp"
                android:layout_margin="2dp"
                android:focusable="false"
                android:src="@drawable/minus"
                android:tint="@color/onCardBackground"
                tools:ignore="UseAppTint" />
            <TextView
                android:text="111"
                android:layout_marginLeft="10dp"
                android:textColor="#f00"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>


        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="38dp"
                android:layout_height="38dp"
                android:layout_margin="2dp"
                android:focusable="false"
                android:src="@drawable/expand"
                android:tint="@color/onCardBackground"
                tools:ignore="UseAppTint" />

            <TextView
                android:text="111"
                android:layout_marginLeft="10dp"
                android:textColor="#f00"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>


    </LinearLayout>-->









<!--

    <ImageView
        android:id="@+id/reset_location"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_margin="2dp"
        android:focusable="false"
        android:visibility="gone"
        android:src="@drawable/reset_location"
        android:tint="@color/onCardBackground" />


    <ImageView
        android:id="@+id/button_nav_bar"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_margin="2dp"
        android:focusable="false"
        android:visibility="gone"
        android:src="@drawable/not_equal"
        android:tint="@color/onCardBackground" />
-->


   <!-- <ImageView
        android:id="@+id/button_rotate"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_margin="2dp"
        android:focusable="false"
        android:src="@drawable/rotate"
        android:visibility="gone"
        android:tint="@color/onCardBackground" />

    <ImageView
        android:id="@+id/button_transfer"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_margin="2dp"
        android:focusable="false"
        android:visibility="gone"
        android:src="@drawable/share_out"
        android:tint="@color/onCardBackground" />

    <ImageView
        android:id="@+id/button_light"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_margin="2dp"
        android:focusable="false"
        android:visibility="gone"
        android:src="@drawable/lightbulb"
        android:tint="@color/onCardBackground" />

    <ImageView
        android:id="@+id/button_light_off"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_margin="2dp"
        android:focusable="false"
        android:visibility="gone"
        android:src="@drawable/lightbulb_off"
        android:tint="@color/onCardBackground" />

    <ImageView
        android:id="@+id/button_power"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_margin="2dp"
        android:focusable="false"
        android:visibility="gone"
        android:src="@drawable/power_off"
        android:tint="@color/onCardBackground" />

    <ImageView
        android:id="@+id/button_close"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:visibility="gone"
        android:layout_margin="2dp"
        android:focusable="false"
        android:src="@drawable/x"
        android:tint="@color/alert" />-->
</LinearLayout>
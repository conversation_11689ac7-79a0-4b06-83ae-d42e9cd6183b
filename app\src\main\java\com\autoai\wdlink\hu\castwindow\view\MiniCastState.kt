package com.autoai.wdlink.hu.castwindow.view

import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState
import com.autoai.wdlink.hu.castwindow.scope.MiniPositionMode.Companion.resolveMiniMode
import com.autoai.wdlink.hu.castwindow.scope.MiniPositionMode.Companion.toMiniModeStr
import com.autoai.wdlink.hu.frame.utils.MiniPisition
import com.autoai.wdlink.hu.sdk.LinkSdkModel

/**
 * 非应用内，悬浮窗最小化形态
 */
class MiniCastState: BaseCastState() {
    private var miniView: MiniView? = null

    override val state: Int
        get() = CastWindowState.MINI

    override fun onEnterState(args: Any?) {
        super.onEnterState(args)
        val miniPisition = args as MiniPisition
        LinkLog.i(TAG, "onEnterState: miniPositionHeight = ${miniPisition.miniPositionHeight}")
        if(miniView == null){
            miniView = MiniView()
        }

        val mode = miniPisition.miniPositionMode.resolveMiniMode().also {
            LinkLog.i(TAG, "onEnterState: mode = ${it.toMiniModeStr()}")
        }
        miniView?.show(mode,miniPisition.miniPositionHeight,miniPisition.position)

        LinkSdkModel.onPause()
    }

    override fun onExitState() {
        super.onExitState()
        miniView?.hide()
        LinkSdkModel.onResume()
    }
}
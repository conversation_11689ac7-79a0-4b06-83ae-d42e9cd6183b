<!-- Generated by SpecStory -->

# 移动逻辑修改为绕圈缩小 (2025-05-16 06:14Z)

_**User**_

修改移动逻辑。 修改成 绕圈缩小的规则。 也就是 y 往上移动一下， x 往左移动一下。 然后 y 依次往下移动，然后 x 往右移动。如何循环到结束。

---

_**Assistant**_

I'll modify the movement logic to create a spiral-like pattern that gradually reduces in size. I'll update the code to alternate between Y and X axes following a spiral pattern.

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java

---

Let me implement the spiral movement pattern by updating the relevant code section.

---

```diff
                  return;
              }
-             // 定义新的步长序列，并按此序列进行往返移动
- //            int[] ySteps = {100, 75, 50, 45, 40, 35, 30, 25, 20, 15, 10, 5};
- //            int[] ySteps = {100, 95, 90, 85, 80, 75, 70, 65, 60, 55, 50, 45, 40, 35, 30, 25, 20, 15, 10, 5};
-             int[] ySteps = {100, 95, 90, 85, 80, 75, 70, 65, 60, 55};
-             int[] xSteps = { 50, 45, 40, 35, 30, 25, 20, 15, 10, 5};
-             boolean moveDown = true;
- 
-             for (int yIdx = 0; yIdx < ySteps.length; yIdx++) {
-                 // 获取当前步长
-                 int currentYStep = ySteps[yIdx];
- 
-                 // 根据移动方向决定正负值
-                 int actualYStep = moveDown ? currentYStep : -currentYStep;
- 
-                 // 执行移动
-                 if(!mouseCalibrationV2(1, 0, actualYStep)){
-                     LogUtil.e(TAG, "mouseCalibration break down");
+             // 定义步长数组，用于绕圈螺旋式缩小移动
+             int[] steps = {100, 90, 80, 70, 60, 50, 40, 30, 20, 10};
+             
+             // 定义四个方向：上、左、下、右
+             final int UP = 0;
+             final int LEFT = 1;
+             final int DOWN = 2;
+             final int RIGHT = 3;
+             
+             for (int step : steps) {
+                 // 螺旋式移动：上→左→下→右，每次循环缩小步长
+                 
+                 // 向上移动
+                 if(!mouseCalibrationV2(1, 0, -step)){
+                     LogUtil.e(TAG, "mouseCalibration break down during UP");
                      return;
                  }
- 
-                 // 每次循环后切换方向， 避免越界
-                 moveDown = !moveDown;
-             }
+                 
+                 // 向左移动
+                 if(!mouseCalibrationV2(1, -step, 0)){
+                     LogUtil.e(TAG, "mouseCalibration break down during LEFT");
+                     return;
+                 }
+                 
+                 // 向下移动
+                 if(!mouseCalibrationV2(1, 0, step)){
+                     LogUtil.e(TAG, "mouseCalibration break down during DOWN");
+                     return;
+                 }
+                 
+                 // 向右移动
+                 if(!mouseCalibrationV2(1, step, 0)){
+                     LogUtil.e(TAG, "mouseCalibration break down during RIGHT");
+                     return;
+                 }
+                 
+                 // 每完成一圈后稍作停顿
+                 sleepTime(50);
+             }
+             
              //--> 将鼠标复位
              reset();
```

---

I've successfully modified the movement logic in the `startVerificationThreadV2` method to create a spiral pattern that gradually reduces in size. Here's what the new implementation does:

1. I replaced the previous alternating y-axis movement with a complete spiral pattern:
   - Each iteration moves in a full square: up → left → down → right
   - The step size reduces with each iteration (100, 90, 80, ..., 10)

2. For each step size in the sequence, it performs:
   - Upward movement (negative y-step)
   - Leftward movement (negative x-step)
   - Downward movement (positive y-step)
   - Rightward movement (positive x-step)

3. After each complete loop, a small 50ms delay is added to allow the system to process the movements.

4. Each movement has a specific error message to help identify where any failure might occur.

This spiral pattern provides better coverage of the screen, creating a more comprehensive calibration process. The movement gradually reduces in size, creating an inward spiral effect that should help increase calibration accuracy.

---

_**User**_

Y 方向上使用ySteps， X 方向使用 xSteps.

---

_**Assistant**_

I'll modify the code to use separate step arrays for X and Y directions to give more control over the movement pattern.

---

```diff
                  return;
              }
-             // 定义步长数组，用于绕圈螺旋式缩小移动
-             int[] steps = {100, 90, 80, 70, 60, 50, 40, 30, 20, 10};
-             
-             // 定义四个方向：上、左、下、右
-             final int UP = 0;
-             final int LEFT = 1;
-             final int DOWN = 2;
-             final int RIGHT = 3;
-             
-             for (int step : steps) {
-                 // 螺旋式移动：上→左→下→右，每次循环缩小步长
+             // 分别为Y方向和X方向定义不同的步长数组
+             int[] ySteps = {100, 90, 80, 70, 60, 50, 40, 30, 20, 10};
+             int[] xSteps = {50, 45, 40, 35, 30, 25, 20, 15, 10, 5};
+             
+             // 确保两个数组长度一致
+             int iterations = Math.min(ySteps.length, xSteps.length);
+             
+             for (int i = 0; i < iterations; i++) {
+                 int yStep = ySteps[i];
+                 int xStep = xSteps[i];
+                 
+                 // 螺旋式移动：上→左→下→右，每次循环使用对应索引的步长
                  
-                 // 向上移动
-                 if(!mouseCalibrationV2(1, 0, -step)){
+                 // 向上移动（使用yStep）
+                 if(!mouseCalibrationV2(1, 0, -yStep)){
                      LogUtil.e(TAG, "mouseCalibration break down during UP");
                      return;
                  }
                  
-                 // 向左移动
-                 if(!mouseCalibrationV2(1, -step, 0)){
+                 // 向左移动（使用xStep）
+                 if(!mouseCalibrationV2(1, -xStep, 0)){
                      LogUtil.e(TAG, "mouseCalibration break down during LEFT");
                      return;
                  }
                  
-                 // 向下移动
-                 if(!mouseCalibrationV2(1, 0, step)){
+                 // 向下移动（使用yStep）
+                 if(!mouseCalibrationV2(1, 0, yStep)){
                      LogUtil.e(TAG, "mouseCalibration break down during DOWN");
                      return;
                  }
                  
-                 // 向右移动
-                 if(!mouseCalibrationV2(1, step, 0)){
+                 // 向右移动（使用xStep）
+                 if(!mouseCalibrationV2(1, xStep, 0)){
                      LogUtil.e(TAG, "mouseCalibration break down during RIGHT");
                      return;
```

---

I've updated the movement logic to use separate step arrays for X and Y directions as requested:

1. Now the code uses:
   - `ySteps` array for vertical movements (up and down)
   - `xSteps` array for horizontal movements (left and right)

2. Key changes made:
   - Added separate step arrays with different values
   - Used `Math.min(ySteps.length, xSteps.length)` to ensure we don't exceed either array's bounds
   - Each iteration of the spiral uses the corresponding index from both arrays
   - Movement comments now clarify which step value is being used

3. The spiral pattern remains the same (up → left → down → right), but now with more precise control over the horizontal and vertical step sizes.

This implementation gives you more flexibility to fine-tune the calibration movement in each direction separately, which can be useful if the device needs different sensitivity for horizontal versus vertical movements.

---


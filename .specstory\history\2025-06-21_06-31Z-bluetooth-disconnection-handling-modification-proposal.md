<!-- Generated by SpecStory -->

# Bluetooth disconnection handling modification proposal (2025-06-21 06:31Z)

_**User**_

@@bluetooth_calibration_icon_display_logic.md  请先阅读这个，特别是背景， 然后 @bluetooth_calibration_issues.md  分析这个问题 ， 对于这里第二问题，你建议如何修改，请给出合理、完善的修改方案，只要给出方案，不要实施。 

---

_**Assistant**_

I'll analyze the bluetooth calibration icon display logic and focus on problem #2 from the issues document.

After reviewing both documents, I understand that the main issue is with how bluetooth disconnection is handled differently across platforms.

## 问题2: 蓝牙状态监听器的平台差异处理不一致

**问题描述**: 
- 在 `btConnectedListener.onDisconnected()` 方法中，只对鸿蒙设备调用 `onBluetoothConnectionChanged(false)`
- 文档第42、44、45条提到所有平台都应该通过 `onDisconnected` 检测蓝牙断开

**修改方案**:

1. 修改 `btConnectedListener.onDisconnected()` 方法，使其对所有平台统一处理蓝牙断开事件:

```java
private BluetoothStateListener btConnectedListener = new BluetoothStateListener() {
    @Override
    public void onDisconnected() {
        // 移除平台限制，对所有平台统一处理蓝牙断开事件
        onBluetoothConnectionChanged(false);
    }

    @Override
    public void onConnected() {
        onBluetoothConnectionChanged(true);
    }
};
```

2. 在 `onBluetoothConnectionChanged()` 方法中，确保所有平台在蓝牙断开时都能正确更新状态:

```java
private void onBluetoothConnectionChanged(boolean isConnected) {
    if (!isConnected) {
        // 蓝牙断开时，对所有平台统一设置状态为 BT_DISCONNECTED
        bluetoothStateManager.updateState(BluetoothAbnormalState.BT_DISCONNECTED);
        
        // 如果已互联，显示提示
        if (LinkSdkModel.INSTANCE.isConnected()) {
            showToast(context.getString(R.string.bluetooth_disconnected));
        }
    } else {
        // 蓝牙连接时，触发状态检查
        checkAndUpdateBluetoothState();
    }
}
```

3. 确保 `checkAndUpdateBluetoothState()` 方法中的逻辑与此保持一致:

```java
private void checkAndUpdateBluetoothState() {
    // 其他现有代码...
    
    // 确保在判断蓝牙断开状态时，所有平台使用相同的判断逻辑
    if (btDevices.isEmpty()) {
        // 对所有平台，蓝牙设备列表为空时统一显示为未连接
        bluetoothStateManager.updateState(BluetoothAbnormalState.BT_NOT_CONNECTED);
        return;
    }
    
    // 其他现有代码...
}
```

4. 修改蓝牙状态监听器的注册方式，确保所有平台都能正确接收到断开事件:

```java
private void initBluetoothListener() {
    // 确保对所有平台都注册蓝牙状态监听器
    HidUtil.registerBluetoothStateListener(btConnectedListener);
}
```

5. 在适当的生命周期方法中注册和解除监听器:

```java
@Override
public void onCreate() {
    super.onCreate();
    // 其他初始化代码...
    initBluetoothListener();
}

@Override
public void onDestroy() {
    super.onDestroy();
    // 解除监听器
    HidUtil.unregisterBluetoothStateListener(btConnectedListener);
    // 其他清理代码...
}
```

这个修改方案统一了所有平台的蓝牙断开检测逻辑，确保iOS、Android和鸿蒙平台在蓝牙断开时都能得到一致的处理，符合文档中第4、42、44、45条的要求。

---


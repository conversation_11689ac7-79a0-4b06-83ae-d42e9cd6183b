package com.autoai.wdlink.hu.frame.utils

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.LinkLog.Companion.printStackTraceString
import com.autoai.wdlink.hu.castwindow.utils.ThreadInfoUtils

/**
 * <AUTHOR> zhanggc
 * @description : java类作用描述
 * @date : 2024/6/26 19:33
 * @version : 1.0
 */
 object  PopWinServiceUtils {
     private val TAG = "${ComponentName.Link}${this.javaClass.simpleName}"
  fun startPopWinService(activity: Context) {
      sendPopWinService(activity,"start")
  }
    fun stopPopWinService(activity: Context) {
        sendPopWinService(activity,"stop")
    }

    /**
     * 移除弹窗
     */
    fun removePopWinService(activity: Context) {
        sendPopWinService(activity,"remove")
    }

    /**
     * fixme:<AUTHOR>  触发的实机有多处
     */
    fun showPopWin(activity: Context,state : String? = null) {
        sendPopWinService(activity,"show",state)
    }
    fun removePopWin(activity: Context) {
        sendPopWinService(activity,"hide")
    }
    fun showPopWinLayout(activity: Context) {
        sendPopWinService(activity,"layout")
    }
    fun showUSBPopWin(activity: Context) {
        sendPopWinService(activity,"showDialog")
    }
    fun dismissUSBPopWin(activity: Context) {
        sendPopWinService(activity,"dismissDialog")
    }

    fun notifyScreenState(activity: Context, state: String) {
        sendPopWinService(activity,state)
    }
    fun destroyPopWin(activity: Context) {
        LinkLog.i(TAG, "destroyPopWin: activity = $activity")
        LinkLog.d(TAG, "destroyPopWin: stack: ${ThreadInfoUtils.getStackTraces()}")
        val intent = Intent()
        intent.setClassName(activity.packageName, PopWinService::class.java.name)
        activity.stopService(intent)
    }
    private fun sendPopWinService(context: Context, action: String , state: String? = null) {

        LinkLog.i(TAG, "sendPopWinService: context = $context, action = $action, stack: ${ThreadInfoUtils.getStackTraces()}")
        val intent = Intent()
        intent.setClassName(context.packageName, PopWinService::class.java.name)
        intent.setAction(action)
        intent.putExtra("state",state)
        context.startForegroundService(intent)
    }

    /**
     * bitmap增加圆角
     */
    @JvmStatic
    fun bitmapFillet(bitmap: Bitmap): Bitmap {
        val source = Bitmap.createScaledBitmap(bitmap, bitmap.width, bitmap.height, false)
        val widthLight = source.width
        val heightLight = source.height
        val output = Bitmap.createBitmap(source.width, source.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(output)
        val paintColor = Paint()
        paintColor.flags = Paint.ANTI_ALIAS_FLAG
        val rectF = RectF(Rect(0, 0, widthLight, heightLight))
        val radii = floatArrayOf(30f, 30f, 30f, 30f, 30f, 30f, 30f, 30f)
        val path = Path()
        path.addRoundRect(rectF, radii, Path.Direction.CW)
        canvas.drawPath(path, paintColor)
        val paintImage = Paint()
        paintImage.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_ATOP))
        canvas.drawBitmap(source, 0f, 0f, paintImage)
        return output
    }
}
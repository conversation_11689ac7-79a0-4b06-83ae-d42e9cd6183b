<!-- Generated by SpecStory -->

# Untitled (2025-06-24 11:18Z)

_**User**_

ON_TOUCH_END_POINT 是否可以区分， 是ON_TOUCH_POINT_READY-》ON_TOUCH_APP_FRONT 切换到后台，还是ON_TOUCH_POINT_READY 直接因为在后台返回。 两种给 UI 界面的提示不一致。请仔细分析，并给出修改方案，写到 todo。 等待我确认。

---

_**Assistant**_

I'll analyze the `ON_TOUCH_END_POINT` handling in the code to determine if we can differentiate between the two scenarios you mentioned.

Looking at the SdkViewModel.java file, I can see how iOS calibration is handled. Let me examine the relevant sections:

---

Read file: docs/ios_calibration_flow_sequence.md

---

Let me check if there's any documentation specifically about the issue with `ON_TOUCH_END_POINT` differentiation:

---

Read file: docs/bluetooth_calibration_icon_display_logic.md

---

Let me check if there's any documentation about the issues with the calibration differentiation:

---

Read file: docs/bluetooth_calibration_optimization.md

---

Now let me check if there's a specific analysis document for `ON_TOUCH_END_POINT` differentiation:

---

Read file: docs/@todo/on_touch_end_point_differentiation_analysis.md

---

Let me check more about this issue in the todo directory:

---

Read file: todo/bluetooth_calibration_issues.md

---

After analyzing the code and related documentation, I'm ready to provide my assessment and solution.

## Analysis of ON_TOUCH_END_POINT Issue

Currently, `ON_TOUCH_END_POINT` is used for a single purpose in `SdkViewModel.java`:

```java
} else if (type == ON_TOUCH_END_POINT) {
    //ios手机端发送 校准失败 结束校准
    onTouchSendEnd();
}

private void onTouchSendEnd() {
    LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendEnd <<< iOS app went to background, calibration failed");
    HidIosCalibrationUtil.setBreakDown(true);
    HidIosCalibrationUtil.setHidVerificationState(HID_VERIFY_TYPE_VERIFICATION_FAIL);
    onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL);
}
```

There's no way to differentiate between two scenarios:
1. App was in foreground (`ON_TOUCH_POINT_READY` → `ON_TOUCH_APP_FRONT`) but then went to background
2. App was never in foreground and directly sent `ON_TOUCH_END_POINT`

## Proposed Solution

Based on the analysis in `on_touch_end_point_differentiation_analysis.md`, I recommend implementing **Option 1: Extending State Constants**. This is the clearest solution that requires minimal changes and maintains backward compatibility.

Here's my implementation plan:

1. Add new constants in the iOS side:
   - `ON_TOUCH_END_POINT_FROM_FOREGROUND = 6` - App was in foreground but went to background
   - `ON_TOUCH_END_POINT_FROM_BACKGROUND = 7` - App was never in foreground

2. Add new failure type constants:
   - `ON_HID_APP_BACKGROUND_FAIL_FROM_FOREGROUND = 4` - For UI to display "校准已取消，请保持应用在前台进行校准"
   - `ON_HID_APP_BACKGROUND_FAIL_FROM_BACKGROUND = 5` - For UI to display "请将应用切换到前台后重新校准"

3. Modify the processing logic in `SdkViewModel.java` to handle these new states

## Specific Code Changes

Here's my recommended modification to the `todo/` directory to document this solution:

---



---

Let me try again with the edit:

---



---

Let me try with search_replace to add the new issue:

---



---

Now, let's update the modification plan in the existing todo file:

---



---

Now let's create a detailed implementation plan in a new file:

---

Cancelled

---

```bash
cat todo/on_touch_end_point_differentiation_analysis.md
```

---

Based on my analysis, I've identified the issue with `ON_TOUCH_END_POINT` in the iOS calibration process. Currently, there's no way to differentiate between two important scenarios:

1. When the iOS app was in foreground (`ON_TOUCH_POINT_READY` → `ON_TOUCH_APP_FRONT`) but then went to background
2. When the iOS app was never in foreground and directly returned `ON_TOUCH_END_POINT`

## Recommended Solution

I recommend implementing a solution by extending the state constants to provide clearer differentiation between these scenarios:

1. Add new state constants:
   - `ON_TOUCH_END_POINT_FROM_FOREGROUND` (6): App was in foreground but went to background
   - `ON_TOUCH_END_POINT_FROM_BACKGROUND` (7): App was never in foreground

2. Add new failure type constants:
   - `ON_HID_APP_BACKGROUND_FAIL_FROM_FOREGROUND` (4): For UI to display "校准已取消，请保持应用在前台进行校准"
   - `ON_HID_APP_BACKGROUND_FAIL_FROM_BACKGROUND` (5): For UI to display "请将应用切换到前台后重新校准"

3. Update processing logic in `SdkViewModel.java` and UI handling in `PopWinService.java`

4. Coordinate with iOS team to implement the appropriate distinction between these states

This solution maintains backward compatibility while providing much better user experience with more accurate error messages. I've documented this issue and solution in the todo/bluetooth_calibration_issues.md file.

Please let me know if you'd like me to proceed with creating more detailed implementation specifications.

---


<!-- Generated by SpecStory -->

# 绘制车机端初始化时序图 (2025-06-26 06:25Z)

_**User**_

你是专业的安卓架构师。 现在需要根据现有的项目工程，绘制架构图。 接下来需要绘制 有线传输车机端的初始化时序图。  涉及到的模块 有应用层、LinkHost、SDkViewModel， DataTransfer, ConnctManager, AOAManager, EAPManager, AOACheckDevice, EAPCheckDevice, 和 Android Framework.  请深呼吸，然后一步一步来搜索查找各自的调用关系。  绘制出以上几个角色类的 初始化调用关系。 

---

_**Assistant**_

I'll search for the relevant code to understand the initialization sequence for the wired transmission on the car side. Let me examine the codebase to find the relevant components and their relationships.

---

<details>
            <summary>Searched codebase "SdkViewModel initialization" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L198-252 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L78-154 |
| `app/src/main/java/com/autoai/wdlink/hu/viewmodel/InitViewModel.kt` | L1-13 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/viewmodel/InitViewModel.kt` | L1-13 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L44-65 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L510-573 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L252-302 |
| `app/src/main/java/com/autoai/wdlink/hu/viewmodel/MainActivityViewModel.kt` | L1-55 |
| `app/src/main/java/com/autoai/wdlink/hu/viewmodel/TimeoutViewModel.kt` | L1-20 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L573-637 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/viewmodel/TimeoutViewModel.kt` | L1-20 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L154-198 |
| `app/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L51-81 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/viewmodel/MainActivityViewModel.kt` | L1-67 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L65-118 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkMsg.java` | L1-32 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/BaseViewModel.kt` | L1-29 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L212-261 |
| `app/src/main/java/com/autoai/wdlink/hu/DevActivity.kt` | L1-27 |
| `app/src/main/java/com/autoai/wdlink/hu/castwindow/controlbar/AirControlViewModel.kt` | L1-15 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/frame/BaseViewModel.kt` | L1-29 |
| `app/src/main/java/com/autoai/wdlink/hu/viewmodel/DevelopViewModel.java` | L1-67 |
| `avslink/gradle.properties` | L1-35 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkUtil.kt` | L1-67 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L1-44 |

</details>

---

Let me search for information about ConnectManager, which appears to be a key component in the wired transmission initialization.

---

<details>
            <summary>Searched codebase "ConnectManager init aoa eap" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L1-45 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L1-59 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L1-60 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L78-109 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L47-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L85-115 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L60-117 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L59-73 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L109-137 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L341-384 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L117-158 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L148-176 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L1-48 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L81-109 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L58-81 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L1-52 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L212-261 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/IusbDevice.java` | L1-66 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L117-180 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L310-341 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapLink.java` | L1-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapDevice.java` | L1-95 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L222-266 |

</details>

---

Now, let's look for information about AOAManager and AOACheckDevice:

---

<details>
            <summary>Searched codebase "AOAManager AOACheckDevice initialization" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L58-123 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L1-28 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L76-105 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L28-76 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L1-47 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L222-277 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/IDBCheckDevice.java` | L54-88 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L144-187 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L174-236 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/DeviceManager.java` | L1-46 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L304-335 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L359-397 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L52-83 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L1-45 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/aoa/AoaLink.java` | L1-22 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L277-304 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L373-427 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L105-144 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L456-503 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/IDBCheckDevice.java` | L1-54 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L85-115 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L1-59 |

</details>

---

Let's search for more information about the Android framework interaction and the LinkSdkModel which seems to be a bridge:

---

<details>
            <summary>Searched codebase "LinkSdkModel initialization connect" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L65-118 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L510-573 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L1-45 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L156-185 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L78-154 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L230-268 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L268-299 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L44-65 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkUtil.kt` | L1-67 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L118-156 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L105-147 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L722-744 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L1-44 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L697-722 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L212-261 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L744-778 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkUtil.kt` | L114-157 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkUtil.kt` | L1-66 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L26-69 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L261-327 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L198-252 |
| `app/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L51-81 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L664-697 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L404-451 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L1-43 |

</details>

---

Now, let's look for the LinkHost class which seems to be a central initialization component:

---

<details>
            <summary>Searched codebase "LinkHost init" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/gradle.properties` | L1-35 |
| `gradle.properties` | L1-43 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L1-43 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L43-117 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L261-327 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L327-388 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L117-180 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/aoa/AoaLink.java` | L1-22 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L230-268 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapLink.java` | L1-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L212-261 |
| `avslink/linkHost/build.gradle` | L1-67 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkUtil.kt` | L1-67 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/idb/IdbLink.java` | L1-22 |
| `avslink/linkHost/.gitignore` | L1-2 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/platform/LinkPlatform.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L1-58 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L1-44 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L69-105 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L1-59 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapDevice.java` | L1-95 |
| `settings.gradle` | L1-40 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L180-212 |
| `link-hid/gradle.properties` | L1-26 |
| `app/build.gradle` | L164-203 |

</details>

---

Based on the information gathered, I'll now create a sequence diagram showing the initialization flow for the wired transmission system on the car side. Let me use the create_diagram tool to visualize this:

---



---

Based on my analysis of the codebase, here's a summary of the wired transmission initialization sequence on the car side:

## Wired Transmission Car-Side Initialization Sequence

The initialization flow involves multiple components working together to establish a connection between the car system and mobile devices. The process starts from the application layer and goes through several middleware layers before interacting with the Android Framework.

### Key Components Involved:

1. **Application Layer**: The top-level application that initiates the connection process
2. **LinkSdkModel**: Provides a high-level API for the application to interact with the connection system
3. **LinkHost**: Central coordinator for initializing the various components
4. **SdkViewModel**: Core component managing the connection state and data flow
5. **ConnectManager**: Handles device connection state management
6. **DataTransfer**: Manages data transmission between the car and connected devices
7. **AOACheckDevice/EAPCheckDevice**: Detects connected devices using AOA/EAP protocols
8. **AOAManager/EAPManager**: Handles the actual data transfer for AOA/EAP connections
9. **Android Framework**: Provides low-level USB and system services

### Initialization Flow:

1. The application calls `LinkSdkModel.initLink(context)` to start the initialization process
2. LinkSdkModel creates a SdkConfig with necessary parameters and calls `LinkHost.init(context, sdkConfig)`
3. LinkHost initializes SdkViewModel which sets up:
   - WindowUtil for display management
   - SdkApi for native message communication
   - DataTransfer for handling data transmission
   - ConnectManager for managing connection state

4. ConnectManager initializes device detection components:
   - AOACheckDevice for Android devices
   - EAPCheckDevice for iOS devices (if EAP model is enabled)
   - DeviceStatusReceiver for handling connection status changes

5. DataTransfer initializes connection managers:
   - AOAManager for Android connections
   - EAPManager for iOS connections (if EAP model is enabled)

6. When the application calls `readyForLink()`, the system:
   - Activates SdkViewModel with `onCreate()`, `onResume()` and `startCheckConnect()`
   - Initializes SdkApi to handle native callbacks
   - Starts device detection through ConnectManager

7. AOACheckDevice and EAPCheckDevice scan for connected devices:
   - When a device is detected, they notify the respective managers
   - AOAManager and EAPManager establish the connection and start data transfer

8. Status updates are propagated back through the system via callbacks and listeners

The diagram illustrates how these components interact during the initialization process, showing the flow from high-level application APIs down to the device-specific managers that handle the actual connections.

---

_**User**_

startCheckCOnnect() 中 resume 是使用再什么场景，里面是如何实现的，也绘制成时序图。 中文回答。

---

_**Assistant**_

我将检查 `startCheckConnect()` 方法中 `resume` 的使用场景和实现细节，然后绘制相应的时序图。

---

<details>
            <summary>Searched codebase "ConnectManager startCheckConnect resume" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L85-115 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L115-158 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L1-45 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/helper/DeviceListAdapter.java` | L177-211 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L510-573 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L58-81 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/IDBCheckDevice.java` | L118-139 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L359-397 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L1-48 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L138-180 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L109-137 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L299-341 |
| `app/src/main/java/com/autoai/wdlink/hu/lifecycle/NetworkMonitor.kt` | L1-29 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L78-109 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L158-186 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L47-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L341-384 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L722-744 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L52-83 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L1-59 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkStatusKtx.kt` | L1-8 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/RequestBTMusicFocusUtil.kt` | L1-52 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L456-503 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L1-52 |

</details>

---

我需要更详细地了解 AOACheckDevice 和 EAPCheckDevice 的 resume 方法实现：

---

<details>
            <summary>Searched codebase "AOACheckDevice resume implementation" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L359-397 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L28-76 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L76-105 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L1-28 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L144-187 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L222-277 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L52-83 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L277-304 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L58-81 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L85-115 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L58-123 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L304-335 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L456-503 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/IDBCheckDevice.java` | L54-88 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L105-144 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L187-222 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L174-236 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L78-109 |
| `app/src/main/java/com/autoai/wdlink/hu/auth/AuthRepository.kt` | L23-74 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L1-48 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L83-106 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/aoa/AoaLink.java` | L1-22 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L1-47 |

</details>

---

现在我需要了解 EapLink 的相关内容，以了解 EAPCheckDevice.resume() 的完整功能：

---

<details>
            <summary>Searched codebase "EapLink getEapStatus EapStatus EAP_ATTACH" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapStatus.java` | L1-16 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L81-109 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L148-176 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L58-81 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L109-145 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/OnLinkEapListener.java` | L1-15 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapDevice.java` | L1-95 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L1-60 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapLink.java` | L78-144 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L1-45 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L87-135 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L137-160 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L89-137 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L135-160 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapLink.java` | L1-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L60-117 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L266-310 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L341-384 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L41-89 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L117-158 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L1-59 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L1-41 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L42-87 |

</details>

---

基于以上的代码分析，我现在可以绘制 `startCheckConnect()` 中 `resume` 的时序图：

---



---

## `startCheckConnect()` 中 `resume` 的使用场景与实现

`startCheckConnect()` 方法是车机端有线传输初始化流程中的重要环节，它负责开始检查并建立与设备的连接。在这个方法中，`resume` 函数的调用是在特定条件下进行的，主要用于恢复或重新初始化设备连接。

### 使用场景

`resume` 方法在以下场景中被调用：

1. 当没有指定连接类型（`CommonData.iNeedConnectType == CommonData.DEVICE_TYPE_NONE`）时
2. 且当前连接状态既不是已连接也不是正在连接中（`SdkViewModel.getInstance().connectStatus != PlatformAdaptor.CONNECT_STATE.CONNECTED && SdkViewModel.getInstance().connectStatus != PlatformAdaptor.CONNECT_STATE.CONNECTING`）时

这种情况通常发生在：
- 系统初始化时
- 之前的连接断开后
- 系统从后台恢复到前台时
- 需要重新扫描检测设备时

### 实现细节

`startCheckConnect()` 中会同时调用 `AOACheckDevice.resume()` 和 `EAPCheckDevice.resume()`，分别处理 Android 设备和 iOS 设备的连接恢复：

#### AOACheckDevice.resume() 实现

1. 检查当前是否有已连接的 USB 设备
2. 如果有设备且当前没有选中的设备 (`mUsbDevice == null`)：
   - 遍历所有已连接的 USB 设备
   - 过滤掉不符合条件的设备（如 iOS 设备、存储设备等）
   - 将找到的有效设备设置为当前设备 (`mUsbDevice`)
3. 将连接状态设置为未连接（`CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT`）
4. 如果找到的设备已经处于 AOA 模式，则调用 `resetUsbDeviceIfNecessary()` 重置设备
5. 否则调用 `checkDevices()` 来检查并尝试连接设备

AOA（Android Open Accessory）是 Android 设备与外部硬件通信的协议，这里主要用于 Android 手机与车机的连接。

#### EAPCheckDevice.resume() 实现

1. 获取当前 EAP 的连接状态（`EapLink.getEapStatus()`）
2. 如果状态为 `EAP_ATTACH`（表示苹果设备已经物理连接但还未建立逻辑连接）：
   - 设置 `setUsbDeviceMode = true`（标记 USB 模式已切换）
   - 调用 `EapLink.setUsbMode(true)` 将 USB 切换为设备模式，以便与 iOS 设备建立连接

EAP 是用于 iOS 设备连接的协议，因为 iOS 设备需要特殊的认证和处理方式。

### 总结

`resume` 方法在 `startCheckConnect()` 中的作用是：

1. **检测已连接设备**：检查是否有设备已经物理连接但尚未建立逻辑连接
2. **恢复连接状态**：尝试恢复之前中断的连接
3. **设备模式切换**：为不同类型的设备（Android/iOS）设置适当的连接模式
4. **初始化连接准备**：为后续的连接建立做好准备工作

通过这种机制，车机端可以在各种情况下（如应用启动、从后台恢复、设备重新连接等）尽可能地保持连接的稳定性和一致性，确保用户体验的流畅。

---

_**User**_

你是专业的安卓架构师。 现在需要根据现有的项目工程，绘制架构图。 接下来需要绘制 有线传输车机端的设备发现环节和逻辑。  涉及到的模块 有应用层、LinkHost、SDkViewModel， DataTransfer, ConnctManager, AOAManager, EAPManager, AOACheckDevice, EAPCheckDevice, 和 Android Framework.  EAP SDK, DeviceStatusReceiver.  请深呼吸，然后一步一步来搜索查找各自的调用关系。  绘制出以上几个角色类的 设备发现逻辑。 

---

_**Assistant**_

我将搜索项目代码以理解有线传输车机端的设备发现环节和逻辑，然后绘制架构图。让我们一步步进行。

---

<details>
            <summary>Searched codebase "AOACheckDevice checkDevices" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L1-28 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L144-187 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L28-76 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L76-105 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L105-144 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L52-83 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L304-335 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L222-277 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L277-304 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/IDBCheckDevice.java` | L54-88 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L359-397 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L187-222 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L58-123 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/IDBCheckDevice.java` | L118-139 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L174-236 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/IDBCheckDevice.java` | L1-54 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L1-48 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L83-106 |
| `app/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L188-222 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L85-115 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L335-359 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L1-47 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L106-130 |

</details>

---

接下来让我查看EAPCheckDevice的设备发现逻辑：

---

<details>
            <summary>Searched codebase "EAPCheckDevice onLinkDeviceCallbackType" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L81-109 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L89-137 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/OnLinkEapListener.java` | L1-15 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L87-135 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L148-176 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapLink.java` | L1-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapDevice.java` | L1-95 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapLink.java` | L78-144 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L58-81 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/IusbDevice.java` | L1-85 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L1-60 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L137-160 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L1-41 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L1-45 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L1-42 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L135-160 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L109-145 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/IusbDevice.java` | L88-126 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/IusbDevice.java` | L66-88 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L41-89 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L117-180 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L43-117 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L42-87 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L1-59 |

</details>

---

让我了解一下DeviceStatusReceiver的工作原理：

---

<details>
            <summary>Searched codebase "DeviceStatusReceiver onReceive filterDeviceInMsg" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L137-163 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L47-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L1-47 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L78-109 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L109-137 |
| `app/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L320-353 |
| `app/src/main/java/com/autoai/wdlink/hu/auth/AuthModel.kt` | L32-71 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L1-48 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L180-215 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/service/WirelessReceiver.java` | L1-52 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/IDBCheckDevice.java` | L88-118 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/service/WirelessReceiver.java` | L92-142 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L52-83 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L451-473 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/helper/MyBroadcastReceiver.java` | L1-49 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusListener.java` | L1-25 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L464-480 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L105-144 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L230-253 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L473-510 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L517-553 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L119-153 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L82-138 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/IDBManager.java` | L104-151 |

</details>

---

接下来让我查看连接管理器是如何处理设备发现事件的：

---

<details>
            <summary>Searched codebase "ConnectManager startCheckConnect DeviceStatusListener" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L1-45 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusListener.java` | L1-25 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L115-158 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L85-115 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L1-47 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L109-137 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L1-52 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L47-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L1-48 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L78-109 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L137-163 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/IDBCheckDevice.java` | L118-139 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L118-156 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L1-58 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L156-185 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L52-90 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L52-83 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L138-180 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/helper/DeviceListAdapter.java` | L177-211 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L144-187 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/OnLinkStatusChangeListener.java` | L1-16 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L82-138 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L81-109 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/IusbDevice.java` | L1-66 |

</details>

---

让我了解一下SdkViewModel如何与ConnectManager交互的部分：

---

<details>
            <summary>Searched codebase "SdkViewModel deviceStatusListener onDeviceIn" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusListener.java` | L1-25 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L451-473 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L137-163 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L118-156 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L1-47 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L473-510 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/OnLinkStatusChangeListener.java` | L1-16 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L156-185 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L404-451 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L573-637 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L65-118 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/jni/CallBack/DEVICE_STATUS_CALLBACK.java` | L1-5 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidBtStateReceiver.kt` | L226-265 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L47-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L1-45 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L230-253 |
| `app/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L1110-1135 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidBtStateReceiver.kt` | L265-312 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L1-58 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L26-69 |
| `app/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L188-222 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L82-138 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L109-137 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L81-109 |

</details>

---

基于以上的代码分析，我现在可以绘制一个详细的架构图，展示有线传输车机端的设备发现环节和逻辑流程：

---



---

# 有线传输车机端设备发现环节架构分析

基于对代码的深入分析，我构建了一个完整的架构图，展示有线传输车机端的设备发现逻辑。下面是对整个流程的详细说明。

## 架构概述

有线传输车机端的设备发现涉及多个组件协同工作，形成了一个分层结构：

1. **应用层** - 为用户提供界面，启动设备发现流程
2. **LinkSdkModel/LinkHost** - 提供统一的API接口，作为中间层连接应用和底层服务
3. **SdkViewModel** - 核心业务逻辑层，协调各组件工作
4. **ConnectManager** - 连接管理器，负责检测和管理设备连接状态
5. **设备检测层** - AOACheckDevice(Android设备)和EAPCheckDevice(iOS设备)负责特定设备类型的检测
6. **数据传输层** - DataTransfer、AOAManager、EAPManager负责数据通道的建立和管理
7. **系统框架层** - Android Framework和专用SDK(如EAP SDK)提供底层支持

## 设备发现流程

### 1. 初始化阶段

1. 应用层调用`LinkSdkModel.initLink(context)`启动初始化
2. 请求依次传递到`LinkHost`和`SdkViewModel`
3. `SdkViewModel`初始化`ConnectManager`，传入`deviceStatusListener`回调
4. `ConnectManager`初始化`AOACheckDevice`、`EAPCheckDevice`和`DeviceStatusReceiver`
5. 同时初始化`DataTransfer`，进而初始化`AOAManager`和`EAPManager`

### 2. 设备发现触发

1. 应用层调用`LinkSdkModel.readyForLink(context)`开始设备发现
2. 请求传递到`SdkViewModel.startCheckConnect()`
3. `ConnectManager.startCheckConnect()`被调用，开始检测流程
4. 设置`CommonData.isCheckingStart = true`，表示开始检测

### 3. 设备检测逻辑分支

根据是否有指定连接类型，进入不同分支：

1. **有指定AOA连接类型**：直接调用`AOACheckDevice.checkDevices()`
2. **有指定EAP连接类型**：发送`CONNECT_STATUS_IN`消息
3. **有指定IDB连接类型**：调用`IDBCheckDevice.checkDevices()`
4. **无指定连接类型且未连接**：调用`AOACheckDevice.resume()`和`EAPCheckDevice.resume()`

### 4. Android设备发现流程

1. **USB设备插入**：系统广播`ACTION_USB_ATTACH`被`DeviceStatusReceiver`接收
2. 调用`AOACheckDevice.checkDevices()`
3. 获取当前连接的USB设备列表，过滤掉存储设备和iOS设备
4. 对找到的可用设备调用`initUsbDevice(device)`
5. 检查设备是否已处于AOA模式：
   - 是：直接发送`CONNECT_STATUS_IN`消息
   - 否：调用`switchToAOA()`切换设备到AOA模式

### 5. iOS设备发现流程

1. iOS设备连接后，EAP SDK回调`onLinkDeviceCallbackType(EAP_ATTACH)`
2. `EAPCheckDevice`调用`setUsbMode(true)`将USB切换到设备模式
3. EAP SDK回调`onLinkDeviceCallbackType(EAP_ONLINE_ATTACH)`
4. `EAPCheckDevice`调用`activeEap()`激活EAP连接
5. EAP SDK回调`onLinkDeviceCallbackType(EAP_AUTHENTICATION_PASS)`
6. `EAPCheckDevice`调用`eapLaunchApp()`并发送`CONNECT_STATUS_IN`消息

### 6. 设备连接状态处理

1. 发送`DEVICE_IN_MSG`广播
2. `DeviceStatusReceiver`接收并调用`filterDeviceInMsg(type, ip)`
3. 根据设备类型启动对应的连接：
   - AOA设备：`DataTransfer.startConnect(DEVICE_TYPE_AOA)`，进而调用`AOAManager.startAOA()`
   - EAP设备：`DataTransfer.startConnect(DEVICE_TYPE_EAP)`，进而调用`EAPManager.startEAP()`
4. 调用`deviceStatusListener.onDeviceIn(type, ip)`将状态通知给`SdkViewModel`
5. `SdkViewModel`更新设备类型，调用`mSdkApi.deviceChg(type, ip)`
6. 分发连接状态`dispatchLinkState(LINK_STATUS_CONNECTED)`
7. 状态变化通过回调链最终通知到应用层

## 关键组件职责

1. **ConnectManager**：协调设备检测，启动设备发现流程
2. **AOACheckDevice/EAPCheckDevice**：负责具体设备类型的检测和初始化
3. **DeviceStatusReceiver**：处理设备连接状态广播，协调数据传输启动
4. **DataTransfer**：管理数据传输通道，调用具体的传输管理器
5. **AOAManager/EAPManager**：负责特定协议的数据传输实现
6. **SdkViewModel**：整合所有组件，管理整个连接生命周期

这个设备发现流程展示了一个复杂但结构清晰的系统架构，通过分层设计和事件驱动机制，实现了对多种设备类型的灵活支持和可扩展性。

---

_**User**_

分析一下设备断开流程呢。 同样的。

---

_**Assistant**_

我将分析设备断开流程，以同样的方式从代码中查找相关信息并构建架构图。

---

<details>
            <summary>Searched codebase "deviceOut onDeviceOut DeviceStatusReceiver filterDeviceOutMsg" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L78-109 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L137-163 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L1-47 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L47-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L109-137 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L180-215 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L1-48 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusListener.java` | L1-25 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L230-253 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/service/WirelessReceiver.java` | L92-142 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L82-138 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L473-510 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/macro/WL_API_DEVICE_TYPE.java` | L1-12 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L214-238 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L176-214 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/service/WirelessReceiver.java` | L1-52 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L440-496 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L83-106 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidBtStateReceiver.kt` | L226-265 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L52-83 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L324-366 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L148-176 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/IDBCheckDevice.java` | L88-118 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/jni/Enum/WL_JNI_DEVICE_TYPE.java` | L1-18 |

</details>

---

接下来我需要查看`SdkViewModel`中与设备断开相关的处理方法:

---

<details>
            <summary>Searched codebase "SdkViewModel onDeviceOut resetConnectStatus" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L573-637 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L697-722 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L1-45 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L65-118 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L722-744 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L118-156 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L138-180 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L78-109 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L156-185 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusListener.java` | L1-25 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L115-158 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L1-52 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L1-48 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L148-176 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L510-573 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `app/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L1110-1135 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L26-69 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L137-163 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L109-137 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L451-473 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L230-253 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L85-115 |
| `app/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L188-222 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L388-440 |

</details>

---

我还需要了解DataTransfer的stopConnect方法的实现细节:

---

<details>
            <summary>Searched codebase "DataTransfer stopConnect AOAManager stopAOA EAPManager stopEAP" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L59-73 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L1-59 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L78-109 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L60-117 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L1-45 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L1-60 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L1-48 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L58-123 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L45-85 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L47-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L456-503 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L341-384 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L414-456 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L85-115 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L1-47 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L266-310 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L138-180 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L158-195 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L109-137 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L117-158 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L123-174 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L321-359 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L384-408 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/DeviceManager.java` | L1-46 |

</details>

---

现在我需要了解AOAManager和EAPManager的停止连接的具体实现:

---

<details>
            <summary>Searched codebase "AOAManager stopAOA implementation" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L58-123 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L123-174 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L59-73 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/aoa/AoaLink.java` | L1-22 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L456-503 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L321-359 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/idb/VarHelper.java` | L1-41 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L414-456 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L359-414 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidAoa.java` | L1-67 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L373-427 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L589-645 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L1-28 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L503-521 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L528-589 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L76-105 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L174-236 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L645-684 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L481-528 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java` | L277-304 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L1-47 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L1-59 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/DeviceManager.java` | L1-46 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L78-109 |

</details>

---

接下来了解EapLink的断开实现:

---

<details>
            <summary>Searched codebase "EapLink eapBulkClose" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L89-137 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L87-135 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapLink.java` | L78-144 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L60-117 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L117-158 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapLink.java` | L1-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/eap/EapDevice.java` | L1-95 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L137-160 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L341-384 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L135-160 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L310-341 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/IusbDevice.java` | L126-195 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/IusbDevice.java` | L1-85 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L1-60 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L158-195 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L109-145 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java` | L58-81 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L266-310 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L195-222 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java` | L384-408 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L41-89 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java` | L1-59 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/EapDeviceImpl.kt` | L42-87 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/IusbDevice.java` | L85-115 |

</details>

---

让我看一下无线连接设备的断开流程:

---

<details>
            <summary>Searched codebase "WirelessReceiver onDisdock disconnect DEVICE_OUT_MSG" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/service/WirelessReceiver.java` | L92-142 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L78-109 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L180-215 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/service/WirelessReceiver.java` | L1-52 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L137-163 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/direct/DockChannelListener.java` | L1-20 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/direct/DockChannel.java` | L135-177 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L1-47 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L1-48 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/service/WirelessReceiver.java` | L52-92 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/direct/DockChannel.java` | L1-26 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L214-238 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java` | L47-78 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/WLHardwareListener.java` | L1-29 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/direct/DockChannel.java` | L533-562 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L176-214 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/direct/DockChannel.java` | L215-260 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L1-52 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/direct/DockChannel.java` | L88-106 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/WLHardware.java` | L109-172 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java` | L115-158 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/direct/DockChannel.java` | L465-533 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/SystemUsbReceiver.java` | L83-106 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/net/NetInspectorListener.java` | L1-12 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L388-440 |

</details>

---

根据以上代码分析，我现在可以绘制设备断开流程的时序图：

---



---

# 有线传输车机端设备断开流程分析

通过分析项目代码，我整理出了完整的设备断开流程架构。以下是对整个断开流程的详细说明。

## 设备断开流程概述

设备断开流程是设备发现和连接的反向过程，根据不同的设备类型（有线Android、有线iOS和无线设备），有着稍微不同的处理路径，但整体流程类似。设备断开通常由以下三种情况触发：

1. **物理断开**：用户拔出USB线或主动断开WiFi连接
2. **超时断开**：连接状态异常或通信超时
3. **主动断开**：应用层主动发起断开连接

## 断开流程详细说明

### 1. 有线设备断开流程（Android-AOA设备）

1. **断开触发**：Android Framework检测到USB设备拔出事件
2. **AOACheckDevice**接收到设备拔出事件，通过`sendMsg`发送`CONNECT_STATUS_OUT`消息
3. **DeviceStatusReceiver**接收到`DEVICE_OUT_MSG`广播
4. 执行`filterDeviceOutMsg`方法，确认是AOA设备（Android设备）断开
5. 调用`DataTransfer.stopConnect(DEVICE_TYPE_AOA)`
6. `DataTransfer`调用`AOAManager.stopAOA()`
7. `AOAManager`停止所有数据收发线程，并设置`isAOADeviceStart = false`
8. `DeviceStatusReceiver`调用`onDeviceOut`回调通知`SdkViewModel`
9. 重置连接状态`CommonData.resetConnectStatus()`

### 2. 有线设备断开流程（iOS-EAP设备）

1. **断开触发**：Android Framework检测到USB设备拔出事件
2. **EAPCheckDevice**接收到设备拔出事件，发送`CONNECT_STATUS_OUT`消息
3. **DeviceStatusReceiver**接收到`DEVICE_OUT_MSG`广播
4. 执行`filterDeviceOutMsg`方法，确认是EAP设备（iOS设备）断开
5. 调用`DataTransfer.stopConnect(DEVICE_TYPE_EAP)`
6. `DataTransfer`调用`EAPManager.stopEAP()`
7. `EAPManager`停止所有数据收发线程，并设置`isEAPDeviceStart = false`
8. 调用`closeNode()`方法关闭节点，该方法会调用`EapLink.eapBulkClose()`关闭EAP的数据通道
9. `DeviceStatusReceiver`调用`onDeviceOut`回调通知`SdkViewModel`
10. 重置连接状态`CommonData.resetConnectStatus()`

### 3. 无线设备断开流程

1. **断开触发**：无线连接断开事件
2. **WirelessReceiver**的`wifiListener.onDisdock()`方法被调用
3. 通过`sendBroadcast`发送`DEVICE_OUT_MSG`广播
4. **DeviceStatusReceiver**接收到`DEVICE_OUT_MSG`广播
5. 执行`filterDeviceOutMsg`方法，确认是无线设备断开
6. `DeviceStatusReceiver`调用`onDeviceOut`回调通知`SdkViewModel`
7. 重置连接状态`CommonData.resetConnectStatus()`

### 4. 共通的断开后处理流程

所有设备类型断开后，都会执行以下步骤：

1. `SdkViewModel`的`resetConnectStatus()`被调用
2. 发送`DISCONNECT`消息到底层
3. 调用`connectStatusChange(type, LINK_STATUS_NONE)`更新连接状态
4. 分发连接状态事件`dispatchLinkState(LINK_STATUS_NONE)`
5. 通知`LinkSdkModel`状态变化
6. `LinkSdkModel`更新状态并通知UI层
7. 应用层收到通知并更新UI，显示设备已断开

## 架构特点

1. **事件驱动**：整个断开流程是基于事件驱动的，通过广播和回调机制实现
2. **松耦合**：各组件间通过接口和广播进行通信，降低了耦合度
3. **分层处理**：从底层设备检测到UI层显示，每一层都有明确的职责划分
4. **统一入口**：所有设备类型的断开事件最终都通过`DeviceStatusReceiver`集中处理

这种架构设计使得系统能够灵活应对不同类型设备的断开情况，同时保持了代码的可维护性和扩展性。

---


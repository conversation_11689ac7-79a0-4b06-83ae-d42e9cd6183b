// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.GridLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;
import top.eiyooooo.easycontrol.app.client.view.MyViewForSmallView;

public final class ModuleSmallViewBinding implements ViewBinding {
  @NonNull
  private final MyViewForSmallView rootView;

  @NonNull
  public final FrameLayout bar;

  @NonNull
  public final GridLayout barView;

  @NonNull
  public final ImageView buttonBack;

  @NonNull
  public final ImageView buttonClose;

  @NonNull
  public final ImageView buttonFull;

  @NonNull
  public final ImageView buttonHome;

  @NonNull
  public final ImageView buttonLight;

  @NonNull
  public final ImageView buttonLightOff;

  @NonNull
  public final ImageView buttonMini;

  @NonNull
  public final ImageView buttonNavBar;

  @NonNull
  public final ImageView buttonPower;

  @NonNull
  public final ImageView buttonRotate;

  @NonNull
  public final ImageView buttonSwitch;

  @NonNull
  public final ImageView buttonTransfer;

  @NonNull
  public final EditText editText;

  @NonNull
  public final ImageButton ivClose;

  @NonNull
  public final ImageButton ivMini;

  @NonNull
  public final ImageButton ivMove;

  @NonNull
  public final LinearLayout llTitle;

  @NonNull
  public final MyViewForSmallView myView;

  @NonNull
  public final LinearLayout navBar;

  @NonNull
  public final View reSize;

  @NonNull
  public final ImageView resetLocation;

  @NonNull
  public final RelativeLayout rlContext;

  @NonNull
  public final LinearLayout textureViewLayout;

  private ModuleSmallViewBinding(@NonNull MyViewForSmallView rootView, @NonNull FrameLayout bar,
      @NonNull GridLayout barView, @NonNull ImageView buttonBack, @NonNull ImageView buttonClose,
      @NonNull ImageView buttonFull, @NonNull ImageView buttonHome, @NonNull ImageView buttonLight,
      @NonNull ImageView buttonLightOff, @NonNull ImageView buttonMini,
      @NonNull ImageView buttonNavBar, @NonNull ImageView buttonPower,
      @NonNull ImageView buttonRotate, @NonNull ImageView buttonSwitch,
      @NonNull ImageView buttonTransfer, @NonNull EditText editText, @NonNull ImageButton ivClose,
      @NonNull ImageButton ivMini, @NonNull ImageButton ivMove, @NonNull LinearLayout llTitle,
      @NonNull MyViewForSmallView myView, @NonNull LinearLayout navBar, @NonNull View reSize,
      @NonNull ImageView resetLocation, @NonNull RelativeLayout rlContext,
      @NonNull LinearLayout textureViewLayout) {
    this.rootView = rootView;
    this.bar = bar;
    this.barView = barView;
    this.buttonBack = buttonBack;
    this.buttonClose = buttonClose;
    this.buttonFull = buttonFull;
    this.buttonHome = buttonHome;
    this.buttonLight = buttonLight;
    this.buttonLightOff = buttonLightOff;
    this.buttonMini = buttonMini;
    this.buttonNavBar = buttonNavBar;
    this.buttonPower = buttonPower;
    this.buttonRotate = buttonRotate;
    this.buttonSwitch = buttonSwitch;
    this.buttonTransfer = buttonTransfer;
    this.editText = editText;
    this.ivClose = ivClose;
    this.ivMini = ivMini;
    this.ivMove = ivMove;
    this.llTitle = llTitle;
    this.myView = myView;
    this.navBar = navBar;
    this.reSize = reSize;
    this.resetLocation = resetLocation;
    this.rlContext = rlContext;
    this.textureViewLayout = textureViewLayout;
  }

  @Override
  @NonNull
  public MyViewForSmallView getRoot() {
    return rootView;
  }

  @NonNull
  public static ModuleSmallViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ModuleSmallViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.module_small_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ModuleSmallViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bar;
      FrameLayout bar = ViewBindings.findChildViewById(rootView, id);
      if (bar == null) {
        break missingId;
      }

      id = R.id.bar_view;
      GridLayout barView = ViewBindings.findChildViewById(rootView, id);
      if (barView == null) {
        break missingId;
      }

      id = R.id.button_back;
      ImageView buttonBack = ViewBindings.findChildViewById(rootView, id);
      if (buttonBack == null) {
        break missingId;
      }

      id = R.id.button_close;
      ImageView buttonClose = ViewBindings.findChildViewById(rootView, id);
      if (buttonClose == null) {
        break missingId;
      }

      id = R.id.button_full;
      ImageView buttonFull = ViewBindings.findChildViewById(rootView, id);
      if (buttonFull == null) {
        break missingId;
      }

      id = R.id.button_home;
      ImageView buttonHome = ViewBindings.findChildViewById(rootView, id);
      if (buttonHome == null) {
        break missingId;
      }

      id = R.id.button_light;
      ImageView buttonLight = ViewBindings.findChildViewById(rootView, id);
      if (buttonLight == null) {
        break missingId;
      }

      id = R.id.button_light_off;
      ImageView buttonLightOff = ViewBindings.findChildViewById(rootView, id);
      if (buttonLightOff == null) {
        break missingId;
      }

      id = R.id.button_mini;
      ImageView buttonMini = ViewBindings.findChildViewById(rootView, id);
      if (buttonMini == null) {
        break missingId;
      }

      id = R.id.button_nav_bar;
      ImageView buttonNavBar = ViewBindings.findChildViewById(rootView, id);
      if (buttonNavBar == null) {
        break missingId;
      }

      id = R.id.button_power;
      ImageView buttonPower = ViewBindings.findChildViewById(rootView, id);
      if (buttonPower == null) {
        break missingId;
      }

      id = R.id.button_rotate;
      ImageView buttonRotate = ViewBindings.findChildViewById(rootView, id);
      if (buttonRotate == null) {
        break missingId;
      }

      id = R.id.button_switch;
      ImageView buttonSwitch = ViewBindings.findChildViewById(rootView, id);
      if (buttonSwitch == null) {
        break missingId;
      }

      id = R.id.button_transfer;
      ImageView buttonTransfer = ViewBindings.findChildViewById(rootView, id);
      if (buttonTransfer == null) {
        break missingId;
      }

      id = R.id.edit_text;
      EditText editText = ViewBindings.findChildViewById(rootView, id);
      if (editText == null) {
        break missingId;
      }

      id = R.id.iv_close;
      ImageButton ivClose = ViewBindings.findChildViewById(rootView, id);
      if (ivClose == null) {
        break missingId;
      }

      id = R.id.iv_mini;
      ImageButton ivMini = ViewBindings.findChildViewById(rootView, id);
      if (ivMini == null) {
        break missingId;
      }

      id = R.id.iv_move;
      ImageButton ivMove = ViewBindings.findChildViewById(rootView, id);
      if (ivMove == null) {
        break missingId;
      }

      id = R.id.ll_title;
      LinearLayout llTitle = ViewBindings.findChildViewById(rootView, id);
      if (llTitle == null) {
        break missingId;
      }

      MyViewForSmallView myView = (MyViewForSmallView) rootView;

      id = R.id.nav_bar;
      LinearLayout navBar = ViewBindings.findChildViewById(rootView, id);
      if (navBar == null) {
        break missingId;
      }

      id = R.id.re_size;
      View reSize = ViewBindings.findChildViewById(rootView, id);
      if (reSize == null) {
        break missingId;
      }

      id = R.id.reset_location;
      ImageView resetLocation = ViewBindings.findChildViewById(rootView, id);
      if (resetLocation == null) {
        break missingId;
      }

      id = R.id.rl_context;
      RelativeLayout rlContext = ViewBindings.findChildViewById(rootView, id);
      if (rlContext == null) {
        break missingId;
      }

      id = R.id.texture_view_layout;
      LinearLayout textureViewLayout = ViewBindings.findChildViewById(rootView, id);
      if (textureViewLayout == null) {
        break missingId;
      }

      return new ModuleSmallViewBinding((MyViewForSmallView) rootView, bar, barView, buttonBack,
          buttonClose, buttonFull, buttonHome, buttonLight, buttonLightOff, buttonMini,
          buttonNavBar, buttonPower, buttonRotate, buttonSwitch, buttonTransfer, editText, ivClose,
          ivMini, ivMove, llTitle, myView, navBar, reSize, resetLocation, rlContext,
          textureViewLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

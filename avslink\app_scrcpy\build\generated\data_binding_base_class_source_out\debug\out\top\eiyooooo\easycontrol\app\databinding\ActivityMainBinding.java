// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ExpandableListView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView buttonAdd;

  @NonNull
  public final ImageView buttonPair;

  @NonNull
  public final ImageView buttonRefresh;

  @NonNull
  public final ImageView buttonSet;

  @NonNull
  public final ExpandableListView devicesList;

  private ActivityMainBinding(@NonNull LinearLayout rootView, @NonNull ImageView buttonAdd,
      @NonNull ImageView buttonPair, @NonNull ImageView buttonRefresh, @NonNull ImageView buttonSet,
      @NonNull ExpandableListView devicesList) {
    this.rootView = rootView;
    this.buttonAdd = buttonAdd;
    this.buttonPair = buttonPair;
    this.buttonRefresh = buttonRefresh;
    this.buttonSet = buttonSet;
    this.devicesList = devicesList;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_add;
      ImageView buttonAdd = ViewBindings.findChildViewById(rootView, id);
      if (buttonAdd == null) {
        break missingId;
      }

      id = R.id.button_pair;
      ImageView buttonPair = ViewBindings.findChildViewById(rootView, id);
      if (buttonPair == null) {
        break missingId;
      }

      id = R.id.button_refresh;
      ImageView buttonRefresh = ViewBindings.findChildViewById(rootView, id);
      if (buttonRefresh == null) {
        break missingId;
      }

      id = R.id.button_set;
      ImageView buttonSet = ViewBindings.findChildViewById(rootView, id);
      if (buttonSet == null) {
        break missingId;
      }

      id = R.id.devices_list;
      ExpandableListView devicesList = ViewBindings.findChildViewById(rootView, id);
      if (devicesList == null) {
        break missingId;
      }

      return new ActivityMainBinding((LinearLayout) rootView, buttonAdd, buttonPair, buttonRefresh,
          buttonSet, devicesList);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

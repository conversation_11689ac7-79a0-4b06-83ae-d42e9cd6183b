// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ItemAddDeviceBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final EditText address;

  @NonNull
  public final TextView addressTitle;

  @NonNull
  public final CheckBox isOptions;

  @NonNull
  public final EditText name;

  @NonNull
  public final Button ok;

  @NonNull
  public final LinearLayout options;

  @NonNull
  public final Button scanAddress;

  @NonNull
  public final Button scanRemoteAppList;

  @NonNull
  public final EditText specifiedApp;

  @NonNull
  public final TextView specifiedAppTitle;

  private ItemAddDeviceBinding(@NonNull LinearLayout rootView, @NonNull EditText address,
      @NonNull TextView addressTitle, @NonNull CheckBox isOptions, @NonNull EditText name,
      @NonNull Button ok, @NonNull LinearLayout options, @NonNull Button scanAddress,
      @NonNull Button scanRemoteAppList, @NonNull EditText specifiedApp,
      @NonNull TextView specifiedAppTitle) {
    this.rootView = rootView;
    this.address = address;
    this.addressTitle = addressTitle;
    this.isOptions = isOptions;
    this.name = name;
    this.ok = ok;
    this.options = options;
    this.scanAddress = scanAddress;
    this.scanRemoteAppList = scanRemoteAppList;
    this.specifiedApp = specifiedApp;
    this.specifiedAppTitle = specifiedAppTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAddDeviceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAddDeviceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_add_device, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAddDeviceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.address;
      EditText address = ViewBindings.findChildViewById(rootView, id);
      if (address == null) {
        break missingId;
      }

      id = R.id.address_title;
      TextView addressTitle = ViewBindings.findChildViewById(rootView, id);
      if (addressTitle == null) {
        break missingId;
      }

      id = R.id.is_options;
      CheckBox isOptions = ViewBindings.findChildViewById(rootView, id);
      if (isOptions == null) {
        break missingId;
      }

      id = R.id.name;
      EditText name = ViewBindings.findChildViewById(rootView, id);
      if (name == null) {
        break missingId;
      }

      id = R.id.ok;
      Button ok = ViewBindings.findChildViewById(rootView, id);
      if (ok == null) {
        break missingId;
      }

      id = R.id.options;
      LinearLayout options = ViewBindings.findChildViewById(rootView, id);
      if (options == null) {
        break missingId;
      }

      id = R.id.scan_address;
      Button scanAddress = ViewBindings.findChildViewById(rootView, id);
      if (scanAddress == null) {
        break missingId;
      }

      id = R.id.scan_remote_app_list;
      Button scanRemoteAppList = ViewBindings.findChildViewById(rootView, id);
      if (scanRemoteAppList == null) {
        break missingId;
      }

      id = R.id.specified_app;
      EditText specifiedApp = ViewBindings.findChildViewById(rootView, id);
      if (specifiedApp == null) {
        break missingId;
      }

      id = R.id.specified_app_title;
      TextView specifiedAppTitle = ViewBindings.findChildViewById(rootView, id);
      if (specifiedAppTitle == null) {
        break missingId;
      }

      return new ItemAddDeviceBinding((LinearLayout) rootView, address, addressTitle, isOptions,
          name, ok, options, scanAddress, scanRemoteAppList, specifiedApp, specifiedAppTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

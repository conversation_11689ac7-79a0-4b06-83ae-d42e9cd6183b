<!-- Generated by SpecStory -->

# Java Calibration Strategy Implementation (2025-05-16 08:00Z)

_**User**_

@CalibrationStrategy.java  @HidIosCalibrationManager.java  参考实现一下这个类呢。

---

_**Assistant**_

I'll implement the `RegularCalibrationStrategy` class based on the provided interface and the `HidIosCalibrationManager` implementation.

First, I need to check what imports are needed and what the class should implement:

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java

---

Let me check for the `CalibrationPoint` and `MyPoint` classes:

---

<details>
            <summary>Searched codebase "CalibrationPoint" • **7** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/CalibrationPoint.java

---

<details>
            <summary>Searched codebase "MyPoint" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/model/MyPoint.java

---

Let me also check for the `Pair` class that's used in the interface:

---

<details>
            <summary>Searched codebase "import Pair" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Let me do a more specific search:

---

<details>
            <summary>Grep search for "import.*Pair" • **13** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/AdbKeyActivity.java` | L11 | `import top.eiyooooo.easycontrol.app.adb.AdbKeyPair;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/VideoDecode.java` | L6 | `import android.util.Pair;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/view/ClientView.java` | L6 | `import android.util.Pair;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/view/FullActivity.java` | L11 | `import android.util.Pair;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/Client.java` | L10 | `import android.util.Pair;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/view/SmallView.java` | L11 | `import android.util.Pair;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/helper/PublicTools.java` | L25 | `import android.util.Pair;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/IpActivity.java` | L7 | `import android.util.Pair;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/PairActivity.java` | L15 | `import top.eiyooooo.easycontrol.app.adb.AdbPairManager;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/PairActivity.java` | L16 | `import top.eiyooooo.easycontrol.app.databinding.ActivityPairBinding;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/adb/AdbKeyPair.java` | L10 | `import java.security.KeyPair;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/adb/AdbKeyPair.java` | L11 | `import java.security.KeyPairGenerator;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/entity/AppData.java` | L22 | `import top.eiyooooo.easycontrol.app.adb.AdbKeyPair;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/adb/Adb.java` | L13 | `import android.util.Pair;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/testapp/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L21 | `import android.util.Pair;` |

</details>

---

Now I have a better understanding of the components. Let me implement the `RegularCalibrationStrategy` class:

---

```diff
+ package com.autoai.avslinkhid.calibration;
+ 
+ import android.util.Pair;
+ import com.autoai.avslinkhid.model.MyPoint;
+ import com.autoai.avslinkhid.datatransfer.HidIosCalibrationManager;
+ import com.autoai.common.util.LogUtil;
+ 
+ import java.util.ArrayList;
+ import java.util.HashMap;
+ import java.util.List;
+ import java.util.Map;
+ 
+ /**
+  * Regular calibration strategy implementation.
+  * This strategy implements regular calibration methods for the HID device.
+  */
  public class RegularCalibrationStrategy implements CalibrationStrategy {
+     private static final String TAG = "RegularCalibStrategy";
+     
+     // Maps to store calibration data
+     private Map<Float, Integer> calibrationMap;
+     private boolean calibrationReady = false;
+     
+     // Delay between calibration points in milliseconds
+     private static final int DEFAULT_DELAY = 500;
+     
+     // Calibration points for testing
+     private static final int CALIBRATION_POINT_COUNT = 4;
+     
+     public RegularCalibrationStrategy() {
+         calibrationMap = new HashMap<>();
+     }
  
      @Override
      public List<CalibrationPoint> getCalibrationPoints() {
-         // TODO Auto-generated method stub
-         throw new UnsupportedOperationException("Unimplemented method 'getCalibrationPoints'");
+         List<CalibrationPoint> points = new ArrayList<>();
+         
+         // Based on HidIosCalibrationManager, we need to generate 4 calibration points
+         // with Y values that help us calculate different step sizes
+         
+         // Point with maximum step (0,0)
+         points.add(new CalibrationPoint(0, 0, DEFAULT_DELAY, true, false));
+         
+         // Point with medium step
+         points.add(new CalibrationPoint(0, 40, DEFAULT_DELAY, true, false));
+         
+         // Point with smaller step
+         points.add(new CalibrationPoint(0, 80, DEFAULT_DELAY, true, false));
+         
+         // Point with minimum step
+         points.add(new CalibrationPoint(0, 100, DEFAULT_DELAY, true, false));
+         
+         return points;
+     }
+ 
+     @Override
+     public int parseCalibrationPoints(List<MyPoint> points) {
+         try {
+             // Validate points
+             if (points == null || points.size() < CALIBRATION_POINT_COUNT) {
+                 LogUtil.e(TAG, "Invalid calibration points: " + (points == null ? "null" : points.size()));
+                 return 1; // Failed
+             }
+             
+             // Filter and validate X values (all should be the same)
+             float x = points.get(0).getX();
+             for (int i = 1; i < points.size(); i++) {
+                 if (Math.abs(points.get(i).getX() - x) > 0.1f) {
+                     LogUtil.e(TAG, "X values are not consistent");
+                     return 1; // Failed
+                 }
+             }
+             
+             // Validate Y values (all should be different)
+             for (int i = 0; i < points.size() - 1; i++) {
+                 if (Math.abs(points.get(i).getY() - points.get(i + 1).getY()) < 0.1f) {
+                     LogUtil.e(TAG, "Y values are not different enough");
+                     return 1; // Failed
+                 }
+             }
+             
+             // Calculate steps and build the calibration map
+             float stepMax = points.get(0).getY() - points.get(1).getY();
+             float stepMid = points.get(1).getY() - points.get(2).getY();
+             float stepMin = points.get(2).getY() - points.get(3).getY();
+             
+             LogUtil.d(TAG, "Calibration values: stepMax=" + stepMax + 
+                     ", stepMid=" + stepMid + ", stepMin=" + stepMin);
+             
+             // Map pixel values to HID data values
+             calibrationMap.put(stepMax, 100);
+             calibrationMap.put(stepMid, 50);
+             calibrationMap.put(stepMin, 10);
+             
+             calibrationReady = true;
+             return 0; // Success
+         } catch (Exception e) {
+             LogUtil.e(TAG, "Error parsing calibration points", e);
+             return 1; // Failed
+         }
+     }
+ 
+     @Override
+     public Pair<Integer, Float> queryHidSendData(float expectMovePixel) {
+         if (!calibrationReady) {
+             LogUtil.e(TAG, "Calibration not ready");
+             return new Pair<>(0, 0.0f);
+         }
+         
+         // Find the best match for the expected pixel movement
+         float absExpectPixel = Math.abs(expectMovePixel);
+         int sign = expectMovePixel >= 0 ? 1 : -1;
+         
+         // Start with defaults
+         int hidDataValue = 0;
+         float actualPixelValue = 0;
+         
+         // Use the closest step size for the expected movement
+         if (absExpectPixel >= HidIosCalibrationManager.getStepMax() * 0.8f) {
+             // Use max step
+             hidDataValue = 100 * sign;
+             actualPixelValue = HidIosCalibrationManager.getStepMax() * sign;
+         } else if (absExpectPixel >= HidIosCalibrationManager.getStepMid() * 0.8f) {
+             // Use medium step
+             hidDataValue = 50 * sign;
+             actualPixelValue = HidIosCalibrationManager.getStepMid() * sign;
+         } else if (absExpectPixel >= HidIosCalibrationManager.getStepMin() * 0.8f) {
+             // Use min step
+             hidDataValue = 10 * sign;
+             actualPixelValue = HidIosCalibrationManager.getStepMin() * sign;
+         } else {
+             // For very small movements, use the smallest step
+             hidDataValue = 5 * sign;
+             actualPixelValue = (HidIosCalibrationManager.getStepMin() * 0.5f) * sign;
+         }
+         
+         return new Pair<>(hidDataValue, actualPixelValue);
+     }
+ 
+     @Override
+     public void clearCalibrationParams() {
+         calibrationMap.clear();
+         calibrationReady = false;
+         LogUtil.d(TAG, "Calibration parameters cleared");
      }
-     
+ 
+     @Override
+     public boolean isCalibrationReady() {
+         return calibrationReady;
+     }
  }
  
```

---

I've implemented the `RegularCalibrationStrategy` class based on the `CalibrationStrategy` interface and using the `HidIosCalibrationManager` as a reference. Here's what the implementation does:

1. `getCalibrationPoints()`: Provides 4 calibration points with the same X position (0) but different Y positions (0, 40, 80, 100) to help calculate different step sizes.

2. `parseCalibrationPoints()`: Validates the input points and extracts the step sizes (stepMax, stepMid, stepMin) from the differences between points' Y values. It builds a calibration map and returns 0 (success) or 1 (failure).

3. `queryHidSendData()`: Takes an expected pixel movement and determines the appropriate HID data value and the actual pixel value that will result from it, based on the step values from HidIosCalibrationManager.

4. `clearCalibrationParams()`: Clears the calibration map and resets the calibration state.

5. `isCalibrationReady()`: Returns whether the calibration is ready to be used.

The implementation leverages the calibration data from HidIosCalibrationManager for making movement calculations, which ensures consistency with the existing calibration mechanisms in the app.

---


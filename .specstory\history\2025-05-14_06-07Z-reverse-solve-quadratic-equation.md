<!-- Generated by SpecStory -->

# Reverse Solve Quadratic Equation (2025-05-14 06:07Z)

_**User**_

   double predictedY = coeffs.a * inputIndex * inputIndex + coeffs.b * inputIndex + coeffs.c; 已经有了 coeffs.a 的参数，如果根据 predictedY ，反向求解 inputIndex.

---

_**Assistant**_

I need to implement a method to solve for the inputIndex given a predictedY and the quadratic coefficients. This is essentially solving the quadratic equation: a*x² + b*x + c = y for x.

---

```diff
          for (int idx = 0; idx < 100; idx+= 5) {
              QuadraticCoefficients currentCoeffs = (idx <= 50) ? coeffs_le50 : coeffs_gt50;
-             calculateYByQuadraticFunction(yList, yStepsList, idx, currentCoeffs);
+             calculateYByQuadraticFunction(idx, currentCoeffs);
          }
  
```

```diff
  
      public static float calculateYByQuadraticFunction(int inputIndex) {
- 
-     }
-     /**
-      * 新的反控校准逻辑, 根据输入索引点 index，计算对应手机侧屏幕移动量的 y 值。 
-      * @param yList 手机侧屏幕移动量列表
-      * @param yStepsList 预定义的X轴步长列表
-      * @param inputIndex 索引点
-      * @param coeffs 预计算的二次回归系数
-      * @return 手机侧屏幕移动量
-      */
-     private static float calculateYByQuadraticFunction(List<Float> yList, List<Integer> yStepsList, int inputIndex, QuadraticCoefficients coeffs){
- 
-         // 异常处理：如果索引超出范围 (yStepsList is guaranteed non-empty by checkPointV2)
-         if (inputIndex < yStepsList.get(0) || inputIndex > yStepsList.get(yStepsList.size() - 1)) {
-             printLog("calculateYByQuadraticFunction: 无效的 inputIndex: " + inputIndex + ". 必须在 " + yStepsList.get(0) + " 和 " + yStepsList.get(yStepsList.size() - 1) + " 之间。");
-             return 0;
-         }
-         
-         float yValue;
- 
-         if (coeffs != null && coeffs.isValid) {
-             printLog("calculateYByQuadraticFunction: For inputIndex " + inputIndex + ", using calculated coeffs: a=" + coeffs.a + ", b=" + coeffs.b + ", c=" + coeffs.c);
-             double predictedY = coeffs.a * inputIndex * inputIndex + coeffs.b * inputIndex + coeffs.c;
-             yValue = (float) predictedY;
-             printLog("calculateYByQuadraticFunction: 根据 inputIndex " + inputIndex + " 预测的 Y 值: " + yValue);
-         } else {
-             printLog("calculateYByQuadraticFunction: Coefficients invalid or null for inputIndex " + inputIndex + ". Using fallback.");
-             int originalValueIndex = yStepsList.indexOf(inputIndex);
-             if (originalValueIndex != -1) {
-                 yValue = yList.get(originalValueIndex);
-                 printLog("calculateYByQuadraticFunction fallback: Found direct value for " + inputIndex + ": " + yValue);
-             } else {
-                 printLog("calculateYByQuadraticFunction fallback: inputIndex " + inputIndex + " not in yStepsList. Returning 0.");
-                 yValue = 0;
-             }
-         }
-         
-         // 查找并打印inputIndex对应的yList值
-         int indexInYSteps = yStepsList.indexOf(inputIndex);
-         if (indexInYSteps != -1) {
-             float originalValue = yList.get(indexInYSteps);
-             float diff = yValue - originalValue;
-             printLog("index: " + inputIndex + " 原始值: " + originalValue + " 计算值: " + yValue + " 差值: " + diff);
+         QuadraticCoefficients currentCoeffs = (inputIndex <= 50) ? coeffs_le50 : coeffs_gt50;
+         if (currentCoeffs != null && currentCoeffs.isValid) {
+             printLog("calculateYByQuadraticFunction: For inputIndex " + inputIndex + ", using calculated coeffs: a=" + currentCoeffs.a + ", b=" + currentCoeffs.b + ", c=" + currentCoeffs.c);
+             double predictedY = currentCoeffs.a * inputIndex * inputIndex + currentCoeffs.b * inputIndex + currentCoeffs.c;
+             return (float) predictedY;
+         } else {
+             printLog("calculateYByQuadraticFunction: Coefficients invalid or null for inputIndex " + inputIndex + ". Using fallback.");
+             // Try to find the value in the global arrays if available
+             if (yListGlobal.size() > 0 && yStepsListGlobal.size() > 0) {
+                 int originalValueIndex = yStepsListGlobal.indexOf(inputIndex);
+                 if (originalValueIndex != -1) {
+                     return yListGlobal.get(originalValueIndex);
+                 }
+             }
+             printLog("calculateYByQuadraticFunction fallback: No value found. Returning 0.");
+             return 0;
+         }
+     }
+     
+     /**
+      * 求解二次方程：给定 y 值，求解对应的 inputIndex
+      * 解方程：a*x² + b*x + c = y
+      * 
+      * @param targetY 目标 Y 值
+      * @param coeffs 二次方程系数
+      * @return 如果有实数解，返回较小的那个解；如果无解，返回 -1
+      */
+     public static float solveQuadraticForX(float targetY, QuadraticCoefficients coeffs) {
+         if (coeffs == null || !coeffs.isValid) {
+             printLog("solveQuadraticForX: 无效的系数，无法求解方程。");
+             return -1;
+         }
+         
+         // 将方程 a*x² + b*x + c = y 转换为标准形式 a*x² + b*x + (c-y) = 0
+         double a = coeffs.a;
+         double b = coeffs.b;
+         double c = coeffs.c - targetY;
+         
+         // 如果 a 接近于 0，则退化为一次方程 b*x + c = 0
+         if (Math.abs(a) < 1e-9) {
+             if (Math.abs(b) < 1e-9) {
+                 // 如果 b 也接近于 0，且 c 不为 0，则无解
+                 if (Math.abs(c) >= 1e-9) {
+                     printLog("solveQuadraticForX: 方程退化为无解情况。");
+                     return -1;
+                 }
+                 // 如果 c 也接近于 0，则任意值都是解，返回 0 作为默认解
+                 printLog("solveQuadraticForX: 方程退化为恒等式，任意值都是解。");
+                 return 0;
+             }
+             // 解一次方程 b*x + c = 0
+             float result = (float) (-c / b);
+             printLog("solveQuadraticForX: 方程退化为一次方程，解为 " + result);
+             return result;
+         }
+         
+         // 计算判别式 Δ = b² - 4ac
+         double delta = b * b - 4 * a * c;
+         
+         if (delta < 0) {
+             // 判别式小于 0，无实数解
+             printLog("solveQuadraticForX: 方程无实数解。判别式 = " + delta);
+             return -1;
+         }
+         
+         // 计算两个解
+         double sqrtDelta = Math.sqrt(delta);
+         double x1 = (-b + sqrtDelta) / (2 * a);
+         double x2 = (-b - sqrtDelta) / (2 * a);
+         
+         printLog("solveQuadraticForX: 方程有两个解: x1 = " + x1 + ", x2 = " + x2);
+         
+         // 返回较小的那个解，如果两个解都小于0，则返回较大的那个解
+         if (x1 >= 0 && x2 >= 0) {
+             return (float) Math.min(x1, x2);
+         } else if (x1 >= 0) {
+             return (float) x1;
+         } else if (x2 >= 0) {
+             return (float) x2;
          } else {
-             printLog("index: " + inputIndex + " 计算值: " + yValue + " (原始值在yStepsList中未找到进行比较)");
+             printLog("solveQuadraticForX: 两个解都小于 0，返回较大的那个。");
+             return (float) Math.max(x1, x2);
          }
-         return yValue;
      }
  
```

---

I've implemented two key methods:

1. `calculateYByQuadraticFunction(int inputIndex)` - This method uses the appropriate quadratic coefficients based on whether inputIndex is ≤50 or >50 to calculate the Y value.

2. `solveQuadraticForX(float targetY, QuadraticCoefficients coeffs)` - This method solves the reverse problem: given a targetY and coefficients, it finds the corresponding inputIndex by solving the quadratic equation.

The implementation handles several edge cases:
- When the equation degrades to a linear equation (a ≈ 0)
- When there's no solution (discriminant < 0)
- When there are two solutions, returning the more appropriate one

To use this to find an inputIndex for a given Y value, you would call:
```java
float targetY = /* your Y value */;
QuadraticCoefficients coeffs = /* either coeffs_le50 or coeffs_gt50 */;
float inputIndex = solveQuadraticForX(targetY, coeffs);
```

The method will return the appropriate inputIndex, or -1 if no valid solution exists.

---


// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.GridLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ItemNightModeChangerBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final Button buttonAuto;

  @NonNull
  public final Button buttonCustom;

  @NonNull
  public final GridLayout buttonLayout;

  @NonNull
  public final Button buttonNo;

  @NonNull
  public final Button buttonYes;

  @NonNull
  public final TextView title;

  private ItemNightModeChangerBinding(@NonNull FrameLayout rootView, @NonNull Button buttonAuto,
      @NonNull Button buttonCustom, @NonNull GridLayout buttonLayout, @NonNull Button buttonNo,
      @NonNull Button buttonYes, @NonNull TextView title) {
    this.rootView = rootView;
    this.buttonAuto = buttonAuto;
    this.buttonCustom = buttonCustom;
    this.buttonLayout = buttonLayout;
    this.buttonNo = buttonNo;
    this.buttonYes = buttonYes;
    this.title = title;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNightModeChangerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNightModeChangerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_night_mode_changer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNightModeChangerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_auto;
      Button buttonAuto = ViewBindings.findChildViewById(rootView, id);
      if (buttonAuto == null) {
        break missingId;
      }

      id = R.id.button_custom;
      Button buttonCustom = ViewBindings.findChildViewById(rootView, id);
      if (buttonCustom == null) {
        break missingId;
      }

      id = R.id.button_layout;
      GridLayout buttonLayout = ViewBindings.findChildViewById(rootView, id);
      if (buttonLayout == null) {
        break missingId;
      }

      id = R.id.button_no;
      Button buttonNo = ViewBindings.findChildViewById(rootView, id);
      if (buttonNo == null) {
        break missingId;
      }

      id = R.id.button_yes;
      Button buttonYes = ViewBindings.findChildViewById(rootView, id);
      if (buttonYes == null) {
        break missingId;
      }

      id = R.id.title;
      TextView title = ViewBindings.findChildViewById(rootView, id);
      if (title == null) {
        break missingId;
      }

      return new ItemNightModeChangerBinding((FrameLayout) rootView, buttonAuto, buttonCustom,
          buttonLayout, buttonNo, buttonYes, title);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

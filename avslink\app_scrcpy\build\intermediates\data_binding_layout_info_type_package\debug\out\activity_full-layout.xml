<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_full" modulePackage="top.eiyooooo.easycontrol.app" filePath="avslink\app_scrcpy\src\main\res\layout\activity_full.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/activity_full_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="177" endOffset="13"/></Target><Target id="@+id/edit_text" view="EditText"><Expressions/><location startLine="9" startOffset="2" endLine="13" endOffset="45"/></Target><Target id="@+id/texture_view_layout" view="LinearLayout"><Expressions/><location startLine="20" startOffset="4" endLine="26" endOffset="40"/></Target><Target id="@+id/nav_bar" view="LinearLayout"><Expressions/><location startLine="28" startOffset="4" endLine="78" endOffset="18"/></Target><Target id="@+id/button_rotate" view="ImageView"><Expressions/><location startLine="39" startOffset="6" endLine="46" endOffset="32"/></Target><Target id="@+id/button_switch" view="ImageView"><Expressions/><location startLine="48" startOffset="6" endLine="56" endOffset="32"/></Target><Target id="@+id/button_home" view="ImageView"><Expressions/><location startLine="58" startOffset="6" endLine="66" endOffset="32"/></Target><Target id="@+id/button_back" view="ImageView"><Expressions/><location startLine="68" startOffset="6" endLine="76" endOffset="32"/></Target><Target id="@+id/button_more" view="ImageView"><Expressions/><location startLine="81" startOffset="2" endLine="89" endOffset="28"/></Target><Target id="@+id/bar_view" view="GridLayout"><Expressions/><location startLine="91" startOffset="2" endLine="176" endOffset="14"/></Target><Target id="@+id/button_nav_bar" view="ImageView"><Expressions/><location startLine="104" startOffset="4" endLine="111" endOffset="46"/></Target><Target id="@+id/button_mini" view="ImageView"><Expressions/><location startLine="113" startOffset="4" endLine="120" endOffset="46"/></Target><Target id="@+id/button_full_exit" view="ImageView"><Expressions/><location startLine="122" startOffset="4" endLine="129" endOffset="46"/></Target><Target id="@+id/button_close" view="ImageView"><Expressions/><location startLine="131" startOffset="4" endLine="138" endOffset="35"/></Target><Target id="@+id/button_transfer" view="ImageView"><Expressions/><location startLine="140" startOffset="4" endLine="147" endOffset="46"/></Target><Target id="@+id/button_light_off" view="ImageView"><Expressions/><location startLine="149" startOffset="4" endLine="156" endOffset="46"/></Target><Target id="@+id/button_power" view="ImageView"><Expressions/><location startLine="158" startOffset="4" endLine="165" endOffset="46"/></Target><Target id="@+id/button_lock" view="ImageView"><Expressions/><location startLine="167" startOffset="4" endLine="174" endOffset="46"/></Target></Targets></Layout>
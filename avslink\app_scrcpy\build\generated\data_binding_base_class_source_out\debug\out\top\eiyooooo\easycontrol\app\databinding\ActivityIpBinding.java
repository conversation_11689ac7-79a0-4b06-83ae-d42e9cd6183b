// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ActivityIpBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView backButton;

  @NonNull
  public final LinearLayout ipv4;

  @NonNull
  public final LinearLayout ipv6;

  @NonNull
  public final LinearLayout scanned;

  @NonNull
  public final TextView scanning;

  private ActivityIpBinding(@NonNull LinearLayout rootView, @NonNull ImageView backButton,
      @NonNull LinearLayout ipv4, @NonNull LinearLayout ipv6, @NonNull LinearLayout scanned,
      @NonNull TextView scanning) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.ipv4 = ipv4;
    this.ipv6 = ipv6;
    this.scanned = scanned;
    this.scanning = scanning;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityIpBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityIpBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_ip, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityIpBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.back_button;
      ImageView backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.ipv4;
      LinearLayout ipv4 = ViewBindings.findChildViewById(rootView, id);
      if (ipv4 == null) {
        break missingId;
      }

      id = R.id.ipv6;
      LinearLayout ipv6 = ViewBindings.findChildViewById(rootView, id);
      if (ipv6 == null) {
        break missingId;
      }

      id = R.id.scanned;
      LinearLayout scanned = ViewBindings.findChildViewById(rootView, id);
      if (scanned == null) {
        break missingId;
      }

      id = R.id.scanning;
      TextView scanning = ViewBindings.findChildViewById(rootView, id);
      if (scanning == null) {
        break missingId;
      }

      return new ActivityIpBinding((LinearLayout) rootView, backButton, ipv4, ipv6, scanned,
          scanning);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

<!-- Generated by SpecStory -->

# Change logging to android.util.log.e (2025-07-01 10:06Z)

_**User**_

这个页面的打印，都改成 android.util.log.e

---

_**Assistant**_

I'll replace all the logging in HidBtStateReceiver.kt with android.util.Log.e.

---

Cancelled

---

Cancelled

---

Let me try again by reading the file first and then making the edit:

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidBtStateReceiver.kt

---

Now I need to make the edit to replace all remaining LogUtil calls with android.util.Log.e:

---



---

Now I'll find and replace all the remaining LogUtil.d, LogUtil.i, etc. calls with Log.e:

---

<details>
            <summary>Grep search for "LogUtil\." • **9** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L99 | `LogUtil.e(TAG + TAG_IOS_CALIB, "calibration already in progress, ignoring request");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L193 | `LogUtil.i(TAG + TAG_IOS_CALIB, ">>> sending to iOS: " + stateStr + "(" + controlState + ")");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L229 | `LogUtil.d(TAG + TAG_IOS_CALIB, "<<< received from iOS: " +` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L292 | `LogUtil.d(TAG + TAG_IOS_CALIB, "starting prepare calibration");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L352 | `LogUtil.d(TAG + TAG_IOS_CALIB, "starting formal calibration");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L359 | `LogUtil.d(TAG, "startVerification: " + calibrationStrategy.getClass().getSimpleName());` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L384 | `LogUtil.e(TAG, "mouseCalibrationByStrategy point is null");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L438 | `LogUtil.d(TAG + TAG_IOS_CALIB, "<<< received calibration points from iOS: " + obj);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L469 | `LogUtil.d(TAG + TAG_IOS_CALIB, "linear calibration failed, switching to curve strategy");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L481 | `LogUtil.e(TAG, "checkPoint err", e);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L495 | `LogUtil.d(TAG + TAG_IOS_CALIB, ">>> sending END_SUCCESS to iOS");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L542 | `LogUtil.d(TAG + TAG_IOS_CALIB, "final result, notifying callback state=" + state);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L548 | `LogUtil.d(TAG + TAG_IOS_CALIB, "retrying calibration, attempt=" +` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L555 | `LogUtil.d(TAG + TAG_IOS_CALIB, "max retries reached or BT disconnected, final FAIL");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L581 | `LogUtil.d(TAG + TAG_IOS_CALIB, "timeout after 5 seconds, sending FAIL");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L594 | `LogUtil.d(TAG + TAG_IOS_CALIB, "timeout, sending REQ_FAIL, state=HID_VERIFY_TYPE_HID_CONNECT_VERIFICATION_FAIL");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L611 | `LogUtil.d(TAG + TAG_IOS_CALIB, "<<< received END_POINT from iOS (app background)");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_complete_sequence_analysis.md` | L629 | `LogUtil.d(TAG + TAG_IOS_CALIB, "BT HID disconnected");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/device_discovery_architecture.md` | L416 | `LogUtil.e(TAG, "Device already disconnected");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/device_discovery_architecture.md` | L426 | `LogUtil.e(TAG, "Device type mismatch");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/device_discovery_architecture.md` | L439 | `LogUtil.e(TAG, "deinitUsbDevice fail");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/todo/ios_calibration_flow_sequence_issues.md` | L52 | `LogUtil.e(TAG, "LinkHid startVerification   state: HID_VERIFY_TYPE_VERIFICATION_ING");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/todo/ios_calibration_flow_sequence_issues.md` | L78 | `LogUtil.d(TAG, "BT HID not connected, waiting for connection within 5 seconds");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L723 | `LogUtil.i(TAG, "onClick, id = " + id);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L860 | `LogUtil.i(TAG, "onRvcStatusChange(BACKING_UP):" + state);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L878 | `LogUtil.d(TAG, "Send Media State = " + state);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L887 | `LogUtil.d(TAG, "Send App State = " + bShow);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L896 | `LogUtil.d(TAG, "Send Connect State = " + state);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L906 | `LogUtil.d(TAG, "Send Device = " + type + " State = " + state);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L922 | `LogUtil.d(TAG, "Send TrackInfo SingerName = " + singerName);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L923 | `LogUtil.d(TAG, "Send TrackInfo SongName = " + songName);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L924 | `LogUtil.d(TAG, "Send TrackInfo albumName = " + albumName);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L936 | `LogUtil.d(TAG, "Send NaviInfo segInfo = " + segInfo);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L937 | `LogUtil.d(TAG, "Send NaviInfo totalDistance = " + totalDistance);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L948 | `LogUtil.d(TAG, "Send Navi State = " + state);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L960 | `LogUtil.e(TAG, "getPhonelinkInfo key is null ");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L964 | `LogUtil.d(TAG, "getPhonelinkInfo key = " + key);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L1005 | `LogUtil.d(TAG, "Get OP Play ---");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L1009 | `LogUtil.d(TAG, "Get OP Pause ---");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L1013 | `LogUtil.d(TAG, "Get OP Next ---");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L1017 | `LogUtil.d(TAG, "Get OP Previous ---");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L1025 | `LogUtil.d(TAG, "Get Show Req ---");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L1113 | `LogUtil.d(TAG, "show welink -----------");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L1156 | `LogUtil.d(TAG, "KeyCode Down----------: \n" + keyEvent.getKeyCode());` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L1157 | `LogUtil.d(TAG, "KeyCode Down:" + keyEvent.getKeyCode());` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L1161 | `LogUtil.d(TAG, "Long press=>KEYCODE_VOICE_ASSIST!");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L1166 | `LogUtil.d(TAG, "KeyCode Up-------:  \n" + keyEvent.getKeyCode());` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/%E8%BD%A6%E6%9C%BA%E7%AB%AF%E6%96%B0%E4%BA%92%E8%81%94SDK%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3.md` | L1244 | `LogUtil.i(TAG, "PCM_STOP_PLAY type = " + type);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/jni/JniApi.java` | L132 | `LogUtil.e("JNI Test", "End");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/JniInteraction.java` | L25 | `private static final boolean CHECK_JNI_CALLBACK = LogUtil.isFullLog();` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/JniInteraction.java` | L45 | `LogUtil.d(TAG, "audioDataCallback: len=" + len);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/JniInteraction.java` | L51 | `LogUtil.d(TAG, "deviceStatusCallback: status=" + status);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/JniInteraction.java` | L57 | `LogUtil.d(TAG, "huDataCallback: len=" + len + ", type=" + type);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/JniInteraction.java` | L64 | `LogUtil.v(TAG, "imageDataCallback: size=" + size + ", width=" + width + ", type=" + height + ", spspps_data_size=" + spspps_data_size + ", frame_type=" + frame_type);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/JniInteraction.java` | L70 | `LogUtil.d(TAG, "launcherDataCallback: len=" + len);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/JniInteraction.java` | L77 | `LogUtil.d(TAG, "jniMsgCallback: size=" + size + ", type" + type + ", para" + para);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/JniInteraction.java` | L83 | `LogUtil.d(TAG, "jniStatusCallback: status=" + status + ", para1" + para1);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/JniInteraction.java` | L89 | `LogUtil.d(TAG, "jniStatusCallback: len=" + len);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L47 | `LogUtil.v(TAG, "touchResponse:btn = " + btn);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L153 | `LogUtil.v(TAG, "videoX:" + videoX + "videoY:" + videoY + "videow:" + videoWidth + "videoH:" + videoHeight);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L247 | `LogUtil.d(TAG, "sendOnHuAppFront:"+str);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L277 | `//        LogUtil.e("AudioFocus",  "sendAudioFocusLoss = " + str);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L284 | `//        LogUtil.e("AudioFocus",  "sendAudioFocusGain = " + str);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L305 | `LogUtil.i("BTPhone",  "sendBtPhoneState = " + str);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L311 | `LogUtil.i("LocalVR",  "sendHuLocalVrState = " + str);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L317 | `LogUtil.i("ReqAppData",  "sendHuReqAppData = " + str);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L323 | `LogUtil.i("sendHuReqPhoneIsPlayVideo",  "sendHuReqPhoneIsPlayVideo = " + str);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L328 | `LogUtil.i("sendHuReqKeyFrame",  "sendHuReqKeyFrame = " + str);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L50 | `LogUtil.v(TAG, "launcherDataCb:" + msg);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L52 | `LogUtil.v(TAG, "jstr:" + jstr);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L139 | `LogUtil.v(TAG, "Error PCM Mute State");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L147 | `LogUtil.v(TAG, "Error PCM Stop Play State");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L326 | `LogUtil.w(TAG,"First frame type is no sps idr");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L47 | `LogUtil.v(TAG, "setConfig \n" +` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L60 | `LogUtil.e(TAG, "SDK CONFIG DATA ERROR");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L97 | `LogUtil.v(TAG, "setContext");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L101 | `LogUtil.v(TAG, "init");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L104 | `LogUtil.e(TAG, "SDK CONFIG DATA IS NOT SETTED");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L135 | `LogUtil.v(TAG, "deinit");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L141 | `LogUtil.e(TAG, "DEINIT FAILED,SDK IS NOT INIT");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L150 | `LogUtil.v(TAG, "sendButton:btn=" + btn + " para=" + para);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L153 | `LogUtil.e(TAG, "SDK IS NOT INIT");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L157 | `LogUtil.e(TAG, "BUTTON IS NOT EXIST");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L170 | `LogUtil.v(TAG, "sendPoint:x=" + x + " y=" + y + " type=" + type);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L173 | `LogUtil.e(TAG, "SDK IS NOT INIT");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L177 | `LogUtil.e(TAG, "POINT LOCATION OR TYPE ERROR");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L199 | `LogUtil.v(TAG, "sendPointSync");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L201 | `LogUtil.e(TAG, "SDK IS NOT INIT");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L213 | `LogUtil.i(TAG, "sendHardKey, key = " + key);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L255 | `//        LogUtil.i(TAG,"sendAudioStateHardKey, key = "+key);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L265 | `//        LogUtil.i(TAG,"sendPModeHardKey, key = "+key);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L275 | `//        LogUtil.i(TAG,"sendVehicleInfo, vehicleBrand = "+vehicleBrand.toString()+", vehicleModel = "+vehicleModel.toString());` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L285 | `LogUtil.i(TAG, "sendHuMicState, state = " + state);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L296 | `LogUtil.i(TAG, "sendHuRvcState, rvcState = " + rvcState);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L306 | `LogUtil.i(TAG,"sendControlEnAndCh, controlState = "+controlState);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L316 | `//        LogUtil.i(TAG,"sendResBtFocusState, btFocusState = "+btFocusState);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L327 | `LogUtil.i(TAG,"sendHuKeyState, keyState = "+keyState);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L337 | `LogUtil.i(TAG, "sendBTMacInfo, btMacInfo = " + btMacInfo);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L347 | `LogUtil.v(TAG, "sendMsg:msg =" + msg);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L349 | `LogUtil.e(TAG, "SDK IS NOT INIT");` |

</details>

---

I need to replace any LogUtil calls in the HidBtStateReceiver.kt file:

---

<details>
            <summary>Grep search for "LogUtil\." • **0** files</summary>
        
No results found
</details>

---

It seems all LogUtil calls in the file have already been changed to Log.e calls. Let me verify the file again to make sure:

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidBtStateReceiver.kt

---

Great! The changes have been applied successfully. All LogUtil references were removed from the import statements, and all logging calls in the file now use android.util.Log.e instead. I'll check the next part of the file to confirm:

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidBtStateReceiver.kt

---

Perfect! All the logging in HidBtStateReceiver.kt has been successfully changed to use android.util.Log.e. The changes include:

1. Commented out the import of LogUtil with `// import com.autoai.common.util.LogUtil`
2. All logging statements now use `Log.e(TAG, ...)` instead of the previous LogUtil calls

All instances of logging in the file are now using android.util.Log.e as requested.

---


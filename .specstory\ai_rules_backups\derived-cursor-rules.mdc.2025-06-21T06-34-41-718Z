
## HEADERS

## TECH STACK

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

A new document file with the function name has been requested to be created in the docs directory.

## CODING STANDARDS

## DEBUGGING

## WORKFLOW & RELEASE RULES

### Bluetooth Icon and Calibration Button Logic

#### Display Logic Requirements

##### 1. Unconnected State
- **Requirement**: If not connected (`LinkSdkModel.INSTANCE.isConnected()` returns false), do not display the Bluetooth icon.
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Trigger Condition**: Check Bluetooth status and find that it is not connected.
  - **Detailed Trigger Condition**:
    - `LinkSdkModel.INSTANCE.isConnected()` returns false
    - Regardless of the device platform (iOS, Android, or HarmonyOS), the Bluetooth icon will not be displayed.

##### 2. Normal State (NORMAL)
- **Requirement**: When Bluetooth is normally connected and there are no abnormalities, do not display the Bluetooth icon.
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Trigger Conditions**:
  - Bluetooth pairing is successful.
  - iOS device calibration is successful.
  - Bluetooth device matches correctly.
  - **Detailed Trigger Conditions**:
    - **iOS Platform**: Bluetooth is paired and connected successfully, and the calibration status is `CalibrationStatus.SUCCESS`.
    - **Android Platform**: Bluetooth is paired and connected successfully, and it matches the Link device (confirmed via `SdkViewModel.getInstance().updateBTPairedWithPhone()`).
    - **HarmonyOS Platform**: Bluetooth is paired and connected successfully, and the Bluetooth device name matches `blueToothName`.

##### 3. Bluetooth Not Connected (BT_NOT_CONNECTED)
- **Requirement**: Display the Bluetooth not connected icon and prompt the user.
- **UI Actions**:
  - Display Bluetooth icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set Bluetooth icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - Display Toast prompt: "Please connect to Bluetooth"
- **Trigger Condition**: No connected Bluetooth device is detected.
  - **Detailed Trigger Conditions**:
    - **All Platforms**: `HidUtil.getConnectedBluetoothDevices()` returns an empty collection.
    - **iOS Platform (Specific Case)**: This state is also triggered if the calibration status is `CalibrationStatus.BT_NOT_CONNECTED`.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 4. Bluetooth Disconnected (BT_DISCONNECTED)
- **Requirement**: Display the Bluetooth disconnected icon and prompt the user.
- **UI Actions**:
  - Display Bluetooth icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set Bluetooth icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - If already connected, display Toast prompt: "Bluetooth connection has been disconnected"
- **Trigger Condition**: Bluetooth connection status changes from connected to disconnected.
  - **Detailed Trigger Conditions**:
    - **All Platforms**: Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`.
    - **iOS Platform**: Bluetooth is disconnected, but interconnection still exists.
    - **Android Platform**: Bluetooth is disconnected, but interconnection still exists.
    - **HarmonyOS Platform**: Bluetooth is disconnected, but interconnection still exists, triggered via the `onDisconnected` callback.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 5. Bluetooth Device Mismatched (BT_DEVICE_MISMATCHED)
- **Requirement**: Display the Bluetooth icon and prompt the user that the wrong device is connected.
- **UI Actions**:
  - Display Bluetooth icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set Bluetooth icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - Display Toast prompt: "May cause abnormal remote control phone sound"
- **Trigger Conditions**:
  - Mainly for HarmonyOS systems.
  - Detect that the Bluetooth device name does not match the expected name.
  - **Detailed Trigger Conditions**:
    - **HarmonyOS Platform**: Bluetooth is connected, but the device name does not match `blueToothName`.
    - **Detection Logic**: Iterate through the device collection returned by `HidUtil.getConnectedBluetoothDevices()` and check if the device name matches `blueToothName`.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 6. iOS Needs Calibration (IOS_NEEDS_CALIBRATION)
- **Requirement**: Only display the calibration icon on iOS devices.
- **UI Actions**:
  - Display calibration icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set calibration icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.calibration_icon)`
- **Trigger Conditions**:
  - Device type is iOS.
  - Calibration status is failed, APP is not in the foreground, or Bluetooth is not connected.
  - **Detailed Trigger Conditions**:
    - **iOS Platform Only**: `isIOSDevice()` returns true.
    - Calibration status is one of the following:
      - `CalibrationStatus.FAILED`: Calibration failed.
      - `CalibrationStatus.APP_NOT_FOREGROUND`: The mobile app is not in the foreground.
      - `CalibrationStatus.BT_NOT_CONNECTED`: Bluetooth is not connected.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 7. iOS Calibration Failed
- **Requirement**: Display the calibration failed interface and provide a re-calibration option.
- **UI Actions**:
  - Display calibration icon.
  - Display calibration failed interface: `popCalibrationLin.setVisibility(View.VISIBLE)`
  - Display failure icon: `popCalibrationImg.setImageResource(R.mipmap.calibration_failed_img)`
  - Display failure title: `popCalibrationTitle.setText("Calibration failed")`
  - Provide a 5-second countdown re-calibration button.
- **Trigger Condition**: Receive calibration failed callback (`CalibrationStatus.FAILED`).
  - **Detailed Trigger Conditions**:
    - **iOS Platform Only**: `isIOSDevice()` returns true.
    - Calibration result of `1` (corresponding to `CalibrationStatus.FAILED`) is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 8. iOS APP Not in Foreground
- **Requirement**: Display the calibration icon and prompt the user to open the phone APP.
- **UI Actions**:
  - Display calibration icon.
  - Display Toast prompt: "Please open the WeLink app on your phone"
- **Trigger Condition**: Receive APP not in foreground callback (`CalibrationStatus.APP_NOT_FOREGROUND`).

#### Button Click Behavior

##### 9. Click Bluetooth Not Connected Icon
- **Requirement**: Prompt the user to connect to Bluetooth.
- **UI Action**: Display Toast prompt: "Please connect to Bluetooth"
- **Trigger Condition**: Click the icon when the current state is `BT_NOT_CONNECTED`.

##### 10. Click Bluetooth Disconnected Icon
- **Requirement**: Prompt the user that Bluetooth is disconnected.
- **UI Action**: Display Toast prompt: "Bluetooth connection has been disconnected"
- **Trigger Condition**: Click the icon when the current state is `BT_DISCONNECTED`.

##### 11. Click Bluetooth Device Mismatched Icon
- **Requirement**: Prompt the user to switch Bluetooth.
- **UI Action**: Display Toast prompt: "May cause abnormal remote control phone sound"
- **Trigger Condition**: Click the icon when the current state is `BT_DEVICE_MISMATCHED`.

##### 12. Click iOS Calibration Icon
- **Requirement**: Hide the button and start calibration.
- **UI Actions**:
  - Hide the calibration button: `mBlueToothOrCalibration.setVisibility(View.INVISIBLE)`
  - Start calibration process

- **Trigger Condition**: Click the icon when the current state is `IOS_NEEDS_CALIBRATION`.

#### Display Logic Requirements - Detailed

##### 1. Unconnected State
- **Requirement**: If not connected, do not display the Bluetooth icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Condition**:
  - `LinkSdkModel.INSTANCE.isConnected()` returns false
  - Regardless of the device platform (iOS, Android, or HarmonyOS), the Bluetooth icon will not be displayed

##### 2. Normal State (NORMAL)
- **Requirement**: Bluetooth normally connected and no abnormalities, do not display the Bluetooth icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - **iOS Platform**: Bluetooth is paired and connected successfully, and the calibration status is `CalibrationStatus.SUCCESS`
  - **Android Platform**: Bluetooth is paired and connected successfully, and it matches the Link device (confirmed via `SdkViewModel.getInstance().updateBTPairedWithPhone()`)
  - **HarmonyOS Platform**: Bluetooth is paired and connected successfully, and the Bluetooth device name matches `blueToothName`

##### 3. Bluetooth Not Connected (BT_NOT_CONNECTED)
- **Requirement**: Display the Bluetooth not connected icon and prompt the user
- **UI Action**: Display Bluetooth icon and prompt the user to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - **All Platforms**: `HidUtil.getConnectedBluetoothDevices()` returns an empty collection
  - **iOS Platform特例**: If the calibration status is `CalibrationStatus.BT_NOT_CONNECTED`, this state is also triggered
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 4. Bluetooth Disconnected (BT_DISCONNECTED)
- **Requirement**: Display the Bluetooth disconnected icon and prompt the user
- **UI Action**: Display Bluetooth icon and prompt the user that the Bluetooth connection has been disconnected
- **Detailed Trigger Conditions**:
  - **All Platforms**: Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`
  - **iOS Platform**: Bluetooth is disconnected but interconnection still exists
  - **Android Platform**: Bluetooth is disconnected but interconnection still exists
  - **HarmonyOS Platform**: Bluetooth is disconnected but interconnection still exists, triggered via the `onDisconnected` callback
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 5. Bluetooth Device Mismatched (BT_DEVICE_MISMATCHED)
- **Requirement**: Display the Bluetooth icon and prompt the user that the wrong device is connected
- **UI Action**: Display Bluetooth icon and prompt the user that it may cause abnormal remote control phone sound
- **Detailed Trigger Conditions**:
  - **HarmonyOS Platform**: Bluetooth is connected, but the device name does not match `blueToothName`
  - Detection logic: Iterate through the device collection returned by `HidUtil.getConnectedBluetoothDevices()` and check if the device name matches `blueToothName`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 6. iOS Needs Calibration (IOS_NEEDS_CALIBRATION)
- **Requirement**: Only display the calibration icon on iOS devices
- **UI Action**: Display calibration icon
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: `isIOSDevice()` returns true
  - Calibration status is one of the following:
    - `CalibrationStatus.FAILED`: Calibration failed
    - `CalibrationStatus.APP_NOT_FOREGROUND`: The mobile APP is not in the foreground
    - `CalibrationStatus.BT_NOT_CONNECTED`: Bluetooth is not connected
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 7. iOS Calibration Failed
- **Requirement**: Display the calibration failed interface and provide a re-calibration option
- **UI Action**: Display calibration icon and calibration failed interface
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: `isIOSDevice()` returns true
  - Through `LinkSdkModel.INSTANCE.registerPopHidCheckCallback` receive calibration result as `1` (corresponding to `CalibrationStatus.FAILED`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 8. iOS APP Not in Foreground
- **Requirement**: Display the calibration icon and prompt the user to open the phone APP
- **UI Action**: Display calibration icon and Toast prompt
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: `isIOSDevice()` returns true
  - Through `LinkSdkModel.INSTANCE.registerPopHidCheckCallback` receive calibration result as `3` (corresponding to `CalibrationStatus.APP_NOT_FOREGROUND`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

#### Button Click Behavior - Detailed

##### 9. Click Bluetooth Not Connected Icon
- **Requirement**: Prompt the user to connect to Bluetooth
- **UI Action**: Display Toast prompt: "Please connect to Bluetooth"
- **Detailed Trigger Conditions**:
  - **All Platforms**: Current state is `BT_NOT_CONNECTED`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

##### 10. Click Bluetooth Disconnected Icon
- **Requirement**: Prompt the user that Bluetooth is disconnected
- **UI Action**: Display Toast prompt: "Bluetooth connection has been disconnected"
- **Detailed Trigger Conditions**:
  - **All Platforms**: Current state is `BT_DISCONNECTED`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

##### 11. Click Bluetooth Device Mismatched Icon
- **Requirement**: Prompt the user to switch Bluetooth
- **UI Action**: Display Toast prompt: "May cause abnormal remote control phone sound"
- **Detailed Trigger Conditions**:
  - **Mainly for HarmonyOS Platform**: Current state is `BT_DEVICE_MISMATCHED`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

##### 12. Click iOS Calibration Icon
- **Requirement**: Hide the button and start calibration
- **UI Action**: Hide the calibration button and begin calibration
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: Current state is `IOS_NEEDS_CALIBRATION`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

#### Platform-Specific Combined Conditions

##### 13. iOS Platform - Normal State
- **Requirement**: Do not display any icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - `platform.contains("ios")` is true
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Calibration status is `CalibrationStatus.SUCCESS` (calibration result `0` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 14. iOS Platform - Needs Calibration
- **Requirement**: Display calibration icon
- **UI Action**: Display calibration icon
- **Detailed Trigger Conditions**:
  - `platform.contains("ios")` is true
  - Bluetooth is paired and connected successfully
  - Calibration status is one of the following:
    - `CalibrationStatus.FAILED` (calibration result `1` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
    - `CalibrationStatus.APP_NOT_FOREGROUND` (calibration result `3` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
    - `CalibrationStatus.BT_NOT_CONNECTED` (calibration result `2` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 15. iOS Platform - Bluetooth Not Connected
- **Requirement**: Display the Bluetooth not connected icon
- **UI Action**: Display Bluetooth icon and prompt to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - `platform.contains("ios")` is true
  - `HidUtil.getConnectedBluetoothDevices()` returns an empty collection
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 16. Android Platform - Normal State
- **Requirement**: Do not display any icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - `platform.contains("android")` is true
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Bluetooth matching with Link device is confirmed via `SdkViewModel.getInstance().updateBTPairedWithPhone()`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 17. Android Platform - Bluetooth Not Connected or Disconnected
- **Requirement**: Display the Bluetooth not connected/disconnected icon
- **UI Action**: Display Bluetooth icon and prompt to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - `platform.contains("android")` is true
  - `HidUtil.getConnectedBluetoothDevices()` returns an empty collection or Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 18. HarmonyOS Platform - Normal State
- **Requirement**: Do not display any icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - `!isIOSDevice() && !isAndroidDevice()` is true (HarmonyOS System)
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Iterating through the connected Bluetooth devices, there exists a device name matching `blueToothName`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 19. HarmonyOS Platform - Bluetooth Device Mismatched
- **Requirement**: Display the Bluetooth device mismatched icon
- **UI Action**: Display Bluetooth icon and prompt that it may cause abnormal remote control
- **Detailed Trigger Conditions**:
  - `!isIOSDevice() && !isAndroidDevice()` is true (HarmonyOS System)
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Iterating through the connected Bluetooth devices, no device name matches `blueToothName`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 20. HarmonyOS Platform - Bluetooth Not Connected or Disconnected
- **Requirement**: Display the Bluetooth not connected/disconnected icon
- **UI Action**: Display Bluetooth icon and prompt to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - `!isIOSDevice() && !isAndroidDevice()` is true (HarmonyOS System)
  - `HidUtil.getConnectedBluetoothDevices()` returns an empty collection or Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)
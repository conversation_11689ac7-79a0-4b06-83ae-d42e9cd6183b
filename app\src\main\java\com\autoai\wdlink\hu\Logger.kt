package com.autoai.wdlink.hu

import android.content.Context
import com.autoai.link.baselog.WeLinkLog

/**
 * 日志工具类
 *
 * <AUTHOR> */
object Logger {
    private val LOG: WeLinkLog = object : WeLinkLog() {
        override fun configTag(): String {
            return "A55日志"
        }

        override fun configLogName(): String {
            return Logger::class.java.simpleName + ".java"
        }
    }

    fun isIsLoggable(): Boolean {
        return LOG.isFileLoggable
    }

    fun setIsLoggable(isLoggable: Boolean) {
        LOG.isIsLoggable = isLoggable
    }

    fun isIsFileLoggable(): Boolean {
        return LOG.isFileLoggable
    }

    fun setIsFileLoggable(isFileLoggable: Boolean) {
        LOG.isFileLoggable = isFileLoggable
    }

    fun v(msg: String?) {
        LOG.v("#${Thread.currentThread().name}: $msg")
    }

    fun v(msg: String?, t: Throwable?) {
        LOG.v("#${Thread.currentThread().name}: $msg", t)
    }

    fun d(msg: String?) {
        LOG.d("#${Thread.currentThread().name}: $msg")
    }

    fun d(msg: String?, t: Throwable?) {
        LOG.d("#${Thread.currentThread().name}: $msg", t)
    }

    fun i(msg: String?) {
        LOG.i("#${Thread.currentThread().name}: $msg")
    }

    fun i(msg: String?, t: Throwable?) {
        LOG.i("#${Thread.currentThread().name}: $msg", t)
    }

    fun w(msg: String?) {
        LOG.w("#${Thread.currentThread().name}: $msg")
    }

    fun w(msg: String?, t: Throwable?) {
        LOG.w("#${Thread.currentThread().name}: $msg", t)
    }

    fun e(msg: String?) {
        LOG.e("#${Thread.currentThread().name}: $msg")
    }

    fun e(msg: String?, t: Throwable?) {
        LOG.e("#${Thread.currentThread().name}: $msg", t)
    }

    fun printStackTrace(msg: String?) {
        LOG.printStackTrace(msg)
    }

    fun registerUncaughtExceptionHandler(context: Context?) {
        LOG.registerUncaughtExceptionHandler(context, null)
    }


}
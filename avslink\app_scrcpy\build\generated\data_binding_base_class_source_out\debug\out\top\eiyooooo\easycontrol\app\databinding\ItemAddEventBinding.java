// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.GridLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ItemAddEventBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final Button buttonChangeToMini;

  @NonNull
  public final Button buttonChangeToSmall;

  @NonNull
  public final GridLayout buttonLayout;

  @NonNull
  public final TextView title;

  private ItemAddEventBinding(@NonNull FrameLayout rootView, @NonNull Button buttonChangeToMini,
      @NonNull Button buttonChangeToSmall, @NonNull GridLayout buttonLayout,
      @NonNull TextView title) {
    this.rootView = rootView;
    this.buttonChangeToMini = buttonChangeToMini;
    this.buttonChangeToSmall = buttonChangeToSmall;
    this.buttonLayout = buttonLayout;
    this.title = title;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAddEventBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAddEventBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_add_event, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAddEventBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_change_to_mini;
      Button buttonChangeToMini = ViewBindings.findChildViewById(rootView, id);
      if (buttonChangeToMini == null) {
        break missingId;
      }

      id = R.id.button_change_to_small;
      Button buttonChangeToSmall = ViewBindings.findChildViewById(rootView, id);
      if (buttonChangeToSmall == null) {
        break missingId;
      }

      id = R.id.button_layout;
      GridLayout buttonLayout = ViewBindings.findChildViewById(rootView, id);
      if (buttonLayout == null) {
        break missingId;
      }

      id = R.id.title;
      TextView title = ViewBindings.findChildViewById(rootView, id);
      if (title == null) {
        break missingId;
      }

      return new ItemAddEventBinding((FrameLayout) rootView, buttonChangeToMini,
          buttonChangeToSmall, buttonLayout, title);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

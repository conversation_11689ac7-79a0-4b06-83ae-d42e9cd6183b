// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.GridLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ActivityFullBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final GridLayout barView;

  @NonNull
  public final ImageView buttonBack;

  @NonNull
  public final ImageView buttonClose;

  @NonNull
  public final ImageView buttonFullExit;

  @NonNull
  public final ImageView buttonHome;

  @NonNull
  public final ImageView buttonLightOff;

  @NonNull
  public final ImageView buttonLock;

  @NonNull
  public final ImageView buttonMini;

  @NonNull
  public final ImageView buttonMore;

  @NonNull
  public final ImageView buttonNavBar;

  @NonNull
  public final ImageView buttonPower;

  @NonNull
  public final ImageView buttonRotate;

  @NonNull
  public final ImageView buttonSwitch;

  @NonNull
  public final ImageView buttonTransfer;

  @NonNull
  public final EditText editText;

  @NonNull
  public final LinearLayout navBar;

  @NonNull
  public final LinearLayout textureViewLayout;

  private ActivityFullBinding(@NonNull FrameLayout rootView, @NonNull GridLayout barView,
      @NonNull ImageView buttonBack, @NonNull ImageView buttonClose,
      @NonNull ImageView buttonFullExit, @NonNull ImageView buttonHome,
      @NonNull ImageView buttonLightOff, @NonNull ImageView buttonLock,
      @NonNull ImageView buttonMini, @NonNull ImageView buttonMore, @NonNull ImageView buttonNavBar,
      @NonNull ImageView buttonPower, @NonNull ImageView buttonRotate,
      @NonNull ImageView buttonSwitch, @NonNull ImageView buttonTransfer,
      @NonNull EditText editText, @NonNull LinearLayout navBar,
      @NonNull LinearLayout textureViewLayout) {
    this.rootView = rootView;
    this.barView = barView;
    this.buttonBack = buttonBack;
    this.buttonClose = buttonClose;
    this.buttonFullExit = buttonFullExit;
    this.buttonHome = buttonHome;
    this.buttonLightOff = buttonLightOff;
    this.buttonLock = buttonLock;
    this.buttonMini = buttonMini;
    this.buttonMore = buttonMore;
    this.buttonNavBar = buttonNavBar;
    this.buttonPower = buttonPower;
    this.buttonRotate = buttonRotate;
    this.buttonSwitch = buttonSwitch;
    this.buttonTransfer = buttonTransfer;
    this.editText = editText;
    this.navBar = navBar;
    this.textureViewLayout = textureViewLayout;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityFullBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityFullBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_full, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityFullBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bar_view;
      GridLayout barView = ViewBindings.findChildViewById(rootView, id);
      if (barView == null) {
        break missingId;
      }

      id = R.id.button_back;
      ImageView buttonBack = ViewBindings.findChildViewById(rootView, id);
      if (buttonBack == null) {
        break missingId;
      }

      id = R.id.button_close;
      ImageView buttonClose = ViewBindings.findChildViewById(rootView, id);
      if (buttonClose == null) {
        break missingId;
      }

      id = R.id.button_full_exit;
      ImageView buttonFullExit = ViewBindings.findChildViewById(rootView, id);
      if (buttonFullExit == null) {
        break missingId;
      }

      id = R.id.button_home;
      ImageView buttonHome = ViewBindings.findChildViewById(rootView, id);
      if (buttonHome == null) {
        break missingId;
      }

      id = R.id.button_light_off;
      ImageView buttonLightOff = ViewBindings.findChildViewById(rootView, id);
      if (buttonLightOff == null) {
        break missingId;
      }

      id = R.id.button_lock;
      ImageView buttonLock = ViewBindings.findChildViewById(rootView, id);
      if (buttonLock == null) {
        break missingId;
      }

      id = R.id.button_mini;
      ImageView buttonMini = ViewBindings.findChildViewById(rootView, id);
      if (buttonMini == null) {
        break missingId;
      }

      id = R.id.button_more;
      ImageView buttonMore = ViewBindings.findChildViewById(rootView, id);
      if (buttonMore == null) {
        break missingId;
      }

      id = R.id.button_nav_bar;
      ImageView buttonNavBar = ViewBindings.findChildViewById(rootView, id);
      if (buttonNavBar == null) {
        break missingId;
      }

      id = R.id.button_power;
      ImageView buttonPower = ViewBindings.findChildViewById(rootView, id);
      if (buttonPower == null) {
        break missingId;
      }

      id = R.id.button_rotate;
      ImageView buttonRotate = ViewBindings.findChildViewById(rootView, id);
      if (buttonRotate == null) {
        break missingId;
      }

      id = R.id.button_switch;
      ImageView buttonSwitch = ViewBindings.findChildViewById(rootView, id);
      if (buttonSwitch == null) {
        break missingId;
      }

      id = R.id.button_transfer;
      ImageView buttonTransfer = ViewBindings.findChildViewById(rootView, id);
      if (buttonTransfer == null) {
        break missingId;
      }

      id = R.id.edit_text;
      EditText editText = ViewBindings.findChildViewById(rootView, id);
      if (editText == null) {
        break missingId;
      }

      id = R.id.nav_bar;
      LinearLayout navBar = ViewBindings.findChildViewById(rootView, id);
      if (navBar == null) {
        break missingId;
      }

      id = R.id.texture_view_layout;
      LinearLayout textureViewLayout = ViewBindings.findChildViewById(rootView, id);
      if (textureViewLayout == null) {
        break missingId;
      }

      return new ActivityFullBinding((FrameLayout) rootView, barView, buttonBack, buttonClose,
          buttonFullExit, buttonHome, buttonLightOff, buttonLock, buttonMini, buttonMore,
          buttonNavBar, buttonPower, buttonRotate, buttonSwitch, buttonTransfer, editText, navBar,
          textureViewLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

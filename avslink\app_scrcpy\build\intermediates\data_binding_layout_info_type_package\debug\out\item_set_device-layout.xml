<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_set_device" modulePackage="top.eiyooooo.easycontrol.app" filePath="avslink\app_scrcpy\src\main\res\layout\item_set_device.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_set_device_0" view="LinearLayout"><Expressions/><location startLine="2" startOffset="0" endLine="107" endOffset="14"/></Target><Target id="@+id/button_layout" view="GridLayout"><Expressions/><location startLine="19" startOffset="2" endLine="77" endOffset="14"/></Target><Target id="@+id/button_change" view="Button"><Expressions/><location startLine="29" startOffset="4" endLine="37" endOffset="43"/></Target><Target id="@+id/button_delete" view="Button"><Expressions/><location startLine="41" startOffset="4" endLine="49" endOffset="43"/></Target><Target id="@+id/button_night_mode" view="Button"><Expressions/><location startLine="55" startOffset="4" endLine="63" endOffset="43"/></Target><Target id="@+id/button_get_uuid" view="Button"><Expressions/><location startLine="67" startOffset="4" endLine="75" endOffset="43"/></Target><Target id="@+id/button_create_shortcut" view="Button"><Expressions/><location startLine="79" startOffset="2" endLine="91" endOffset="40"/></Target><Target id="@+id/button_start_wireless" view="Button"><Expressions/><location startLine="93" startOffset="2" endLine="105" endOffset="40"/></Target></Targets></Layout>
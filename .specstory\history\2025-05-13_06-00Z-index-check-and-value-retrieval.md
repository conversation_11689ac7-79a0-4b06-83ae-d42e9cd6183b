<!-- Generated by SpecStory -->

# Index Check and Value Retrieval (2025-05-13 06:00Z)

_**User**_

index 判断是否在 yStepsList 中，如果在其中，根据索引，从 yLList 找到该值，然后直接返回。

---

_**Assistant**_

Looking at the code, I can see that the current implementation checks if the index is less than `yList.size()`, but it doesn't check if the index is actually in the `yStepsList`. Let me fix this by modifying the condition in the `getYByX` method.

---

```diff
  
          // 异常处理：如果索引超出范围或yList为空，返回0
-         if (index < 0 || index > ySteps.get(0) || yList.isEmpty()) {
+         if (index < 0 || index > ySteps[0] || yList.isEmpty()) {
              printLog("Invalid index or empty yList");
              return 0;
          }
          
-         // 如果索引恰好在已知点上，直接返回对应值
-         if (index < yList.size()) {
-             return yList.get(index);
+         // 如果索引恰好在yStepsList中，直接返回对应值
+         if (yStepsList.contains(index) && yStepsList.indexOf(index) < yList.size()) {
+             return yList.get(yStepsList.indexOf(index));
          }
          
```

---

Now the code checks if the index is actually contained in the `yStepsList` using `yStepsList.contains(index)`. If it is, it finds the position of that index in the `yStepsList` using `yStepsList.indexOf(index)` and returns the corresponding value from `yList` at that position.

This ensures that we're only returning a direct value when the index is actually one of the predefined step values in `yStepsList`, rather than just checking if the index is within range of the `yList` size.

---


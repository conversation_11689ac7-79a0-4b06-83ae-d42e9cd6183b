<!-- Generated by SpecStory -->

# Untitled (2025-06-24 11:37Z)

_**User**_

发现 ON_HID_APP_NOT_FOREGROUND 这种状态下， 不出现PopwinService校准图标。 理论上无法校准，应该有校准图标，请分析问什么，然后告诉我解决方案，等待确认。 慢慢来，一步一步分析。

---

_**Assistant**_

I'll analyze the issue with ON_HID_APP_NOT_FOREGROUND state not showing the calibration icon in PopwinService. Let's go through this step by step.

First, let me examine the code to understand the iOS calibration flow and how it handles different states:

---

<details>
            <summary>Grep search for "PopwinService.*calibration" • **7** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/todo/bluetooth_calibration_issues.md` | L2 | `基于对 PopWinService.java 代码和 `docs/bluetooth_calibration_icon_display_logic.md` 文档的对比分析，发现以下需要修复的问题：` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L562 | `- `PopWinService.CalibrationMonitoringCallback()`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L684 | `LogUtil.d(TAG_BT + TAG_IOS_CALIB, "PopWinService:handleBluetoothButtonClick user triggered iOS calibration");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2103 | `LogUtil.d(TAG_BT + TAG_IOS_CALIB, "PopWinService:CalibrationMonitoringCallback <<< received calibration result=" + isCheck + ", status=" + calibrationStatus);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L77 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2770 | `(calibrationStatus == CalibrationStatus.FAILED ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L78 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2771 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L79 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2772 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED);` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L80 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2770 | `(calibrationStatus == CalibrationStatus.FAILED ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L81 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2771 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L82 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2772 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED);` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L192 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2778 | `return calibrationStatus == CalibrationStatus.FAILED ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L197 | `⟪ 121 characters skipped ⟫77 | `⟪ 112 characters skipped ⟫e/utils/PopWinService.java` | L2770 | `(calibrationStatus == CalibrationStatus.FAILED ||` |` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L198 | `⟪ 121 characters skipped ⟫80 | `⟪ 112 characters skipped ⟫e/utils/PopWinService.java` | L2770 | `(calibrationStatus == CalibrationStatus.FAILED ||` |` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L203 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2778 | `return calibrationStatus == CalibrationStatus.FAILED ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L254 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2778 | `return calibrationStatus == CalibrationStatus.FAILED ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L255 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2779 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L256 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2780 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED;` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L257 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2778 | `return calibrationStatus == CalibrationStatus.FAILED ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L258 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2779 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L259 | `| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2780 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED;` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L701 | `+                 LogUtil.d(TAG_BT, "PopWinService:handleBluetoothButtonClick IOS_NEEDS_CALIBRATION");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L735 | `+                 LogUtil.d(TAG_BT, "PopWinService:onCarBtPairedConnected iOS device status=" + calibrationStatus);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L741 | `+                     LogUtil.d(TAG_BT, "PopWinService:onCarBtPairedConnected iOS needs calibration");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L746 | `+                     LogUtil.d(TAG_BT, "PopWinService:onCarBtPairedConnected iOS no calibration needed");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L776 | `+         LogUtil.d(TAG_BT, "PopWinService:CalibrationMonitoringCallback");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L780 | `+             LogUtil.d(TAG_BT, "PopWinService:PopHidCheckCallback status=" + calibrationStatus + " value=" + isCheck);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L787 | `+                 LogUtil.d(TAG_BT, "PopWinService:PopHidCheckCallback showing calibration UI");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L857 | `- `PopWinService:CalibrationMonitoringCallback`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-24-%E6%A3%80%E6%9F%A5%E6%96%87%E6%A1%A3%E4%B8%8E%E4%BB%A3%E7%A0%81%E7%9A%84%E4%B8%80%E8%87%B4%E6%80%A7.md` | L500 | `| PopWinService.handleBluetoothButtonClick() | 通过 `case IOS_NEEDS_CALIBRATION:` 分支处理 | 完全符合 |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-24-%E6%A3%80%E6%9F%A5%E6%96%87%E6%A1%A3%E4%B8%8E%E4%BB%A3%E7%A0%81%E7%9A%84%E4%B8%80%E8%87%B4%E6%80%A7.md` | L600 | `| PopWinService 收到校准结果回调 | 通过 `CalibrationMonitoringCallback` 实现 | 完全符合 |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L562 | `- `PopWinService.CalibrationMonitoringCallback()`` |

</details>

---

Let me check the documentation that describes the calibration icon display logic:

---

Read file: docs/bluetooth_calibration_icon_display_logic.md

---

Now, let me check the file that documents the iOS calibration flow:

---

Read file: docs/ios_calibration_flow_sequence.md

---

Now, let me examine what happens specifically when we have the ON_HID_APP_NOT_FOREGROUND status:

---

<details>
            <summary>Grep search for "ON_HID_APP_NOT_FOREGROUND|APP_NOT_FOREGROUND" • **17** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/bluetooth_calibration_icon_display_logic.md` | L74 | `- `CalibrationStatus.APP_NOT_FOREGROUND`: 手机APP未在前台` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/bluetooth_calibration_icon_display_logic.md` | L99 | `- 通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`3`(对应`CalibrationStatus.APP_NOT_FOREGROUND`)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/bluetooth_calibration_icon_display_logic.md` | L157 | `- `CalibrationStatus.APP_NOT_FOREGROUND`(通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`3`)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/bluetooth_calibration_icon_display_logic.md` | L232 | `3. **有蓝牙设备但需要校准**: 如果有蓝牙设备连接且校准状态为`FAILED`、`APP_NOT_FOREGROUND`或`BT_NOT_CONNECTED`，显示校准图标` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L25 | `PopWin->>PopWin: 检查校准状态<br/>(FAILED/APP_NOT_FOREGROUND/BT_NOT_CONNECTED)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L284 | `- 如果校准状态为 FAILED/APP_NOT_FOREGROUND/BT_NOT_CONNECTED` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L552 | `- 3：应用不在前台 → CalibrationStatus.APP_NOT_FOREGROUND` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L1103 | `case APP_NOT_FOREGROUND:` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L1776 | `APP_NOT_FOREGROUND(3) // 应用不在前台` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_06-47-refactor-platform-checks-into-methods.md` | L309 | `+         APP_NOT_FOREGROUND(3), // 手机app未在前台，无法校准` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_06-47-refactor-platform-checks-into-methods.md` | L391 | `- `APP_NOT_FOREGROUND(3)` - 手机app未在前台，无法校准` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_06-47-refactor-platform-checks-into-methods.md` | L442 | `- `APP_NOT_FOREGROUND` (手机app未在前台，无法校准)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L6 | `发现 ON_HID_APP_NOT_FOREGROUND 这种状态下， 不出现校准图标。 理论上无法校准，应该有校准图标，请分析问什么，然后告诉我解决方案，等待确认。 慢慢来，一步一步分析。` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L12 | `Let me analyze the issue with the calibration icon not appearing when ON_HID_APP_NOT_FOREGROUND state occurs.` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L100 | `Now let's check how ON_HID_APP_NOT_FOREGROUND is handled in the onCheckHiTouchEvent method:` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L204 | `<summary>Grep search for "ON_HID_APP_NOT_FOREGROUND" • **3** files</summary>` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L208 | `⟪ 135 characters skipped ⟫.java` | L91 | `public final static int ON_HID_APP_NOT_FOREGROUND = 21;` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L209 | `⟪ 165 characters skipped ⟫ic com.autoai.welink.param.HidConstants.ON_HID_APP_NOT_FOREGROUND;` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L210 | `⟪ 228 characters skipped ⟫ ON_HID_APP_BACKGROUND_FAIL || state == ON_HID_APP_NOT_FOREGROUND) {` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L211 | `⟪ 137 characters skipped ⟫el.java` | L1112 | `onCheckHiTouchEvent(ON_HID_APP_NOT_FOREGROUND);` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L212 | `⟪ 165 characters skipped ⟫ic com.autoai.welink.param.HidConstants.ON_HID_APP_NOT_FOREGROUND;` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L213 | `⟪ 228 characters skipped ⟫ ON_HID_APP_BACKGROUND_FAIL || state == ON_HID_APP_NOT_FOREGROUND) {` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L214 | `⟪ 137 characters skipped ⟫el.java` | L1112 | `onCheckHiTouchEvent(ON_HID_APP_NOT_FOREGROUND);` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L21 | `-                 calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L49 | `+                 calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L67 | `⟪ 209 characters skipped ⟫`calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L70 | `⟪ 209 characters skipped ⟫`calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L72 | `⟪ 209 characters skipped ⟫`calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L75 | `⟪ 209 characters skipped ⟫`calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L78 | `⟪ 150 characters skipped ⟫`calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L81 | `⟪ 150 characters skipped ⟫`calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L129 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L255 | `⟪ 150 characters skipped ⟫`calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L258 | `⟪ 150 characters skipped ⟫`calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-%E5%B0%81%E8%A3%85%E5%87%BD%E6%95%B0%E4%BB%A5%E4%BE%BF%E9%87%8D%E7%94%A8.md` | L329 | `+                status == CalibrationStatus.APP_NOT_FOREGROUND;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-36-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E6%97%B6%E5%BA%8F%E5%88%86%E6%9E%90.md` | L35 | `- 如果校准状态为`FAILED`、`APP_NOT_FOREGROUND`或`BT_NOT_CONNECTED`，则需要校准` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-36-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E6%97%B6%E5%BA%8F%E5%88%86%E6%9E%90.md` | L92 | `- `APP_NOT_FOREGROUND(3)`: 手机APP未在前台` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L140 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L228 | `case APP_NOT_FOREGROUND:` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L230 | `+                     LogUtil.d(TAG_BT, "onCalibrationResult: APP_NOT_FOREGROUND - showing toast");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L314 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L539 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L633 | `case APP_NOT_FOREGROUND:` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L635 | `-                     LogUtil.d(TAG_BT, "onCalibrationResult: APP_NOT_FOREGROUND - showing toast");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L636 | `+                     LogUtil.d(TAG_BT, "BluetoothStateManager:onCalibrationResult APP_NOT_FOREGROUND");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L737 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-33-%E6%9B%B4%E6%96%B0%E6%96%87%E6%A1%A3%E4%B8%AD%E7%9A%84%E5%BB%BA%E8%AE%AE%E5%80%BC.md` | L516 | `- 3：应用不在前台 → CalibrationStatus.APP_NOT_FOREGROUND` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-40-%E5%88%86%E6%9E%90-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E7%9A%84%E6%97%B6%E5%BA%8F.md` | L210 | `4. 检查校准状态，如果是`FAILED`、`APP_NOT_FOREGROUND`或`BT_NOT_CONNECTED`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-40-%E5%88%86%E6%9E%90-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E7%9A%84%E6%97%B6%E5%BA%8F.md` | L347 | `- 3：应用不在前台 → `CalibrationStatus.APP_NOT_FOREGROUND`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_05-50-%E8%93%9D%E7%89%99%E5%9B%BE%E6%A0%87%E5%92%8C%E6%A0%A1%E5%87%86%E6%8C%89%E9%92%AE%E7%9A%84%E9%80%BB%E8%BE%91%E5%88%86%E6%9E%90.md` | L28 | `- `APP_NOT_FOREGROUND(3)`: 手机app未在前台，无法校准` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_05-50-%E8%93%9D%E7%89%99%E5%9B%BE%E6%A0%87%E5%92%8C%E6%A0%A1%E5%87%86%E6%8C%89%E9%92%AE%E7%9A%84%E9%80%BB%E8%BE%91%E5%88%86%E6%9E%90.md` | L210 | `- **触发条件**: 收到APP未在前台回调(`CalibrationStatus.APP_NOT_FOREGROUND`)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_05-50-%E8%93%9D%E7%89%99%E5%9B%BE%E6%A0%87%E5%92%8C%E6%A0%A1%E5%87%86%E6%8C%89%E9%92%AE%E7%9A%84%E9%80%BB%E8%BE%91%E5%88%86%E6%9E%90.md` | L341 | `- `CalibrationStatus.APP_NOT_FOREGROUND`: 手机APP未在前台` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_05-50-%E8%93%9D%E7%89%99%E5%9B%BE%E6%A0%87%E5%92%8C%E6%A0%A1%E5%87%86%E6%8C%89%E9%92%AE%E7%9A%84%E9%80%BB%E8%BE%91%E5%88%86%E6%9E%90.md` | L358 | `- 通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`3`(对应`CalibrationStatus.APP_NOT_FOREGROUND`)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_05-50-%E8%93%9D%E7%89%99%E5%9B%BE%E6%A0%87%E5%92%8C%E6%A0%A1%E5%87%86%E6%8C%89%E9%92%AE%E7%9A%84%E9%80%BB%E8%BE%91%E5%88%86%E6%9E%90.md` | L414 | `- `CalibrationStatus.APP_NOT_FOREGROUND`(通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`3`)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/param/HidConstants.java` | L91 | `public final static int ON_HID_APP_NOT_FOREGROUND = 21;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L5 | `import static com.autoai.welink.param.HidConstants.ON_HID_APP_NOT_FOREGROUND;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1004 | `if (state == ON_HID_SUCCESS || state == ON_HID_CONNECT_FAIL || state == ON_HID_APP_BACKGROUND_FAIL || state == ON_HID_APP_NOT_FOREGROUND) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1112 | `onCheckHiTouchEvent(ON_HID_APP_NOT_FOREGROUND);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.cursor/rules/derived-cursor-rules.mdc` | L467 | `- `CalibrationStatus.APP_NOT_FOREGROUND`: The mobile app is not in the foreground.` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.cursor/rules/derived-cursor-rules.mdc` | L490 | `- **Trigger Condition**: Receive APP not in foreground callback (`CalibrationStatus.APP_NOT_FOREGROUND`).` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.cursor/rules/derived-cursor-rules.mdc` | L567 | `- `CalibrationStatus.APP_NOT_FOREGROUND`: The mobile APP is not in the foreground` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.cursor/rules/derived-cursor-rules.mdc` | L584 | `- Through `LinkSdkModel.INSTANCE.registerPopHidCheckCallback` receive calibration result as `3` (corresponding to `CalibrationStatus.APP_NOT_FOREGROUND`)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.cursor/rules/derived-cursor-rules.mdc` | L640 | `- `CalibrationStatus.APP_NOT_FOREGROUND` (calibration result `3` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L341 | `APP_NOT_FOREGROUND(3), // 手机app未在前台，无法校准` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L614 | `case APP_NOT_FOREGROUND:` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2802 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L340 | `APP_NOT_FOREGROUND(3), // 手机app未在前台，无法校准` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L603 | `case APP_NOT_FOREGROUND:` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2774 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2805 | `status == CalibrationStatus.APP_NOT_FOREGROUND;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L5 | `import static com.autoai.welink.param.HidConstants.ON_HID_APP_NOT_FOREGROUND;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1004 | `if (state == ON_HID_SUCCESS || state == ON_HID_CONNECT_FAIL || state == ON_HID_APP_BACKGROUND_FAIL || state == ON_HID_APP_NOT_FOREGROUND) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1112 | `onCheckHiTouchEvent(ON_HID_APP_NOT_FOREGROUND);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L25 | `PopWin->>PopWin: 检查校准状态<br/>(FAILED/APP_NOT_FOREGROUND/BT_NOT_CONNECTED)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L284 | `- 如果校准状态为 FAILED/APP_NOT_FOREGROUND/BT_NOT_CONNECTED` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L552 | `- 3：应用不在前台 → CalibrationStatus.APP_NOT_FOREGROUND` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L1103 | `case APP_NOT_FOREGROUND:` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L1776 | `APP_NOT_FOREGROUND(3) // 应用不在前台` |

</details>

---

Let me look at the SdkViewModel code specifically in the onTouchSendEnd method, which handles ON_HID_APP_NOT_FOREGROUND:

---

<details>
            <summary>Grep search for "onTouchSendEnd" • **9** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L26 | `onTouchSendEnd();` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L31 | `private void onTouchSendEnd() {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L91 | `2. **处理逻辑单一**: 所有 `ON_TOUCH_END_POINT` 都统一调用 `onTouchSendEnd()` 并设置为 `ON_HID_APP_BACKGROUND_FAIL`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L126 | `onTouchSendEndFromForeground();` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L128 | `onTouchSendEndFromBackground();` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L131 | `onTouchSendEnd();` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L134 | `private void onTouchSendEndFromForeground() {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L140 | `private void onTouchSendEndFromBackground() {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L177 | `onTouchSendEndFromForeground();` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L179 | `onTouchSendEndFromBackground();` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L197 | `onTouchSendEndFromForeground();` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L199 | `onTouchSendEndFromBackground();` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L78 | `SdkVM->>SdkVM: onTouchSendEnd() 校准失败` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L394 | `23C. onTouchSendEnd() - 校准失败，应用不在前台` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L608 | `onTouchSendEnd()` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L677 | `- SdkViewModel.onTouchSendEnd()` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L31 | `⟪ 145 characters skipped ⟫理逻辑单一**: 所有 `ON_TOUCH_END_POINT` 都统一调用 `onTouchSendEnd()` 并设置为 `ON_HID_APP_BACKGROUND_FAIL`` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-33-%E6%9B%B4%E6%96%B0%E6%96%87%E6%A1%A3%E4%B8%AD%E7%9A%84%E5%BB%BA%E8%AE%AE%E5%80%BC.md` | L358 | `- 19C. onTouchSendEnd() - 校准失败，应用不在前台` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-33-%E6%9B%B4%E6%96%B0%E6%96%87%E6%A1%A3%E4%B8%AD%E7%9A%84%E5%BB%BA%E8%AE%AE%E5%80%BC.md` | L359 | `+ 23C. onTouchSendEnd() - 校准失败，应用不在前台` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-24-%E6%A3%80%E6%9F%A5%E6%96%87%E6%A1%A3%E4%B8%8E%E4%BB%A3%E7%A0%81%E7%9A%84%E4%B8%80%E8%87%B4%E6%80%A7.md` | L534 | `| onTouchSendEnd() - 校准失败，应用不在前台 | `onTouchSendEnd()` 调用 | 完全符合 |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-40-%E5%88%86%E6%9E%90-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E7%9A%84%E6%97%B6%E5%BA%8F.md` | L251 | `1. 调用`onTouchSendEnd()`处理校准失败` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-40-%E5%88%86%E6%9E%90-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E7%9A%84%E6%97%B6%E5%BA%8F.md` | L371 | `- 车机端调用`onTouchSendEnd()`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1051 | `onTouchSendEnd();` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1100 | `private void onTouchSendEnd() {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1101 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendEnd <<< iOS app not in foreground");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1107 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendEnd calibration interrupted - app switched to background during calibration");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1111 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendEnd calibration failed - app not in foreground");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1051 | `onTouchSendEnd();` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1100 | `private void onTouchSendEnd() {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1101 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendEnd <<< iOS app not in foreground");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1107 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendEnd calibration interrupted - app switched to background during calibration");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1111 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendEnd calibration failed - app not in foreground");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L78 | `SdkVM->>SdkVM: onTouchSendEnd() 校准失败` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L394 | `23C. onTouchSendEnd() - 校准失败，应用不在前台` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L608 | `onTouchSendEnd()` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L677 | `- SdkViewModel.onTouchSendEnd()` |

</details>

---

Now let me check how the onCheckHiTouchEvent method handles different calibration states:

---

<details>
            <summary>Grep search for "onCheckHiTouchEvent" • **9** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L34 | `onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L137 | `onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL_FROM_FOREGROUND);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/%40todo/on_touch_end_point_differentiation_analysis.md` | L143 | `onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL_FROM_BACKGROUND);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L80 | `SdkVM->>PopWin: onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L148 | `SdkVM->>PopWin: onCheckHiTouchEvent(ON_HID_SUCCESS)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L155 | `SdkVM->>PopWin: onCheckHiTouchEvent(ON_HID_SUCCESS)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L159 | `SdkVM->>PopWin: onCheckHiTouchEvent(ON_HID_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L186 | `SdkVM->>PopWin: onCheckHiTouchEvent(ON_HID_CONNECT_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L495 | `- 触发成功回调：onCheckHiTouchEvent(ON_HID_SUCCESS)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L502 | `- 触发失败回调：onCheckHiTouchEvent(ON_HID_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L513 | `36. onCheckHiTouchEvent(state) 处理：` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L577 | `触发连接失败回调：onCheckHiTouchEvent(ON_HID_CONNECT_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L611 | `触发后台失败回调：onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L680 | `- 触发回调：onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L701 | `- 触发失败回调：onCheckHiTouchEvent(ON_HID_CONNECT_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L912 | `onCheckHiTouchEvent(ON_HID_CONNECT_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L985 | `onCheckHiTouchEvent(ON_HID_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L997 | `private void onCheckHiTouchEvent(int state) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L998 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent processing calibration result state=" + state);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1005 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent final result, notifying callback state=" + state);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1010 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent retrying calibration, attempt=" + (mIOSCalibrationRetryTimes + 1) + "/" + 2);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1016 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent max retries reached, sending END_FAIL");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1024 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent hidCheckCallback is null");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1076 | `onCheckHiTouchEvent(ON_HID_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1082 | `onCheckHiTouchEvent(ON_HID_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1094 | `onCheckHiTouchEvent(ON_HID_SUCCESS);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1108 | `onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1112 | `onCheckHiTouchEvent(ON_HID_APP_NOT_FOREGROUND);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1144 | `onCheckHiTouchEvent(ON_HID_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L100 | `Now let's check how ON_HID_APP_NOT_FOREGROUND is handled in the onCheckHiTouchEvent method:` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L211 | `⟪ 117 characters skipped ⟫app/model/SdkViewModel.java` | L1112 | `onCheckHiTouchEvent(ON_HID_APP_NOT_FOREGROUND);` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-24_11-31-untitled.md` | L214 | `⟪ 117 characters skipped ⟫app/model/SdkViewModel.java` | L1112 | `onCheckHiTouchEvent(ON_HID_APP_NOT_FOREGROUND);` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-33-%E6%9B%B4%E6%96%B0%E6%96%87%E6%A1%A3%E4%B8%AD%E7%9A%84%E5%BB%BA%E8%AE%AE%E5%80%BC.md` | L251 | `触发连接失败回调：onCheckHiTouchEvent(ON_HID_CONNECT_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-33-%E6%9B%B4%E6%96%B0%E6%96%87%E6%A1%A3%E4%B8%AD%E7%9A%84%E5%BB%BA%E8%AE%AE%E5%80%BC.md` | L470 | `- 触发失败回调：onCheckHiTouchEvent(ON_HID_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-33-%E6%9B%B4%E6%96%B0%E6%96%87%E6%A1%A3%E4%B8%AD%E7%9A%84%E5%BB%BA%E8%AE%AE%E5%80%BC.md` | L476 | `- 32. onCheckHiTouchEvent(state) 处理：` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-33-%E6%9B%B4%E6%96%B0%E6%96%87%E6%A1%A3%E4%B8%AD%E7%9A%84%E5%BB%BA%E8%AE%AE%E5%80%BC.md` | L477 | `+ 36. onCheckHiTouchEvent(state) 处理：` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-40-%E5%88%86%E6%9E%90-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E7%9A%84%E6%97%B6%E5%BA%8F.md` | L254 | `4. 触发失败回调：`onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL)`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-40-%E5%88%86%E6%9E%90-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E7%9A%84%E6%97%B6%E5%BA%8F.md` | L280 | `- 触发成功回调：`onCheckHiTouchEvent(ON_HID_SUCCESS)`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-40-%E5%88%86%E6%9E%90-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E7%9A%84%E6%97%B6%E5%BA%8F.md` | L314 | `- 触发成功回调：`onCheckHiTouchEvent(ON_HID_SUCCESS)`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-40-%E5%88%86%E6%9E%90-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E7%9A%84%E6%97%B6%E5%BA%8F.md` | L317 | `- 触发失败回调：`onCheckHiTouchEvent(ON_HID_FAIL)`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-40-%E5%88%86%E6%9E%90-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E7%9A%84%E6%97%B6%E5%BA%8F.md` | L322 | `1. `onCheckHiTouchEvent(state)`处理校准结果：` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-40-%E5%88%86%E6%9E%90-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E7%9A%84%E6%97%B6%E5%BA%8F.md` | L359 | `- 触发连接失败回调：`onCheckHiTouchEvent(ON_HID_CONNECT_FAIL)`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_07-40-%E5%88%86%E6%9E%90-ios-%E6%A0%A1%E5%87%86%E6%B5%81%E7%A8%8B%E7%9A%84%E6%97%B6%E5%BA%8F.md` | L374 | `- 触发后台失败回调：`onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL)`` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-24-%E6%A3%80%E6%9F%A5%E6%96%87%E6%A1%A3%E4%B8%8E%E4%BB%A3%E7%A0%81%E7%9A%84%E4%B8%80%E8%87%B4%E6%80%A7.md` | L576 | `| 触发成功回调：onCheckHiTouchEvent(ON_HID_SUCCESS) | 调用 `onCheckHiTouchEvent(ON_HID_SUCCESS)` | 完全符合 |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-24-%E6%A3%80%E6%9F%A5%E6%96%87%E6%A1%A3%E4%B8%8E%E4%BB%A3%E7%A0%81%E7%9A%84%E4%B8%80%E8%87%B4%E6%80%A7.md` | L580 | `| 触发失败回调：onCheckHiTouchEvent(ON_HID_FAIL) | 调用 `onCheckHiTouchEvent(ON_HID_FAIL)` | 完全符合 |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-24-%E6%A3%80%E6%9F%A5%E6%96%87%E6%A1%A3%E4%B8%8E%E4%BB%A3%E7%A0%81%E7%9A%84%E4%B8%80%E8%87%B4%E6%80%A7.md` | L586 | `| onCheckHiTouchEvent(state) 处理 | 方法存在且实现 | 完全符合 |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-23_05-24-%E6%A3%80%E6%9F%A5%E6%96%87%E6%A1%A3%E4%B8%8E%E4%BB%A3%E7%A0%81%E7%9A%84%E4%B8%80%E8%87%B4%E6%80%A7.md` | L588 | `| 如果失败且重试次数 < 2：重试流程 | 在 `onCheckHiTouchEvent` 中处理重试逻辑 | 完全符合 |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L912 | `onCheckHiTouchEvent(ON_HID_CONNECT_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L985 | `onCheckHiTouchEvent(ON_HID_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L997 | `private void onCheckHiTouchEvent(int state) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L998 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent processing calibration result state=" + state);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1005 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent final result, notifying callback state=" + state);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1010 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent retrying calibration, attempt=" + (mIOSCalibrationRetryTimes + 1) + "/" + 2);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1016 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent max retries reached, sending END_FAIL");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1024 | `LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent hidCheckCallback is null");` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1076 | `onCheckHiTouchEvent(ON_HID_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1082 | `onCheckHiTouchEvent(ON_HID_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1094 | `onCheckHiTouchEvent(ON_HID_SUCCESS);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1108 | `onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1112 | `onCheckHiTouchEvent(ON_HID_APP_NOT_FOREGROUND);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1144 | `onCheckHiTouchEvent(ON_HID_FAIL);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L80 | `SdkVM->>PopWin: onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L148 | `SdkVM->>PopWin: onCheckHiTouchEvent(ON_HID_SUCCESS)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L155 | `SdkVM->>PopWin: onCheckHiTouchEvent(ON_HID_SUCCESS)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L159 | `SdkVM->>PopWin: onCheckHiTouchEvent(ON_HID_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L186 | `SdkVM->>PopWin: onCheckHiTouchEvent(ON_HID_CONNECT_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L495 | `- 触发成功回调：onCheckHiTouchEvent(ON_HID_SUCCESS)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L502 | `- 触发失败回调：onCheckHiTouchEvent(ON_HID_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L513 | `36. onCheckHiTouchEvent(state) 处理：` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L577 | `触发连接失败回调：onCheckHiTouchEvent(ON_HID_CONNECT_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L611 | `触发后台失败回调：onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L680 | `- 触发回调：onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/ios_calibration_flow_sequence.md` | L701 | `- 触发失败回调：onCheckHiTouchEvent(ON_HID_CONNECT_FAIL)` |

</details>

---

Let me also check the PopWinService class to see how it handles APP_NOT_FOREGROUND:

---

Read file: app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java

---

Now I understand the issue clearly. Let me analyze what's happening with the ON_HID_APP_NOT_FOREGROUND status:

## Problem Analysis

1. **The Issue**: When the ON_HID_APP_NOT_FOREGROUND status occurs, the calibration icon does not appear on the screen, even though it should since calibration cannot proceed when the iOS app is not in the foreground.

2. **Expected Behavior**: According to the documentation in `bluetooth_calibration_icon_display_logic.md`, when the iOS app is not in the foreground (APP_NOT_FOREGROUND status), the system should display the calibration icon to indicate to the user that calibration is needed.

3. **Current Flow**:
   - In `SdkViewModel.java`, when the iOS app is not in the foreground, it calls `onTouchSendEnd()`.
   - Within this method, it sets `isCalibrationStarted = false` and calls `onCheckHiTouchEvent(ON_HID_APP_NOT_FOREGROUND)`.
   - The `onCheckHiTouchEvent` method should then notify the callback (PopWinService) about this status.

4. **Root Cause**: Looking at lines 997-1024 in SdkViewModel.java, I can see that in the `onCheckHiTouchEvent` method, the status ON_HID_APP_NOT_FOREGROUND is treated as a "final result" rather than a status that needs action:

```java
if (state == ON_HID_SUCCESS || state == ON_HID_CONNECT_FAIL || state == ON_HID_APP_BACKGROUND_FAIL || state == ON_HID_APP_NOT_FOREGROUND) {
    LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent final result, notifying callback state=" + state);
    mIOSCalibrationRetryTimes = 0;
    hidCheckCallback.onCheck(state);
} else {
    // Retry logic for other states
    ...
}
```

This means when the APP_NOT_FOREGROUND status is received, it's just passed to the callback without triggering any retry mechanism or special handling.

5. **In PopWinService**: According to the documentation and the code, when PopWinService receives the APP_NOT_FOREGROUND status through its CalibrationMonitoringCallback, it should:
   - Update the calibration status to APP_NOT_FOREGROUND
   - Show the calibration icon
   - Display a toast message "请打开手机上的WeLink应用"

But it seems this is not happening, likely because the callback is not being properly registered or processed.

## Solution

Based on this analysis, here are the steps to fix the issue:

1. **Verify Callback Registration**: Make sure that PopWinService.CalibrationMonitoringCallback is properly registered with SdkViewModel through `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`.

2. **Check the CalibrationMonitoringCallback Implementation**: The CalibrationMonitoringCallback method should properly handle the APP_NOT_FOREGROUND status (value 3) by updating the calibration status and calling the bluetoothStateManager to update the UI.

3. **Verify BluetoothStateManager Logic**: In the BluetoothStateManager class within PopWinService, confirm that the onCalibrationResult method correctly processes the APP_NOT_FOREGROUND status and updates the UI accordingly.

4. **Explicit Handling in isCalibrationNeeded Method**: Make sure the isCalibrationNeeded method in PopWinService properly checks for APP_NOT_FOREGROUND status, which it appears to do at line 2774:

```java
return calibrationStatus == CalibrationStatus.FAILED ||
       calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||
       calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED;
```

## Specific Implementation Fix

The most likely issue is in the callback registration or processing. Here's how you should implement the fix:

1. Check if the PopWinService is properly registering its CalibrationMonitoringCallback with the SdkViewModel:

```java
// In PopWinService's onCreate or when appropriate
LinkSdkModel.INSTANCE.registerPopHidCheckCallback(this::CalibrationMonitoringCallback);
```

2. Ensure that CalibrationMonitoringCallback properly processes the APP_NOT_FOREGROUND status (value 3):

```java
private void CalibrationMonitoringCallback(int isCheck) {
    LogUtil.d(TAG_BT + TAG_IOS_CALIB, "PopWinService:CalibrationMonitoringCallback <<< received calibration result=" + isCheck + ", status=" + CalibrationStatus.fromValue(isCheck));
    
    // Update calibration status
    updateCalibrationStatus(isCheck);
    
    // Specifically for APP_NOT_FOREGROUND, ensure the UI is updated
    if (isCheck == CalibrationStatus.APP_NOT_FOREGROUND.getValue()) {
        bluetoothStateManager.checkAndUpdateBluetoothState();
        // Consider explicitly showing a toast
        Toast.makeText(context, "请打开手机上的WeLink应用", Toast.LENGTH_SHORT).show();
    }
}
```

3. Double check that the BluetoothStateManager.onCalibrationResult method correctly handles APP_NOT_FOREGROUND:

```java
private void onCalibrationResult(int calibrationResult) {
    LogUtil.d(TAG_BT, "BluetoothStateManager:onCalibrationResult status=" + calibrationResult);
    switch (CalibrationStatus.fromValue(calibrationResult)) {
        // Other cases...
        case APP_NOT_FOREGROUND:
            LogUtil.d(TAG_BT, "BluetoothStateManager:onCalibrationResult APP_NOT_FOREGROUND");
            if (isIOSDevice() && HidUtil.getConnectedBluetoothDevices().size() > 0) {
                // Update to show calibration icon
                updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, true);
                // Show toast
                Toast.makeText(context, "请打开手机上的WeLink应用", Toast.LENGTH_SHORT).show();
            }
            break;
        // Other cases...
    }
}
```

4. Add more logging in the critical paths to verify the flow:

```java
// In SdkViewModel.onTouchSendEnd
LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendEnd calling onCheckHiTouchEvent with ON_HID_APP_NOT_FOREGROUND");
onCheckHiTouchEvent(ON_HID_APP_NOT_FOREGROUND);

// In SdkViewModel.onCheckHiTouchEvent
if (state == ON_HID_APP_NOT_FOREGROUND) {
    LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent APP_NOT_FOREGROUND - notifying callback");
    // Log the callback object to ensure it's not null
    LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent hidCheckCallback is " + (hidCheckCallback != null ? "registered" : "null"));
}
```

This comprehensive approach should identify and resolve the issue with the ON_HID_APP_NOT_FOREGROUND status not showing the calibration icon.

---


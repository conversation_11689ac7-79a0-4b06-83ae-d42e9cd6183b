package com.autoai.wdlink.hu.controlbar

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.annotation.FloatRange
import androidx.annotation.StyleRes
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.transition.Slide
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.castwindow.controlbar.AirControlBarComponent
import com.autoai.wdlink.hu.castwindow.ktx.dpToPx
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState
import com.autoai.wdlink.hu.castwindow.view.CastWindowStateManager


class AirControlDialogFragment : DialogFragment() {
    private lateinit var airControlBarComponent: AirControlBarComponent
    companion object {
        fun newInstance() = AirControlDialogFragment()
    }

    private val viewModel: AirControlViewModel by viewModels()

    protected fun setDefaultDialogStyle() {
        if (!showsDialog) {
            dismissAllowingStateLoss()
        }
        setStyle(STYLE_NO_TITLE, R.style.TranslucentDialog)
    }

    protected fun setWindowAttributes(@Slide.GravityFlag gravity: Int) {
        this.setWindowAttributes(gravity, 0.6f, 0)
    }

    protected fun setWindowAttributes(
        @Slide.GravityFlag gravity: Int,
        @FloatRange(from = 0.0, to = 1.0) bgAlpha: Float,
        @StyleRes windowAnimations: Int
    ) {
        this.setWindowAttributes(gravity, bgAlpha, windowAnimations, ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT))
    }

    protected fun setWindowAttributes(
        @Slide.GravityFlag gravity: Int,
        @FloatRange(from = 0.0, to = 1.0) bgAlpha: Float,
        @StyleRes windowAnimations: Int,
        layoutParams: ViewGroup.LayoutParams
    ) {
        if (dialog == null) {
            return
        }
        val window = dialog!!.window ?: return
        window.decorView.setPadding(0, 45.dpToPx().toInt(), 0, 0)
        val wlp = window.attributes
        wlp.gravity = gravity
        wlp.dimAmount = bgAlpha //背景透明度
        wlp.windowAnimations = windowAnimations
        wlp.width = layoutParams.width
        wlp.height = layoutParams.height
        window.attributes = wlp
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setDefaultDialogStyle()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return AirControlBarComponent.create(inflater.context, "android")
            .also { airControlBarComponent = it }
            .viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        airControlBarComponent.viewBinding.buttonFull.setOnClickListener {
            Toast.makeText(context, "小窗", Toast.LENGTH_SHORT).show()
            CastWindowStateManager.switchState(CastWindowState.SMALL)
        }
    }

    override fun onStart() {
        super.onStart()
        setWindowAttributes(
            Gravity.TOP, 0f, 0, ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT
            )
        )
    }
}
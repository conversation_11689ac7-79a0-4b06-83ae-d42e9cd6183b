top.eiyooooo.easycontrol.app
color alert
color background
color bar1
color bar2
color bar3
color bar4
color button
color cardBackground
color clientBar
color clientBarSecond
color ic_launcher_background
color onAlert
color onBackground
color onBackgroundSecond
color onButton
color onCardBackground
color onCardBackgroundSecond
color onCardDivider
color translucent
dimen largeFont
dimen middleFont
dimen round
dimen round_30
dimen smallFont
dimen smallSmallFont
drawable background_cron
drawable background_cron_stroke
drawable bars
drawable btn_click_f
drawable btn_resize_move_bg
drawable caret_left
drawable chevron_left
drawable close_button_selector
drawable compress
drawable divider_line
drawable ellipsis_vertical
drawable equals
drawable expand
drawable ic_launcher_foreground
drawable lightbulb
drawable lightbulb_off
drawable link_can_connect
drawable link_can_not_connect
drawable link_checking_connection
drawable lock
drawable mini_button_selector_1
drawable minus
drawable move_button_selector
drawable not_equal
drawable o
drawable pair
drawable phone
drawable phones
drawable plus
drawable power_off
drawable refresh
drawable reset_location
drawable rotate
drawable share_in
drawable share_out
drawable square
drawable unlock
drawable wifi_can_connect
drawable wifi_can_not_connect
drawable wifi_checking_connection
drawable window_bg
drawable x
id adb_key_pri
id adb_key_pub
id address
id address_title
id back_button
id bar
id bar_view
id button_add
id button_always_full_mode
id button_auto
id button_back
id button_cancel
id button_change
id button_change_to_mini
id button_change_to_small
id button_close
id button_confirm
id button_create_shortcut
id button_custom
id button_delete
id button_full
id button_full_exit
id button_get_uuid
id button_go_to_set
id button_home
id button_layout
id button_light
id button_light_off
id button_lock
id button_mini
id button_more
id button_nav_bar
id button_night_mode
id button_no
id button_pair
id button_power
id button_refresh
id button_rotate
id button_set
id button_start_wireless
id button_switch
id button_transfer
id button_yes
id capture_event
id captured_events
id cert_name
id cert_regenerate
id change_to_mini_events
id change_to_small_events
id container_enable
id container_latency
id create_display
id debug_port
id default_full
id device_expand
id device_icon
id device_name
id devices_list
id display_mirroring
id edit_text
id ip
id ipv4
id ipv6
id is_audio
id is_options
id item_detail
id item_spinner
id item_switch
id item_text
id iv_close
id iv_mini
id iv_move
id ll_title
id log_device
id log_text
id my_view
id name
id nav_bar
id night_mode_detector
id ok
id opening_port
id options
id pairing
id pairing_code
id pairing_port
id panel
id panel_landscape
id permission_checker
id re_size
id reset_location
id rl_context
id run_open_port
id run_pair
id scan_address
id scan_remote_app_list
id scanned
id scanning
id set_about
id set_default
id set_display
id set_other
id specified_app
id specified_app_title
id spinner_latency
id switch_enable
id text
id text_about
id text_enable
id text_latency
id text_latency_detail
id texture_view_layout
id title
id webview
layout activity_adb_key
layout activity_full
layout activity_ip
layout activity_log
layout activity_main
layout activity_monitor
layout activity_pair
layout activity_set
layout activity_web_view
layout item_add_device
layout item_add_event
layout item_devices_item
layout item_devices_item_detail
layout item_loading
layout item_night_mode_changer
layout item_reconnect
layout item_request_permission
layout item_set_device
layout item_spinner
layout item_spinner_item
layout item_switch
layout item_text
layout item_text_detail
layout module_dialog
layout module_mini_view
layout module_small_view
mipmap close_0
mipmap close_1
mipmap ic_launcher_round
mipmap mini_image_left
mipmap mini_image_right
mipmap move_icon
mipmap move_icon1
mipmap scale_icon
mipmap scale_icon1
raw easycontrol_server
string adb_key_button
string adb_key_button_code
string adb_key_pri
string adb_key_pub
string add_device_address
string add_device_address_hint
string add_device_button
string add_device_name
string add_device_name_hint
string add_device_option
string add_device_scan
string add_device_scan_address_finish_none
string add_device_scan_finish
string add_device_scan_specify_app_finish_error
string add_device_scanning
string add_device_specify_app
string add_device_specify_app_hint
string app_name
string cancel
string car_version_message
string change_night_mode_failed
string change_night_mode_success
string confirm
string error_address_error
string error_app_not_found
string error_connect_server
string error_create_display
string error_default_device_not_found
string error_device_not_found
string error_mode_not_support
string error_no_browser
string error_refused_back
string error_stream_closed
string error_transfer_app_failed
string ip_copy
string ip_ipv4
string ip_ipv6
string ip_scan_device_title
string ip_scan_finish_copy
string ip_scan_finish_none
string ip_scanning_device
string ip_title
string loading_text
string log_notify
string log_other_devices
string log_title
string main_float_permission
string main_float_permission_button
string main_no_float_mode_button
string main_poem
string monitor_add_change_to_mini_event
string monitor_add_change_to_small_event
string monitor_add_event
string monitor_android_version_not_support
string monitor_capture_event
string monitor_capture_event_start
string monitor_capture_event_stop
string monitor_change_to_mini_event
string monitor_change_to_small_event
string monitor_enable
string monitor_latency
string monitor_latency_detail
string monitor_no_event
string monitor_no_permission
string monitor_permission_request
string monitor_show_event
string monitor_title
string night_mode_auto
string night_mode_current
string night_mode_custom
string night_mode_no
string night_mode_yes
string option_clipboard_sync
string option_clipboard_sync_detail
string option_default_full
string option_default_full_detail
string option_default_usb_device
string option_default_usb_device_detail
string option_is_audio
string option_is_audio_detail
string option_max_fps
string option_max_fps_detail
string option_max_size
string option_max_size_detail
string option_max_size_original
string option_max_video_bit
string option_max_video_bit_detail
string option_night_mode_sync
string option_night_mode_sync_detail
string option_set_resolution
string option_set_resolution_detail
string option_startup_device
string option_startup_device_detail
string option_use_h265
string option_use_h265_detail
string option_use_opus
string option_use_opus_detail
string pair_cert
string pair_cert_regenerate
string pair_debug_port_hint
string pair_failed
string pair_generating_cert
string pair_ip
string pair_ip_hint
string pair_no_cert
string pair_open_port
string pair_pair
string pair_pairing_code_hint
string pair_pairing_port_hint
string pair_run
string pair_success
string set_about
string set_about_how_to_use
string set_about_ip
string set_about_privacy
string set_about_version
string set_about_website
string set_always_full_mode
string set_always_full_mode_detail
string set_app_monitor
string set_app_monitor_detail
string set_audio_channel
string set_audio_channel_detail
string set_auto_countdown
string set_auto_countdown_detail
string set_connect_usb
string set_connect_usb_detail
string set_create_startup_shortcut
string set_default
string set_device_button_change
string set_device_button_create_shortcut
string set_device_button_delete
string set_device_button_get_uuid
string set_device_button_get_uuid_success
string set_device_button_night_mode
string set_device_button_recover_error
string set_device_button_start_wireless
string set_device_button_start_wireless_success
string set_device_title
string set_display
string set_display_auto_back_on_start_default
string set_display_auto_back_on_start_default_detail
string set_display_default_mini_on_outside
string set_display_default_mini_on_outside_detail
string set_display_default_show_nav_bar
string set_display_default_show_nav_bar_detail
string set_display_full_fill
string set_display_full_fill_detail
string set_display_full_to_mini_on_exit
string set_display_full_to_mini_on_exit_detail
string set_display_keep_screen_awake
string set_display_keep_screen_awake_detail
string set_display_mini_recover_on_timeout
string set_display_mini_recover_on_timeout_detail
string set_enable_usb
string set_enable_usb_detail
string set_force_desktop_mode
string set_force_desktop_mode_detail
string set_license
string set_light_off_on_connect
string set_light_off_on_connect_detail
string set_light_off_on_connect_error
string set_light_on_on_close
string set_light_on_on_close_detail
string set_light_on_on_close_error
string set_lock_screen_on_close
string set_lock_screen_on_close_detail
string set_mirror_mode
string set_mirror_mode_detail
string set_no_auto_countdown
string set_other
string set_other_clear_key
string set_other_clear_key_code
string set_other_custom_key
string set_other_locale
string set_other_locale_code
string set_other_log
string set_reconnect
string set_reconnect_detail
string set_set_full_screen
string set_set_full_screen_detail
string set_try_start_default_in_app_transfer
string set_try_start_default_in_app_transfer_detail
string set_wake_up_screen_on_connect
string set_wake_up_screen_on_connect_detail
string start_application_transfer
string start_display_mirroring
string tip_application_transfer
string tip_default_device
string tip_default_usb
string tip_reconnect
style Transparent

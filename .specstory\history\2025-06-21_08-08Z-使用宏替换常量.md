<!-- Generated by SpecStory -->

# 使用宏替换常量 (2025-06-21 08:08Z)

_**User**_

这里的 1,2,3,4 可以改成 @HidConstants.java 中规定的宏么。 

---

_**Assistant**_

I'll update the iOS HID calibration callback to use the constants defined in HidConstants.java.

---

```diff
              //ios 校准回调 0成功，  1校准失败，  2蓝牙未连接, 3 手机app未在前台，无法校准
              when (it.iosHidState) {
-                 0 -> {
+                 HidConstants.ON_HID_SUCCESS -> {
                      binding.scanningEffect.clearAnimation()
                      handler.postDelayed({iosWifiLin("ios")}, 100)
                  }
  
-                 1,
-                 3  -> {
+                 HidConstants.ON_HID_FAIL,
+                 HidConstants.ON_HID_APP_BACKGROUND_FAIL  -> {
                      binding.scanningEffect.clearAnimation()
                      binding.calibrationingImg.visibility = View.GONE
```

---

```diff
  import com.autoai.avs.common.qrcode.QRCodeUtil
  import com.autoai.avslinkhid.api.HidUtil
+ import com.autoai.welink.param.HidConstants
  import com.autoai.wdlink.hu.auth.AuthModel
  import com.autoai.wdlink.hu.auth.AuthModel.hasAuthority
```

---

Now I've updated the iOS HID calibration callback in MainActivity.kt to use the constants defined in HidConstants.java instead of hardcoded values. Specifically:

1. Replaced `0` with `HidConstants.ON_HID_SUCCESS` for successful calibration
2. Replaced `1` with `HidConstants.ON_HID_FAIL` for calibration failure
3. Replaced `3` with `HidConstants.ON_HID_APP_BACKGROUND_FAIL` for when the app is in the background

I also added the necessary import statement: `import com.autoai.welink.param.HidConstants` at the top of the file.

This change makes the code more maintainable and less error-prone since it now uses the named constants instead of magic numbers.

---

_**User**_

改成 是否蓝牙和互联设备匹配的状态判断。

---

_**Assistant**_

I'll update the code to handle the Bluetooth pairing state with the connected device using the HidConstants.ON_HID_BT_PAIRED_WITH_PHONE constant.

---

```diff
                          iosWifiLin2("ios")
                      }else{
-                         iosCalibrationLin()
+                         // 检查蓝牙设备是否与互联手机匹配
+                         LinkSdkModel.checkBluetoothPairing()
                      }
                      Logger.d("permission stateios = ${CommonData.DEVICE_TYPE_WIFI_IOS}")
```

```diff
                      // guideDialog?.dismiss()
                  }
+                 HidConstants.ON_HID_BT_PAIRED_WITH_PHONE -> {
+                     // 蓝牙与互联手机匹配成功，可以进行校准
+                     handler.postDelayed({iosCalibrationLin()}, 100)
+                 }
              }
          }
```

---

I've updated the code to properly handle Bluetooth pairing status with the connected device:

1. Modified the devicetypeObserver to check if Bluetooth devices are connected but not immediately start calibration
2. Instead, it now calls `LinkSdkModel.checkBluetoothPairing()` to verify if the connected Bluetooth device matches the phone connected for screen mirroring
3. Added a case for `HidConstants.ON_HID_BT_PAIRED_WITH_PHONE` which will be triggered when the Bluetooth device is successfully paired with the connected phone
4. When this pairing is confirmed, it then calls `iosCalibrationLin()` to start the calibration process

This change ensures that calibration only starts when there's a confirmed match between the Bluetooth device and the connected phone, as defined by the `ON_HID_BT_PAIRED_WITH_PHONE` constant in HidConstants.java.

---

_**User**_

isBTPairedWithPhone 用这个判断。

---

_**Assistant**_

I'll update the code to use `isBTPairedWithPhone` for checking if the Bluetooth device is paired with the phone.

---

```diff
                      }
                  }
-                 HidConstants.ON_HID_BT_PAIRED_WITH_PHONE -> {
-                     handler.postDelayed({iosCalibrationLin()}, 100)
              }
          }
```

---

```diff
                          iosWifiLin2("ios")
                      }else{
-                         iosCalibrationLin()
+                         // 检查蓝牙设备是否与互联手机匹配
+                         val isBTPairedWithPhone = LinkSdkModel.isBluetoothPairedWithPhone()
+                         if (isBTPairedWithPhone) {
+                             handler.postDelayed({iosCalibrationLin()}, 100)
+                         } else {
+                             iosWifiLin2("ios")
+                         }
                      }
                      Logger.d("permission stateios = ${CommonData.DEVICE_TYPE_WIFI_IOS}")
```

```diff
                          iosWifiLin2("ios")
                      }else{
-                         iosCalibrationLin()
+                         // 检查蓝牙设备是否与互联手机匹配
+                         val isBTPairedWithPhone = LinkSdkModel.isBluetoothPairedWithPhone()
+                         if (isBTPairedWithPhone) {
+                             handler.postDelayed({iosCalibrationLin()}, 100)
+                         } else {
+                             iosWifiLin2("ios")
+                         }
                      }
                      Logger.d("permission stateios = ${CommonData.DEVICE_TYPE_WIFI_IOS}")
```

---

I've updated the code to use `isBTPairedWithPhone` to determine if the Bluetooth device is paired with the connected phone:

1. In the `devicetypeObserver`, when an iOS device is connected over WiFi and Bluetooth devices are detected, instead of immediately calling `iosCalibrationLin()`, the code now:
   - Calls `LinkSdkModel.isBluetoothPairedWithPhone()` to check if the Bluetooth device is paired with the phone
   - If paired (`isBTPairedWithPhone` is true), it starts the calibration process with `iosCalibrationLin()`
   - If not paired, it falls back to `iosWifiLin2("ios")` which handles the WiFi connection without calibration

2. Removed the previous approach that was using `HidConstants.ON_HID_BT_PAIRED_WITH_PHONE` constant

This implementation ensures that the calibration process only starts when there's a confirmed match between the connected Bluetooth device and the phone, using the `isBluetoothPairedWithPhone()` method to make this determination.

---


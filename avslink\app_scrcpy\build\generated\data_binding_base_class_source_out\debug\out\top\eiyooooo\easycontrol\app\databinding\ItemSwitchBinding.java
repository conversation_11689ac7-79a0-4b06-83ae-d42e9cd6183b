// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ItemSwitchBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView itemDetail;

  @NonNull
  public final Switch itemSwitch;

  @NonNull
  public final TextView itemText;

  private ItemSwitchBinding(@NonNull LinearLayout rootView, @NonNull TextView itemDetail,
      @NonNull Switch itemSwitch, @NonNull TextView itemText) {
    this.rootView = rootView;
    this.itemDetail = itemDetail;
    this.itemSwitch = itemSwitch;
    this.itemText = itemText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSwitchBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSwitchBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_switch, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSwitchBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.item_detail;
      TextView itemDetail = ViewBindings.findChildViewById(rootView, id);
      if (itemDetail == null) {
        break missingId;
      }

      id = R.id.item_switch;
      Switch itemSwitch = ViewBindings.findChildViewById(rootView, id);
      if (itemSwitch == null) {
        break missingId;
      }

      id = R.id.item_text;
      TextView itemText = ViewBindings.findChildViewById(rootView, id);
      if (itemText == null) {
        break missingId;
      }

      return new ItemSwitchBinding((LinearLayout) rootView, itemDetail, itemSwitch, itemText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

<!-- Generated by SpecStory -->

# Implementing RegularCalibrationStrategy Method (2025-05-16 08:28Z)

_**User**_

实现这个类，参考这个 //LogUtil.d(TAG,"need move px ========== " + (target - current) * scale);
            // move = 300
            float move = Math.min(HidIosCalibrationManager.getStepMax(), (target - current) * scale);
            //LogUtil.d(TAG,"move 111 = " + move);
            if (move < 0) {
                move = Math.max(-HidIosCalibrationManager.getStepMax(), (target - current) * scale);
            }
            //LogUtil.d(TAG,"move 222 = " + move);
            if(!areFloatsEqual(Math.abs(move), HidIosCalibrationManager.getStepMax())){
                move = Math.min(HidIosCalibrationManager.getStepMid(), (target - current) * scale);
                if (move < 0) {
                    move = Math.max(-HidIosCalibrationManager.getStepMid(), (target - current) * scale);
                }

                if(!areFloatsEqual(Math.abs(move), HidIosCalibrationManager.getStepMid())){
                    move = Math.min(HidIosCalibrationManager.getStepMin(), (target - current) * scale);
                    if (move < 0) {
                        move = Math.max(-HidIosCalibrationManager.getStepMin(), (target - current) * scale);
                    }
                }
            }

            current += (move / scale);
            LogUtil.d(TAG,"move = " + move  + ",current = " + current);
            //--> 要移动Y
            if(movex) {
                if (!HidIosUtil.getVerticalScreen()) {
                    senMouse((byte) -getStepByPx(move), (byte) 0);
                } else {
                    senMouse((byte) 0, (byte) getStepByPx(move));
                }
            }else{
             //--> 要移动X
                if(!HidIosUtil.getVerticalScreen()) {
                    senMouse((byte)0, (byte)getStepByPx(move));
                } else {
                    senMouse((byte) getStepByPx(move), (byte)0);
                }
            }

---

_**Assistant**_

Looking at the provided code, I'll implement the `queryHidSendData` method for the `RegularCalibrationStrategy` class based on the reference code you provided.

---

```diff
      @Override
      public Pair<Integer, Float> queryHidSendData(float expectMovePixel) {
-         
+         float movePixel = expectMovePixel;
+         float actualMovePixel = movePixel;
+         
+         // 根据期望移动像素，选择合适的步长
+         if (Math.abs(movePixel) >= getStepMax()) {
+             actualMovePixel = movePixel > 0 ? getStepMax() : -getStepMax();
+         } else if (Math.abs(movePixel) >= getStepMid()) {
+             actualMovePixel = movePixel > 0 ? getStepMid() : -getStepMid();
+         } else {
+             actualMovePixel = movePixel > 0 ? 
+                               Math.min(getStepMin(), movePixel) : 
+                               Math.max(-getStepMin(), movePixel);
+         }
+         
+         // 计算HID发送数据，转换为整数
+         int hidData = getStepByPx(actualMovePixel);
+         
+         printLog("queryHidSendData: expectMovePixel=" + expectMovePixel + 
+                  ", actualMovePixel=" + actualMovePixel + 
+                  ", hidData=" + hidData);
+                  
+         return new Pair<>(hidData, actualMovePixel);
+     }
+     
+     /**
+      * 将像素值转换为HID发送数据
+      * @param pixelValue 像素值
+      * @return HID发送数据
+      */
+     private int getStepByPx(float pixelValue) {
+         // 简单实现，根据实际情况可能需要更复杂的转换逻辑
+         return (int) pixelValue;
      }
  
```

---

Your method has been implemented. The implementation follows the logic from your reference code:

1. It takes the expected pixel movement as input
2. Determines the appropriate step size (max, mid, or min) based on the magnitude of the movement
3. Calculates the actual pixel movement that will occur
4. Converts the pixel value to HID data units
5. Returns both the HID data and the actual pixel movement as a Pair

The `getStepByPx` helper method was also added to convert pixel values to HID units. Currently, it's a simple conversion, but you can modify it with a more complex transformation if needed.

---

_**User**_

参考当前类的定义，重新实现一下。

---

_**Assistant**_

I'll update the implementation to match the class's existing approach and add the required constants.

---

```diff
      // 最大步长
      private static float stepMax = 0;
+ 
+     // HID步长常量
+     private static final int MAX_STEP = 100;
+     private static final int MID_STEP = 50;
+     private static final int MIN_STEP = 10;
+     
+     // SP键值常量
+     private static final String SP_MAX_STEP = "SP_MAX_STEP";
+     private static final String SP_MID_STEP = "SP_MID_STEP";
+     private static final String SP_MIN_STEP = "SP_MIN_STEP";
+     
      // 是否强制重新计算 stepMin, stepMid, stepMax
      private static boolean forceRecalculateStep = false;
```

---

Now I'll update the `queryHidSendData` method:

---

```diff
      @Override
      public Pair<Integer, Float> queryHidSendData(float expectMovePixel) {
-         float movePixel = expectMovePixel;
-         float actualMovePixel = movePixel;
-         
-         // 根据期望移动像素，选择合适的步长
-         if (Math.abs(movePixel) >= getStepMax()) {
-             actualMovePixel = movePixel > 0 ? getStepMax() : -getStepMax();
-         } else if (Math.abs(movePixel) >= getStepMid()) {
-             actualMovePixel = movePixel > 0 ? getStepMid() : -getStepMid();
-         } else {
-             actualMovePixel = movePixel > 0 ? 
-                               Math.min(getStepMin(), movePixel) : 
-                               Math.max(-getStepMin(), movePixel);
-         }
-         
-         // 计算HID发送数据，转换为整数
+         float scale = 1.0f; // 缩放比例
+         float current = 0.0f; // 当前位置
+         float target = expectMovePixel; // 目标位置
+         
+         // 计算需要移动的像素
+         float move = Math.min(getStepMax(), (target - current) * scale);
+         
+         if (move < 0) {
+             move = Math.max(-getStepMax(), (target - current) * scale);
+         }
+         
+         if (!areFloatsEqual(Math.abs(move), getStepMax())) {
+             move = Math.min(getStepMid(), (target - current) * scale);
+             if (move < 0) {
+                 move = Math.max(-getStepMid(), (target - current) * scale);
+             }
+             
+             if (!areFloatsEqual(Math.abs(move), getStepMid())) {
+                 move = Math.min(getStepMin(), (target - current) * scale);
+                 if (move < 0) {
+                     move = Math.max(-getStepMin(), (target - current) * scale);
+                 }
+             }
+         }
+         
+         // 计算实际移动的像素
+         float actualMovePixel = move;
+         
+         // 计算HID发送数据
          int hidData = getStepByPx(actualMovePixel);
          
```

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java

---

I see there are now duplicate `getStepByPx` methods. Let's fix this:

---

Cancelled

---


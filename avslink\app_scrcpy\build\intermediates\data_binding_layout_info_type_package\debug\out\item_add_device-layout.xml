<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_add_device" modulePackage="top.eiyooooo.easycontrol.app" filePath="avslink\app_scrcpy\src\main\res\layout\item_add_device.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_add_device_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="135" endOffset="14"/></Target><Target id="@+id/name" view="EditText"><Expressions/><location startLine="15" startOffset="2" endLine="22" endOffset="41"/></Target><Target id="@+id/address_title" view="TextView"><Expressions/><location startLine="24" startOffset="2" endLine="31" endOffset="41"/></Target><Target id="@+id/address" view="EditText"><Expressions/><location startLine="38" startOffset="4" endLine="47" endOffset="43"/></Target><Target id="@+id/scan_address" view="Button"><Expressions/><location startLine="49" startOffset="4" endLine="60" endOffset="43"/></Target><Target id="@+id/specified_app_title" view="TextView"><Expressions/><location startLine="64" startOffset="2" endLine="71" endOffset="41"/></Target><Target id="@+id/specified_app" view="EditText"><Expressions/><location startLine="78" startOffset="4" endLine="87" endOffset="45"/></Target><Target id="@+id/scan_remote_app_list" view="Button"><Expressions/><location startLine="89" startOffset="4" endLine="100" endOffset="45"/></Target><Target id="@+id/is_options" view="CheckBox"><Expressions/><location startLine="105" startOffset="2" endLine="113" endOffset="41"/></Target><Target id="@+id/options" view="LinearLayout"><Expressions/><location startLine="115" startOffset="2" endLine="120" endOffset="31"/></Target><Target id="@+id/ok" view="Button"><Expressions/><location startLine="122" startOffset="2" endLine="133" endOffset="41"/></Target></Targets></Layout>
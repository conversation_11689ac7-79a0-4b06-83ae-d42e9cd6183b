package com.autoai.wdlink.hu.frame.utils

import android.bluetooth.BluetoothDevice
import android.content.Context
import android.os.Build
import android.util.Log
import com.autoai.wdlink.hu.Logger
import kotlin.properties.Delegates

object BtMusicFocusManager {
    private var mBtMusicFocusImpl: BtMusicFocusBase? = null

    //车机与手机是否连接成功
    var isConnectPhone: Boolean by Delegates.observable(false) { _, oldValue, newValue ->
        Log.d("BtMusicFocusManager", "set isConnectPhone from $oldValue to $newValue")
    }
    var isA2dpSinkConnect: Boolean by Delegates.observable(false) { _, oldValue, newValue ->
        Log.d("BtMusicFocusManager", "set isA2dpSinkConnect from $oldValue to $newValue")
    }


    /**
     * context: 单例类中谨慎使用Context
     */
    fun init(context: Context) {
        Log.d("BtMusicFocusManager", "initVehicleType ${Build.MODEL}")
        val name = Build.MODEL
        when {
            name.startsWith("XPENG") -> {
                mBtMusicFocusImpl = BtMusicFocusImpl(context.applicationContext)
                mBtMusicFocusImpl?.init()
            }
            else -> {
                //
            }
        }
    }

    /**
     * 请求蓝牙音频焦点
     * device: Mona车机可传null
     */
    fun requestBtMusicFocus(device: BluetoothDevice?) {
        if (!isConnectPhone) return
        Log.d("BtMusicFocusManager", "requestBtMusicFocus ")
        mBtMusicFocusImpl?.requestBtMusicFocus(device)
    }

    /**
     * 放弃蓝牙音频焦点
     * device: Mona车机可传null
     */
    fun abandonBtMusicFocus(device: BluetoothDevice?) {
        if (!isConnectPhone) return
        Log.d("BtMusicFocusManager", "abandonBtMusicFocus")
        mBtMusicFocusImpl?.abandonBtMusicFocus(device)
    }

}
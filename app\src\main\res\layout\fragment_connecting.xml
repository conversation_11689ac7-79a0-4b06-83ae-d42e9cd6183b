<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_centerInParent="true"
    android:gravity="center"
    android:orientation="vertical"
    android:layout_marginLeft="@dimen/margin_title_left"
    android:layout_marginRight="@dimen/margin_title_right"
    tools:background="@drawable/bg_color">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/connecting_title"
        android:textColor="@color/text_color"
        android:textSize="@dimen/title_text_size"
        android:textStyle="bold" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_title_top"
        android:gravity="center"
        android:text="@string/connecting_tip1"
        android:textColor="@color/text_color"
        android:textSize="@dimen/tips_text_size"
        tools:ignore="SmallSp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/connecting_tip2"
        android:textColor="@color/text_color"
        android:textSize="@dimen/tips_text_size"
        tools:ignore="SmallSp" />
</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="top.eiyooooo.easycontrol.app" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.INTERNET" /> <!-- 网络 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- 悬浮窗 -->
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.USB_PERMISSION" /> <!-- USB -->
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> <!-- 桌面快捷方式 -->
    <uses-permission
        android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions" /> <!-- 使用统计 -->

    <uses-feature android:name="android.hardware.usb.host" />

    <!-- <application -->
    <!-- android:name=".helper.MyApplication" -->
    <!-- android:icon="@mipmap/ic_launcher" -->
    <!-- android:installLocation="internalOnly" -->
    <!-- android:label="@string/app_name" -->
    <!-- android:roundIcon="@mipmap/ic_launcher_round" -->
    <!-- android:supportsRtl="true" -->
    <!-- android:theme="@android:style/Theme.DeviceDefault.Light.NoActionBar" -->
    <!-- android:usesCleartextTraffic="true" -->
    <!-- tools:targetApi="31"> -->
    <application
        android:hardwareAccelerated="true"
        android:usesCleartextTraffic="true"
        tools:targetApi="31" >
        <activity
            android:name="top.eiyooooo.easycontrol.app.MainActivity"
            android:configChanges="keyboardHidden|screenSize|orientation"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
            </intent-filter>
        </activity>
        <activity
            android:name="top.eiyooooo.easycontrol.app.MonitorActivity"
            android:configChanges="keyboardHidden|screenSize|orientation|uiMode"
            android:exported="false" />
        <activity
            android:name="top.eiyooooo.easycontrol.app.IpActivity"
            android:exported="false" />
        <activity
            android:name="top.eiyooooo.easycontrol.app.LogActivity"
            android:exported="false" />
        <activity
            android:name="top.eiyooooo.easycontrol.app.WebViewActivity"
            android:exported="false" />
        <activity
            android:name="top.eiyooooo.easycontrol.app.PairActivity"
            android:exported="false" />
        <activity
            android:name="top.eiyooooo.easycontrol.app.SetActivity"
            android:configChanges="keyboardHidden|screenSize|orientation|uiMode"
            android:exported="false" />
        <activity
            android:name="top.eiyooooo.easycontrol.app.client.view.FullActivity"
            android:exported="false"
            android:hardwareAccelerated="true" />
        <activity
            android:name="top.eiyooooo.easycontrol.app.AdbKeyActivity"
            android:exported="false" />
        <activity
            android:name="top.eiyooooo.easycontrol.app.StartDeviceActivity"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:theme="@style/Transparent" />
    </application>

</manifest>
package com.autoai.wdlink.hu.view

import android.net.Uri
import android.os.Bundle
import android.view.View
import android.widget.Toast
import com.autoai.common.util.LogUtil
import com.autoai.wdlink.hu.databinding.FragmentInitBinding
import com.autoai.wdlink.hu.frame.BaseFragment
import com.autoai.wdlink.hu.sdk.LinkSdkModel
import com.autoai.wdlink.hu.viewmodel.InitViewModel

/**
 * 初始页面
 * */
class InitFragment : BaseFragment<InitViewModel, FragmentInitBinding>(FragmentInitBinding::inflate) {

    private var develop = 0
    private var debugOpen = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.tvInitClickHere.setOnClickListener {
            viewModel.help()
        }
//        activityViewModel.stopForLink(requireActivity())
//        PopWinServiceUtils.stopPopWinService(requireContext())
        binding.closeIcon.setOnClickListener {
            activity?.apply {
                finish()
            }
        }
        binding.icInitLogo.setOnClickListener {
            develop++
        }

        binding.icInitLogo.setOnLongClickListener {
            if (develop > 5) {
                debugOpen = !debugOpen
                LinkSdkModel.logAble(logDebug =true, fileDebug = false)
                LogUtil.setIsFullLog(true)

                Toast.makeText(
                    context,
                    "日志输出已" + if (debugOpen) "打开" else "关闭",
                    Toast.LENGTH_SHORT
                ).show()
                develop = 0
                true
            } else{
                develop = 0
                false
            }
        }
    }

    override fun onResume() {
        super.onResume()
//        activityViewModel.fullScreen = false
//        activityViewModel.fullScreen()
//        MainActivityViewModel.readyForLink(requireActivity())
//        PopWinServiceUtils.startPopWinService(requireContext())
    }

    override fun getViewModelClass() = InitViewModel::class

    override fun getViewBindingClass() = FragmentInitBinding::class
}
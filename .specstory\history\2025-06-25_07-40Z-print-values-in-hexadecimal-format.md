<!-- Generated by SpecStory -->

# Print values in hexadecimal format (2025-06-25 07:40Z)

_**User**_

+ ", dx:" + dx + ", dy:" + dy + ",  ， 这个打印，改成 16 进制的，也打印一下。

---

_**Assistant**_

I'll modify the log statement to include the hex representation of dx and dy values.

---



---

I've updated the log statement to display the hexadecimal representation of the dx and dy values. Now when the senMouse method logs data, it will show both the decimal and hexadecimal formats for better debugging.

---

_**User**_

10 进制放在一起，16 进制，一起打印。

---

_**Assistant**_

I'll modify the log format to group decimal values together and hexadecimal values together.

---



---

I've updated the log format to group values more logically. Now the decimal values (dx, dy) are displayed together first, followed by their hexadecimal representations in square brackets [0xXX,0xYY]. This makes the log easier to read while keeping related values grouped together.

---

_**User**_

  private boolean hidThrEvent(int pointCount,float x, float y, int actionMask) { 抬起的时候， 定时 20ms 一次发送  senMouse((byte) 0, (byte) 0);  然后再次抬起的时候， 取消这个定时。 然后再次抬起的时候 再定时发送。需要一次发送，另外依次取消，然后再发送。

---

_**Assistant**_

I'll implement the requested functionality to send periodic mouse events when touch is released, cancel the timer on the next touch release, and restart it on subsequent releases.

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java

---

I'll add the functionality to send periodic mouse events on touch release and manage the timer.

---

```diff
      private Runnable distanceRunnable;
      
+     // 定时发送(0,0)鼠标事件的Runnable
+     private Runnable periodicMouseSendRunnable;
+     // 是否正在发送周期性的鼠标事件
+     private boolean isPeriodicMouseSendActive = false;
+     // 周期性发送鼠标事件的时间间隔
+     private static final int PERIODIC_MOUSE_SEND_DELAY = 20; // 20ms
+ 
      /**
       * 手势监听，用于识别各种手势，转化成特殊事件
```

```diff
                      cancelTimer(heartBeatRunnable);
                      cancelTimer(distanceRunnable);
+                     cancelPeriodicMouseSend();
                      distanceRunFlag = false;
                      break;
```

```diff
          }
      }
+ 
+     /**
+      * 开始周期性发送(0,0)鼠标事件
+      */
+     private void startPeriodicMouseSend() {
+         // 如果已经在运行，先取消之前的任务
+         cancelPeriodicMouseSend();
+         
+         // 创建新的周期性发送任务
+         periodicMouseSendRunnable = new Runnable() {
+             @Override
+             public void run() {
+                 if (isBTHIDConnected) {
+                     // 发送(0,0)鼠标事件
+                     senMouse((byte) 0, (byte) 0);
+                     LogUtil.d(TAG, "Periodic mouse send: sending (0,0)");
+                     
+                     // 安排下一次发送
+                     handler.postDelayed(this, PERIODIC_MOUSE_SEND_DELAY);
+                 } else {
+                     // 如果蓝牙已断开，停止发送
+                     isPeriodicMouseSendActive = false;
+                 }
+             }
+         };
+         
+         // 开始第一次发送
+         handler.post(periodicMouseSendRunnable);
+         isPeriodicMouseSendActive = true;
+         LogUtil.d(TAG, "Started periodic mouse send");
+     }
+     
+     /**
+      * 取消周期性发送鼠标事件
+      */
+     private void cancelPeriodicMouseSend() {
+         if (periodicMouseSendRunnable != null) {
+             handler.removeCallbacks(periodicMouseSendRunnable);
+             periodicMouseSendRunnable = null;
+             isPeriodicMouseSendActive = false;
+             LogUtil.d(TAG, "Cancelled periodic mouse send");
+         }
+     }
+     
+     /**
+      * 切换周期性发送鼠标事件的状态
+      * 如果已经在发送，则取消；如果没有在发送，则开始发送
+      */
+     private void togglePeriodicMouseSend() {
+         if (isPeriodicMouseSendActive) {
+             cancelPeriodicMouseSend();
+         } else {
+             startPeriodicMouseSend();
+         }
+     }
  
      /**
```

```diff
          if (pointCount > 1) {
              LogUtil.d(TAG, "senMouse hidThrEvent pointCount:" + pointCount);
- 
              return true;
          }
```

```diff
  
          LogUtil.e(TAG, "senMouse event.getPointerCount() = " + pointCount + ", getAction = " + actionMask + " ( " + x + ", " + y + " y)" + " ThreadId: " + Thread.currentThread().getId());
-         //--> 正式的转化逻辑
- //         switch (actionMask) {
- //             case MotionEvent.ACTION_DOWN:
- //                 printBase(x,y);
- //                 moveToTarget(x, y);
- //                 //--> 如果在IOS 底部居中的home键区域，模拟home
- //                 if(HidIosUtil.isClickBtn(x, y)){
- //                     hidSendHomeKey();
- //                 }
- 
- //                 mLeftClick =  false;
- //                 // 可能出现没有移动到位置的时候，就按下的效果。 因此移动后，停留一下再按下。
- //                 sleepTime(20);
- //                 break;
- //             case MotionEvent.ACTION_POINTER_DOWN:
- //             case MotionEvent.ACTION_MOVE:
- // //                if(!motionActionMove(false, pointCount, x, y)) {
- // //                    upPosition = false;
- // //                }
- //                 break;
- //             case MotionEvent.ACTION_CANCEL:
- //                 //Log.e( "IIIIIIII",   "!!!!! sendLeftClick true ACTION_CANCEL " + " Thread: " + Thread.currentThread());
- //                 // fixme:是为了模拟一次点击？ guch: 答复是收到 cancel 的话，肯定是有一次 action_down。
- //                 sendLeftClick(true);
- //             case MotionEvent.ACTION_UP:
- //                 motionActionUp(x, y);
- //                 break;
- //             case MotionEvent.ACTION_POINTER_UP:
- //             default:
- //                 break;
- //         }
- //         LogUtil.d(TAG, "onTouch over-------------");
- //         if(upPosition) {
- //             printBase(x, y);
- //         }
+         
+         // 处理抬起事件，触发周期性发送鼠标事件
+         if (actionMask == MotionEvent.ACTION_UP || actionMask == MotionEvent.ACTION_CANCEL) {
+             // 切换周期性发送状态（开始/停止）
+             togglePeriodicMouseSend();
+             LogUtil.d(TAG, "Touch up/cancel detected - toggled periodic mouse send, current state: " + isPeriodicMouseSendActive);
+         }
+         
          return true;
      }
```

---

I've implemented the requested functionality:

1. Added a periodic mouse event sender that will send (0,0) mouse events every 20ms
2. Created a toggle mechanism that:
   - Starts the periodic sending when a touch is released (ACTION_UP or ACTION_CANCEL)
   - Cancels the ongoing periodic sending on the next touch release
   - Restarts it on subsequent touch releases

This implementation allows the following behavior:
- First touch release: Start sending (0,0) every 20ms
- Second touch release: Stop sending
- Third touch release: Start sending again
- And so on...

The periodic sending also automatically stops if Bluetooth is disconnected, and it gets properly canceled when other timers are canceled via the ACTION_CANCEL_ALL action.

All activity is logged for debugging purposes.

---


// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ItemDevicesItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView deviceExpand;

  @NonNull
  public final ImageView deviceIcon;

  @NonNull
  public final TextView deviceName;

  private ItemDevicesItemBinding(@NonNull LinearLayout rootView, @NonNull ImageView deviceExpand,
      @NonNull ImageView deviceIcon, @NonNull TextView deviceName) {
    this.rootView = rootView;
    this.deviceExpand = deviceExpand;
    this.deviceIcon = deviceIcon;
    this.deviceName = deviceName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDevicesItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDevicesItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_devices_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDevicesItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.device_expand;
      ImageView deviceExpand = ViewBindings.findChildViewById(rootView, id);
      if (deviceExpand == null) {
        break missingId;
      }

      id = R.id.device_icon;
      ImageView deviceIcon = ViewBindings.findChildViewById(rootView, id);
      if (deviceIcon == null) {
        break missingId;
      }

      id = R.id.device_name;
      TextView deviceName = ViewBindings.findChildViewById(rootView, id);
      if (deviceName == null) {
        break missingId;
      }

      return new ItemDevicesItemBinding((LinearLayout) rootView, deviceExpand, deviceIcon,
          deviceName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

package com.autoai.wdlink.hu.updata;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.util.Log;

import androidx.core.content.FileProvider;

import com.autoai.wdlink.hu.BuildConfig;
import com.autoai.avs.common.sota.api.SotaApiManager;
import com.autoai.avs.common.sota.api.SotaListener;
import com.autoai.avs.common.sota.constant.PackageType;
import com.autoai.avs.common.sota.model.SotaAppInfo;
import com.autoai.avs.common.sota.model.SotaDeviceInfo;
import com.autoai.avs.common.sota.model.SotaUploadInfo;
import com.autoai.avs.common.sota.util.ToastUtil;
import com.autoai.wdlink.hu.MainActivity;
import com.autoai.wdlink.hu.R;
import com.autoai.wdlink.hu.dialog.UpVersionDialog;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class HWSotaApiManager {

    private static final String TAG = "HWSotaApiManager";

    private static SotaApiManager mSotaApiManager = null;

    private static HWSotaApiManager instance = null;

    public static MainActivity activity;
    private String cachePath;

    public static synchronized HWSotaApiManager getInstance() {
        if (instance == null) {
            instance = new HWSotaApiManager();
        }
        return instance;
    }

    public void init(MainActivity activity) {

        this.activity = activity;

        //第二步 创建SotaApiManager 对象，设置相关属性  以下步骤放在什么地方执行无要求
        if (mSotaApiManager == null) {
            mSotaApiManager = new SotaApiManager(activity, mSotaListeners);
        }
        //设置服务端地址，不设置使用默认
       // mSotaApiManager.setBaseUrl("https://test-wecockpit.autoai.com/");

        //打开日志 sota sdk目前日志跟随commonlib日志向上层应用抛出，由上层应用接收到日志后进行保存处理。
        //mSotaApiManager.setShowLog(true);

        //设置下载路径
        if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())
                || !Environment.isExternalStorageRemovable()) {

            cachePath =activity.getExternalCacheDir().getPath();
        } else {

            cachePath = activity.getFilesDir().getAbsolutePath();
        }

        mSotaApiManager.setDownloadPath(cachePath);
        //第三步 创建 SotaDeviceInfo 对象
        SotaDeviceInfo mSotaDeviceInfo = new SotaDeviceInfo();
        //设备唯一标识  必填，非空
        mSotaDeviceInfo.setDeviceKey("deviceId");
        //设备唯一标识  必填，非空
        mSotaDeviceInfo.setVin("vin");
        //城市code   必填，非空
        mSotaDeviceInfo.setCity("city");
        //系统版本  必填，非空
        mSotaDeviceInfo.setKernelVersion(1);
        //内核显示版本  必填，非空
        mSotaDeviceInfo.setKernelVersionName("kernelVersionName");
        //语言编码  必填，非空
        mSotaDeviceInfo.setLanguage("language");
        //车型code  必填，非空
        mSotaDeviceInfo.setUnicode("unicode");
        //租户code  必填，非空
        mSotaDeviceInfo.setTenant("code");
        //用户ID   非必填，可为空
        mSotaDeviceInfo.setUserId("");
        //用户名称   非必填，可为空
        mSotaDeviceInfo.setUserName("");
        //Uuid   非必填，忽略该字段
        mSotaDeviceInfo.setUuid("");

        //第四步 启动sota sdk
        mSotaApiManager.start(mSotaDeviceInfo);

        //第五步 检查更新
        List<SotaAppInfo> list = new ArrayList<>();
        SotaAppInfo appInfo = new SotaAppInfo();
        appInfo.setPackageName(activity.getPackageName());
        appInfo.setVersion(BuildConfig.VERSION_CODE);
        appInfo.setType(PackageType.APPLICATION);
        mSotaApiManager.checkUpdate(list);

        //暂停下载
       // mSotaApiManager.pauseDownload(new SotaAppInfo());
        //第七步 升级结果通知
        mSotaApiManager.setUploadResult(appInfo, 2, activity.getResources().getString(R.string.updata_success));
    }
    @SuppressLint("StaticFieldLeak")
    private static UpVersionDialog dialog;
    //sota 相关回调
    public static SotaListener mSotaListeners = new SotaListener() {
        //初始化结果回调
        @Override
        public void onInitResult(boolean b, int i) {
            Log.e(TAG, "onInitResult:  " + b + "   i  = "  + i );
        }
        //检查更新结果回调
        @Override
        public void onCheckUpdateResult(boolean b, int i, List<SotaUploadInfo> list) {
            if (b) {
                dialog = new UpVersionDialog(activity);
                dialog.setOnCanlceListener(v -> {
                    dialog.dismiss();
                    activity.finish();
                });
                dialog.setOnUpListener(view -> {
                    //第六步 下载
                    String packageName = list.get(0).getPackageName();
                    PackageType packageType = list.get(0).getPackageType();
                    int updateVersion = list.get(0).getUpdateVersion();
                    SotaAppInfo appInfo = new SotaAppInfo();
                    appInfo.setType(packageType);
                    appInfo.setVersion(updateVersion);
                    appInfo.setPackageName(packageName);
                    mSotaApiManager.download(appInfo);

                });

                dialog.show();
            }
        }
        //下载进度回调
        @Override
        public void onDownloading(SotaAppInfo sotaAppInfo, float v) {

            dialog.setTextListener(activity.getResources().getString(R.string.updata_version), "" + v, (int) v);
        }

        //下载结果回调
        @Override
        public void onDownloadResult(boolean b, String s, int i, String s1, String s2) {
            if (b){
                installApk(activity,s1);
            }else {
                ToastUtil.showToast(activity.getResources().getString(R.string.updata_fail));
            }
        }
        //升级结果上报回调
        @Override
        public void onSetUploadResult(boolean b, int i) {

        }
    };

    // 安装apk
    private static void installApk(Context context,String path) {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        File file = new File(Uri.parse(path).getPath());
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            Uri uri = FileProvider.getUriForFile(context, "com.autoai.wdlink.hu.updata.HwFileProvider", file);

            intent.setDataAndType(uri, "application/vnd.android.package-archive");
        } else {
            intent.setDataAndType(Uri.fromFile(file), "application/vnd.android.package-archive");
        }
        context.startActivity(intent);
    }
}

1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="top.eiyooooo.easycontrol.app" >
5
6    <uses-sdk
7        android:minSdkVersion="21"
7-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml
8        android:targetSdkVersion="34" />
8-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml
9
10    <uses-permission android:name="android.permission.INTERNET" /> <!-- 网络 -->
10-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:5:5-67
10-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:5:22-64
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:6:5-79
11-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:6:22-76
12    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- 悬浮窗 -->
12-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:7:5-78
12-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:7:22-75
13    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
13-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:8:5-80
13-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:8:22-77
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:9:5-76
14-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:9:22-73
15    <uses-permission android:name="android.permission.USB_PERMISSION" /> <!-- USB -->
15-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:10:5-73
15-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:10:22-70
16    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> <!-- 桌面快捷方式 -->
16-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:11:5-88
16-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:11:22-85
17    <uses-permission
17-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:12:5-14:47
18        android:name="android.permission.PACKAGE_USAGE_STATS"
18-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:13:9-62
19        tools:ignore="ProtectedPermissions" /> <!-- 使用统计 -->
19-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:14:9-44
20
21    <uses-feature android:name="android.hardware.usb.host" />
21-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:16:5-62
21-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:16:19-59
22
23    <!-- <application -->
24    <!-- android:name=".helper.MyApplication" -->
25    <!-- android:icon="@mipmap/ic_launcher" -->
26    <!-- android:installLocation="internalOnly" -->
27    <!-- android:label="@string/app_name" -->
28    <!-- android:roundIcon="@mipmap/ic_launcher_round" -->
29    <!-- android:supportsRtl="true" -->
30    <!-- android:theme="@android:style/Theme.DeviceDefault.Light.NoActionBar" -->
31    <!-- android:usesCleartextTraffic="true" -->
32    <!-- tools:targetApi="31"> -->
33    <application
33-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:28:5-75:19
34        android:hardwareAccelerated="true"
34-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:31:9-43
35        android:usesCleartextTraffic="true"
35-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:29:9-44
36        tools:targetApi="31" >
36-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:30:9-29
37        <activity
37-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:32:9-41:20
38            android:name="top.eiyooooo.easycontrol.app.MainActivity"
38-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:33:13-41
39            android:configChanges="keyboardHidden|screenSize|orientation"
39-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:34:13-74
40            android:exported="true" >
40-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:35:13-36
41            <intent-filter>
41-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:36:13-40:29
42                <action android:name="android.intent.action.MAIN" />
42-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:37:17-69
42-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:37:25-66
43
44                <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
45            </intent-filter>
46        </activity>
47        <activity
47-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:42:9-45:40
48            android:name="top.eiyooooo.easycontrol.app.MonitorActivity"
48-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:43:13-44
49            android:configChanges="keyboardHidden|screenSize|orientation|uiMode"
49-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:44:13-81
50            android:exported="false" />
50-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:45:13-37
51        <activity
51-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:46:9-48:40
52            android:name="top.eiyooooo.easycontrol.app.IpActivity"
52-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:47:13-39
53            android:exported="false" />
53-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:48:13-37
54        <activity
54-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:49:9-51:40
55            android:name="top.eiyooooo.easycontrol.app.LogActivity"
55-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:50:13-40
56            android:exported="false" />
56-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:51:13-37
57        <activity
57-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:52:9-54:40
58            android:name="top.eiyooooo.easycontrol.app.WebViewActivity"
58-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:53:13-44
59            android:exported="false" />
59-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:54:13-37
60        <activity
60-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:55:9-57:40
61            android:name="top.eiyooooo.easycontrol.app.PairActivity"
61-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:56:13-41
62            android:exported="false" />
62-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:57:13-37
63        <activity
63-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:58:9-61:40
64            android:name="top.eiyooooo.easycontrol.app.SetActivity"
64-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:59:13-40
65            android:configChanges="keyboardHidden|screenSize|orientation|uiMode"
65-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:60:13-81
66            android:exported="false" />
66-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:61:13-37
67        <activity
67-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:62:9-65:50
68            android:name="top.eiyooooo.easycontrol.app.client.view.FullActivity"
68-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:63:13-53
69            android:exported="false"
69-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:64:13-37
70            android:hardwareAccelerated="true" />
70-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:65:13-47
71        <activity
71-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:66:9-68:40
72            android:name="top.eiyooooo.easycontrol.app.AdbKeyActivity"
72-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:67:13-43
73            android:exported="false" />
73-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:68:13-37
74        <activity
74-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:69:9-73:50
75            android:name="top.eiyooooo.easycontrol.app.StartDeviceActivity"
75-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:70:13-48
76            android:exported="true"
76-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:71:13-36
77            android:hardwareAccelerated="true"
77-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:72:13-47
78            android:theme="@style/Transparent" />
78-->D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\AndroidManifest.xml:73:13-47
79    </application>
80
81</manifest>

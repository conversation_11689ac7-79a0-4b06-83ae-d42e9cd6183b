package com.autoai.wdlink.hu.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.autoai.wdlink.hu.R;

public class UpVersionDialog extends Dialog {
    private Context mContext;
    private View inflate;
    private TextView tv_text;
    private ProgressBar progressBar;
    private TextView tv_progressBar;
    private TextView tv_cancel;
    private TextView tv_upgrade;

    public UpVersionDialog(@NonNull Context context) {
        super(context);
        this.mContext = context;
        initView();
    }

    private void initView() {
        inflate = LayoutInflater.from(mContext).inflate(R.layout.upversion_layout, null);
        tv_text = inflate.findViewById(R.id.tv_text);
        progressBar = inflate.findViewById(R.id.progressBar);
        tv_progressBar = inflate.findViewById(R.id.tv_progressBar);
        tv_cancel = inflate.findViewById(R.id.tv_cancel);
        tv_upgrade = inflate.findViewById(R.id.tv_upgrade);


    }
    //取消键监听器
    public void setOnCanlceListener(View.OnClickListener listener) {
        tv_cancel.setOnClickListener(listener);

    }
    //更新键监听器
    public void setOnUpListener(View.OnClickListener listener) {
        tv_upgrade.setOnClickListener(listener);
    }
    //内容
    public void setTextListener(String text, String code,int progress) {
        tv_text.setText(text);
        tv_progressBar.setText(code);
        progressBar.setProgress(progress);
    }

}

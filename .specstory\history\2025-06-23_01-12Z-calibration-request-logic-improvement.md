<!-- Generated by SpecStory -->

# Calibration request logic improvement (2025-06-23 01:12Z)

_**User**_

需求是互联成功后，应用层会调用 requestCalibration, 如果isBTHIDConnected =true, 就直接调用ON_TOUCH_POINT_REQ_SUCCESS函数（保持原有逻辑）。 如果是 false， 则等待 public void onConnected() { 的回调， 如果回调了，并且requestCalibration 也请求了，则回到的时候调用 ON_TOUCH_POINT_REQ_SUCCESS。 否则五秒过后 走ON_TOUCH_POINT_REQ_FAIL。 请理解一下我的需求。 并详细输出，等待我的确认，不要先修改。

---

_**Assistant**_

我理解你的需求是改进 `requestCalibration()` 方法的逻辑，具体如下：

1. 当 `isBTHIDConnected = true` 时：
   - 保持原有逻辑，直接调用 `onTouchPointRequest(ON_TOUCH_POINT_REQ_SUCCESS)`

2. 当 `isBTHIDConnected = false` 时：
   - 不立即调用 `onTouchPointRequest(ON_TOUCH_POINT_REQ_FAIL)`
   - 而是等待 `onConnected()` 回调
   - 如果在5秒内收到 `onConnected()` 回调，则调用 `onTouchPointRequest(ON_TOUCH_POINT_REQ_SUCCESS)`
   - 如果5秒内没有收到 `onConnected()` 回调，则调用 `onTouchPointRequest(ON_TOUCH_POINT_REQ_FAIL)`

这需要添加一个标志来记录是否调用过 `requestCalibration()`，以及一个计时器来处理5秒超时的情况。

实现思路：
1. 添加一个布尔变量 `waitingForConnection` 来标记是否正在等待连接
2. 添加一个 `Handler` 和 `Runnable` 来处理5秒超时
3. 修改 `requestCalibration()` 方法来实现上述逻辑
4. 修改 `onConnected()` 回调方法来检查是否需要调用 `onTouchPointRequest(ON_TOUCH_POINT_REQ_SUCCESS)`

这样做可以避免在连接尚未建立时就返回失败，给予连接一定的时间窗口来完成。

是否需要我进一步解释或修改这个实现思路？

---

_**User**_

当前这个avslinkhid 库中是否知道 这个 互联连接还是互联断开(WL_API_SDK_MSG_TYPE.CONNECTED:)？ 需要重置上面的操作符。 请再次分析  并帮我规划，每规划等待确认。

---

_**Assistant**_

让我分析一下当前代码中的连接状态管理机制：

在 `BluetoothHidManager` 类中，连接状态是通过 `isBTHIDConnected` 布尔变量来跟踪的，它在以下地方被修改：

1. 在 `connectionStateChangeListener` 的回调中:
   - `onConnected()` 回调中设置 `isBTHIDConnected = true`
   - `onDisConnected()` 回调中设置 `isBTHIDConnected = false`

2. `HidScreenConnectManager.getInstance()` 负责管理连接，并通过注册的监听器通知连接状态变化。

看起来 `BluetoothHidManager` 类已经有监听连接状态的机制，但它没有直接与你提到的 `WL_API_SDK_MSG_TYPE.CONNECTED` 交互。

基于这个分析，我的实现思路是：

1. 添加一个布尔变量 `pendingCalibrationRequest` 来标记是否有待处理的校准请求
2. 添加一个 `Handler` 和 `Runnable` 来处理5秒超时
3. 修改 `requestCalibration()` 方法：
   - 如果 `isBTHIDConnected = true`，保持原有逻辑
   - 如果 `isBTHIDConnected = false`，设置 `pendingCalibrationRequest = true` 并启动5秒倒计时
4. 修改 `onConnected()` 回调：
   - 如果 `pendingCalibrationRequest = true`，调用 `onTouchPointRequest(ON_TOUCH_POINT_REQ_SUCCESS)` 并重置标志
   - 取消倒计时

这样，如果在调用 `requestCalibration()` 后5秒内收到连接成功的回调，就会调用 `onTouchPointRequest(ON_TOUCH_POINT_REQ_SUCCESS)`，否则超时后调用 `onTouchPointRequest(ON_TOUCH_POINT_REQ_FAIL)`。

是否需要我进一步调整这个思路？

---


package com.autoai.wdlink.hu.view

import android.content.Intent
import android.os.Bundle
import android.view.View
import com.autoai.wdlink.hu.databinding.FragmentExitBinding
import com.autoai.wdlink.hu.frame.BaseFragment
import com.autoai.wdlink.hu.viewmodel.ExitViewModel
import com.autoai.welinkapp.model.ButtonType

/**
 * 连接中断了
 * */
class ExitFragment :
    BaseFragment<ExitViewModel, FragmentExitBinding>(FragmentExitBinding::inflate) {

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.tvHeartbeatErrClickHere.setOnClickListener {
            activityViewModel.sendButton(ButtonType.CLICK_FOR_HELP, 0)
        }
        binding.closeIcon.setOnClickListener {
            activity?.apply {
                finish()
            }
        }
    }

    override fun onResume() {
        super.onResume()
//        activityViewModel.fullScreen = false
//        activityViewModel.fullScreen()
//        activityViewModel.stopForLink(requireActivity())
    }

    override fun getViewModelClass() = ExitViewModel::class

    override fun getViewBindingClass() = FragmentExitBinding::class
}
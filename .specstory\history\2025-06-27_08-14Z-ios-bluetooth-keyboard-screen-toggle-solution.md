<!-- Generated by SpecStory -->

# iOS Bluetooth keyboard screen toggle solution (2025-06-27 08:14Z)

_**User**_

@https://github.com/orgs/espruino/discussions/6552  这里是啥方案。翻译成中文，只要翻译网页中的内容。

---

_**Assistant**_

我来翻译这个GitHub讨论的内容，重点关注网页中的解决方案。

## iOS蓝牙HID键盘连接时切换屏幕键盘的方法 - 已解决！

### 讨论内容

**原始问题（由sammachin发布于2021-07-30）：**

我正在设置一个Puck设备作为与iPhone连接的蓝牙键盘，目前工作正常。但iOS有一个烦人的习惯，当连接了硬件键盘时会禁用屏幕键盘。

在苹果键盘上，可以使用弹出键(eject key)来切换屏幕键盘，我相信这个键的键码是0x92。我想能够从我的Puck设备发送这个命令，这样我就不必每次都在蓝牙中断开键盘连接。

有什么建议吗？我尝试了`NRF.sendHIDReport([0,0,146,0,0,0,0,0]`和`NRF.sendHIDReport([0,0,0x92,0,0,0,0,0]`但没有成功。

**解决方案（最终由sammachin发布）：**

成功了！经过更多的实验（又名暴力破解），我设法弄清楚了如何调用第二个报告。这段代码将添加在iOS设备上切换屏幕键盘的能力，当从espruino连接到蓝牙HID键盘时。

我们扩展了内置键盘的报告，添加了额外的代码，然后使用services设置它，这样你仍然可以使用ble_hid_keyboard的所有现有功能，比如`kb.tap(kb.KEY.A, 0);`

我已经在运行2.09版本的pixl.js上测试了这个代码，连接到运行iOS 14.6的iPhone上，所以它可以工作，直到苹果决定改变什么。

```javascript
var kb = require("ble_hid_keyboard");
togleReport = new Uint8Array([
    // 第二个报告用于iOS切换键盘
    0x05, 0x0C,                     // 使用页面（消费者）
    0x09, 0x01,                     // 使用（消费者控制）
    0xA1, 0x01,                     // 集合（应用）
    0x85, 0x02,                     //     报告ID (2)
    0x15, 0x00,                     //     逻辑最小值 (0)
    0x25, 0x01,                     //     逻辑最大值 (1)
    0x75, 0x01,                     //     报告大小 (1)
    0x95, 0x01,                     //     报告计数 (1)
    0x0A, 0xAE, 0x01,               //     使用 (AL 键盘布局)
    0x81, 0x06,                     //     输入（数据,值,相对,位字段）
    0xC0                            // 结束集合
]);

// 将新报告附加到内置kb报告
var report = new Uint8Array(kb.report.length + togleReport.length);
report.set(kb.report);
report.set(togleReport, kb.report.length);

// 使用新的组合报告设置服务
NRF.setServices(undefined, { hid : report });

// 调用此函数来切换屏幕键盘
function toggleKB(){
  NRF.sendHIDReport([2,1], function() {
    NRF.sendHIDReport([2,0], function() {});
   });
}
```

**后续讨论：**

1. 作者（@gfwilliams）表示这个解决方案很棒，并询问是否可以将这段代码放在BLE键盘页面上，因为它很有用。

2. sammachin同意了，并提到还有其他一些AL键可能对iOS做有趣的事情。他还发现发送键盘修饰符17可以切换语音输入键盘。

### 总结

这个讨论解决了如何在iOS设备上通过蓝牙HID键盘接口切换屏幕键盘的问题。解决方案是创建一个自定义HID报告描述符，使用AL键盘布局控制（Consumer Page中的功能），然后发送特定序列的HID报告来触发iOS屏幕键盘的显示/隐藏。这对于需要在硬件键盘和屏幕键盘之间切换的用户非常有用。

---


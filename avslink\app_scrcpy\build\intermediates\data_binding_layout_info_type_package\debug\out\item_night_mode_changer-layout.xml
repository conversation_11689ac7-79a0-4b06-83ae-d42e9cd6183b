<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_night_mode_changer" modulePackage="top.eiyooooo.easycontrol.app" filePath="avslink\app_scrcpy\src\main\res\layout\item_night_mode_changer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/item_night_mode_changer_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="100" endOffset="13"/></Target><Target id="@+id/title" view="TextView"><Expressions/><location startLine="19" startOffset="4" endLine="28" endOffset="32"/></Target><Target id="@+id/button_layout" view="GridLayout"><Expressions/><location startLine="30" startOffset="4" endLine="96" endOffset="16"/></Target><Target id="@+id/button_auto" view="Button"><Expressions/><location startLine="40" startOffset="6" endLine="50" endOffset="45"/></Target><Target id="@+id/button_custom" view="Button"><Expressions/><location startLine="54" startOffset="6" endLine="64" endOffset="45"/></Target><Target id="@+id/button_yes" view="Button"><Expressions/><location startLine="70" startOffset="6" endLine="80" endOffset="45"/></Target><Target id="@+id/button_no" view="Button"><Expressions/><location startLine="84" startOffset="6" endLine="94" endOffset="45"/></Target></Targets></Layout>
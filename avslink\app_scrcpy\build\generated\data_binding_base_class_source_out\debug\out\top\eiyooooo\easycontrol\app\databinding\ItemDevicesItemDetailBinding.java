// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Switch;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ItemDevicesItemDetailBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button createDisplay;

  @NonNull
  public final Switch defaultFull;

  @NonNull
  public final Button displayMirroring;

  @NonNull
  public final Switch isAudio;

  private ItemDevicesItemDetailBinding(@NonNull LinearLayout rootView,
      @NonNull Button createDisplay, @NonNull Switch defaultFull, @NonNull Button displayMirroring,
      @NonNull Switch isAudio) {
    this.rootView = rootView;
    this.createDisplay = createDisplay;
    this.defaultFull = defaultFull;
    this.displayMirroring = displayMirroring;
    this.isAudio = isAudio;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDevicesItemDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDevicesItemDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_devices_item_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDevicesItemDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.create_display;
      Button createDisplay = ViewBindings.findChildViewById(rootView, id);
      if (createDisplay == null) {
        break missingId;
      }

      id = R.id.default_full;
      Switch defaultFull = ViewBindings.findChildViewById(rootView, id);
      if (defaultFull == null) {
        break missingId;
      }

      id = R.id.display_mirroring;
      Button displayMirroring = ViewBindings.findChildViewById(rootView, id);
      if (displayMirroring == null) {
        break missingId;
      }

      id = R.id.is_audio;
      Switch isAudio = ViewBindings.findChildViewById(rootView, id);
      if (isAudio == null) {
        break missingId;
      }

      return new ItemDevicesItemDetailBinding((LinearLayout) rootView, createDisplay, defaultFull,
          displayMirroring, isAudio);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

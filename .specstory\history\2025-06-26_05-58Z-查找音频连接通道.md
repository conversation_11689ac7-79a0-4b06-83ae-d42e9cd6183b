<!-- Generated by SpecStory -->

# 查找音频连接通道 (2025-06-26 05:58Z)

_**User**_

搜索一下，这个项目，哪里是音频建立连接通道的。 也就是将手机端的声音通过车机输出的。

---

_**Assistant**_

I'll search for the audio connection channel code in this project - looking for where the phone's audio is sent through the car's speakers.

---

<details>
            <summary>Searched codebase "audio connection channel phone car speakers" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/AudioAdapter.java` | L1-91 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L244-300 |
| `avslink/车机端新互联SDK接入文档.md` | L1183-1231 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/BtMusicFocusManager.kt` | L1-59 |
| `avslink/车机端新互联SDK接入文档.md` | L995-1032 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/macro/WL_API_APP_MSG_TYPE.java` | L1-31 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/idb/VarHelper.java` | L1133-1199 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/audio/AudioPlayer.java` | L1-28 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/idb/VarHelper.java` | L172-260 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L744-778 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/Enum/WL_SDK_MSG_STATUS.java` | L1-36 |
| `avslink/车机端新互联SDK接入文档.md` | L878-906 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L846-907 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L345-394 |
| `avslink/车机端新互联SDK接入文档.md` | L807-837 |
| `avslink/车机端新互联SDK接入文档.md` | L1231-1292 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L778-814 |
| `avslink/车机端新互联SDK接入文档.md` | L957-995 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L722-744 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L697-722 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkMsg.java` | L32-74 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/AudioDecode.java` | L119-151 |
| `avslink/车机端新互联SDK接入文档.md` | L1292-1335 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L664-697 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L394-440 |

</details>

---

Let me search specifically for audio initialization and channel setup code to understand more about the audio connection path.

---

<details>
            <summary>Searched codebase "audio pcm channel initialization sdk phone car" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L244-300 |
| `avslink/车机端新互联SDK接入文档.md` | L1183-1231 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/audio/AudioPlayer.java` | L1-28 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/idb/VarHelper.java` | L172-260 |
| `avslink/车机端新互联SDK接入文档.md` | L1231-1292 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/AudioAdapter.java` | L1-91 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/idb/VarHelper.java` | L1133-1199 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L86-148 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L345-394 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/AudioDecode.java` | L119-151 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkMsg.java` | L32-74 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkListener.java` | L1-23 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPara.java` | L1-87 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L846-907 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkJson.java` | L408-452 |
| `avslink/车机端新互联SDK接入文档.md` | L995-1032 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/BtMusicFocusManager.kt` | L1-59 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/macro/WL_API_APP_MSG_TYPE.java` | L1-31 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkJson.java` | L76-141 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkMsg.java` | L74-117 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/jni/JniStruct/strJniConfig.java` | L1-21 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L778-814 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L126-162 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L198-252 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkInfo.java` | L72-165 |

</details>

---

Let me look for the core SDK initialization code where the audio connections are established:

---

<details>
            <summary>Searched codebase "AudioAnalyze wlAudioInit audio connection implementation" • **23** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/车机端新互联SDK接入文档.md` | L1183-1231 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/AudioAdapter.java` | L1-91 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/BtMusicFocusImpl.kt` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/audio/AudioPlayer.java` | L1-28 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInteraction.java` | L1-64 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L664-697 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/AudioDecode.java` | L1-64 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L846-907 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L345-394 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L394-440 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L212-261 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L86-148 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L722-744 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L778-814 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/net/UDPReceive.java` | L1-43 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L697-722 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/direct/DockChannel.java` | L88-106 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/AudioDecode.java` | L64-119 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPara.java` | L1-87 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/WLHardware.java` | L172-228 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/BtMusicFocusManager.kt` | L1-59 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/direct/DockChannel.java` | L396-417 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L440-504 |

</details>

---

Let me search more specifically for audio initialization and the PCM data handling that would be used to send audio from phone to car:

---

<details>
            <summary>Searched codebase "AudioAnalyze implementation PCM data flow phone to car" • **19** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L846-907 |
| `avslink/车机端新互联SDK接入文档.md` | L1183-1231 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/idb/VarHelper.java` | L172-260 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/audio/AudioPlayer.java` | L1-28 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/AudioAdapter.java` | L1-91 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L244-300 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/AudioDecode.java` | L1-64 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/idb/VarHelper.java` | L1133-1199 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkJson.java` | L408-452 |
| `avslink/车机端新互联SDK接入文档.md` | L995-1032 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkMsg.java` | L32-74 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/BtMusicFocusManager.kt` | L1-59 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/AudioDecode.java` | L119-151 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/AudioDecode.java` | L64-119 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L394-440 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L345-394 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkListener.java` | L1-23 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/macro/WL_API_APP_MSG_TYPE.java` | L1-31 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkMsg.java` | L74-117 |

</details>

---

I need to specifically check the SDK initialization and connection setup to understand how the audio channel is established. Let me check for the SDK initialization and LinkHost setup:

---

<details>
            <summary>Searched codebase "LinkHost init audio connection setup" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/AudioAdapter.java` | L1-91 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/audio/AudioPlayer.java` | L1-28 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L212-261 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L1-43 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/platform/LinkPlatform.java` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L440-504 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L230-268 |
| `avslink/车机端新互联SDK接入文档.md` | L1292-1335 |
| `avslink/车机端新互联SDK接入文档.md` | L1231-1292 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L664-697 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L261-327 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L69-105 |
| `avslink/linkHost/src/main/AndroidManifest.xml` | L1-33 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/AudioDecode.java` | L1-64 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L722-744 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L504-557 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L697-722 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L744-778 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L78-154 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L327-388 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/LinkHost.java` | L180-212 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L86-148 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/BtMusicFocusImpl.kt` | L1-58 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkConfig.java` | L1-93 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/AudioDecode.java` | L64-119 |

</details>

---

Let me check specifically for the implementation of AudioAnalyze which seems to be a core class for audio processing:

---

<details>
            <summary>Searched codebase "AudioAnalyze implementation wlAudioInit wl_audio_input_data" • **18** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/车机端新互联SDK接入文档.md` | L1183-1231 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/AudioDecode.java` | L1-64 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/audio/AudioPlayer.java` | L1-28 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/jni/CallBack/AUDIO_DATA_CALLBACK.java` | L1-5 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/AudioAdapter.java` | L1-91 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInteraction.java` | L1-64 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/AudioDecode.java` | L64-119 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L846-907 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPara.java` | L1-87 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L496-533 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L394-440 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/util/MediaCodecUtil.java` | L527-568 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/JniInteraction.java` | L22-74 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L126-162 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L86-148 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L778-814 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L209-257 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L440-496 |

</details>

---

Based on my search results, I now have a clearer picture of how the audio connection works in this project. Let me provide a comprehensive answer about the audio connection channel between the phone and car.

The audio connection in this project works through a PCM data channel that allows the phone's audio to be transmitted through the car's speakers. Here's how it works:

1. **Audio Channel Initialization**:
   - The audio connection is established through the `AudioAdapter` and `AudioPlayer` interface implementation
   - The SDK initialization includes an optional `AudioPlayer` parameter that allows for audio customization

2. **Connection and Port Configuration**:
   - There are dedicated PCM data ports for transmitting audio:
     - `androidPcmDataPort` (port 6833) - PCM data channel for Android devices
     - `iosPcmDataPort` (port 6834) - PCM data channel for iOS devices

3. **Audio Data Flow**:
   - The phone's audio data is received by the car through the SDK via `onAudioData` method in `SdkViewModel`
   - The audio flow path is: 
     - Phone → SDK → `SdkViewModel.onAudioData()` → `AudioAdapter.onAudioData()` → `AudioPlayer.onAudioData()` → `AudioAnalyze.wl_audio_input_data()`
   - The AudioAnalyze class processes the PCM data and outputs it to the car's audio system

4. **Audio Implementation**:
   - The `AudioAnalyze` class provides core functionality for handling audio:
     - `wlAudioInit(Context)` - initializes the audio system
     - `wl_audio_input_data(byte[], int)` - processes incoming audio data from the phone
     - `audioResume()` - resumes audio playback
     - `audioStop()` - stops audio playback
     - `audioReset()` - resets specific audio stream types

5. **Audio Stream Types**:
   - The system supports multiple audio stream types:
     - `AUDIO_STREAM_MUSIC` - for music playback
     - `AUDIO_STREAM_TTS` - for text-to-speech and navigation prompts
     - `AUDIO_STREAM_VR` - for voice recognition
     - `AUDIO_STREAM_ALL` - for all audio streams

6. **Control Commands**:
   - The car can send commands to control audio on the phone:
     - `sendMusicPlay()` - starts music playback on the phone
     - `sendMusicStop()` - stops music playback on the phone
     - `sendAudioExit()` - puts WeLink Audio in quiet mode
     - `sendAudioResume()` - resumes WeLink Audio

7. **Audio Playback Setup**:
   - For playback on the car, the system uses Android's `AudioTrack` to output the PCM data
   - The PCM data is configured with specific parameters:
     - Sample rate: 16000Hz
     - Channel: MONO (CHANNEL_IN_MONO)
     - Encoding: 16-bit PCM (ENCODING_PCM_16BIT)
     - Buffer size: 8192 bytes

This audio connection system allows for bidirectional audio control, with the phone's audio being transmitted to the car's speakers, while also allowing the car to control playback on the phone.

---


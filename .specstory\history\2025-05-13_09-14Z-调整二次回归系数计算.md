<!-- Generated by SpecStory -->

# 调整二次回归系数计算 (2025-05-13 09:14Z)

_**User**_

调整成提前计算<=50 和 大于 50 情况下，二次回归系数。

---

_**Assistant**_

<think><details><summary>Thought Process</summary>The user wants to refactor the `HidIosCalibrationManager.java` file.
I need to modify the `checkPointV2` and `getYByX` methods.

Here's the plan:
1.  In `checkPointV2`, after creating `yList`, calculate the quadratic regression coefficients for two cases: `inputIndex <= 50` and `inputIndex > 50`.
2.  Store these coefficients, perhaps in a new class or as member variables if appropriate (though these are static methods, so member variables of `HidIosCalibrationManager` might not be the best fit unless they are also static). A simpler approach for now is to pass them to `getYByX`.
3.  Modify `getYByX` to accept these pre-calculated coefficients.
4.  Instead of recalculating coefficients inside `getYByX`, use the passed-in coefficients based on the `inputIndex`.
5.  The `performQuadraticRegression` method will still be used for the actual calculation of coefficients, but it will be called from `checkPointV2` rather than `getYByX`.

Let's refine step 2. Since `getYByX` is called in a loop within `checkPointV2`, it's better to calculate the coefficients once outside this loop in `checkPointV2` and then pass them to `getYByX`.

Revised plan:
1.  Define a simple inner class or record to hold the coefficients `a`, `b`, `c`.
2.  In `checkPointV2`:
    *   After `yList` is prepared.
    *   Prepare `dataPointsX_le50`, `dataPointsY_le50` for `inputIndex <= 50`.
    *   Call `performQuadraticRegression` (or a modified version that just returns coefficients) to get `coeffs_le50`.
    *   Prepare `dataPointsX_gt50`, `dataPointsY_gt50` for `inputIndex > 50`.
    *   Call `performQuadraticRegression` to get `coeffs_gt50`.
    *   In the loop that calls `getYByX`, pass the appropriate set of coefficients.
3.  Modify `getYByX`:
    *   Remove the logic for preparing `dataPointsX` and `dataPointsY`.
    *   Remove the call to `performQuadraticRegression`.
    *   Add parameters for `a`, `b`, `c`.
    *   Calculate `predictedY` using `a * inputIndex * inputIndex + b * inputIndex + c`.
4.  The `performQuadraticRegression` method needs to be adapted. It currently calculates and returns `predictedY`. It should be split or modified:
    *   One part calculates and returns the coefficients `a, b, c`.
    *   Another part (or `getYByX` itself) uses these coefficients to predict `y`.

Let's make `performQuadraticRegression` return the coefficients.

Revised Plan v3:
1.  Create a new private static class, say `QuadraticCoefficients`, with fields `a`, `b`, `c`.
2.  Modify `performQuadraticRegression`:
    *   Change its return type to `QuadraticCoefficients`.
    *   Instead of calculating and returning `predictedY`, it will calculate `a`, `b`, `c` and return `new QuadraticCoefficients(a, b, c)`.
    *   Handle the fallback cases (n < 3 or detM close to zero) by returning `null` or a special `QuadraticCoefficients` object indicating failure, so the caller can implement fallback logic (e.g., using the direct value from `yList`).
3.  In `checkPointV2`:
    *   After `yList` is prepared.
    *   **For `inputIndex <= 50` case:**
        *   Define `regressionBaseXValues_le50 = Arrays.asList(5, 10, 20, 40, 50);`
        *   Prepare `dataPointsX_le50`, `dataPointsY_le50` using `yList` and `regressionBaseXValues_le50`.
        *   Call `QuadraticCoefficients coeffs_le50 = performQuadraticRegression(dataPointsX_le50, dataPointsY_le50);` (The parameters `inputIndex`, `yStepsList`, `yList` for fallback in `performQuadraticRegression` are no longer relevant for *calculating coefficients*, but the fallback logic itself might need to be re-evaluated. For now, assume `performQuadraticRegression` returns `null` on failure).
    *   **For `inputIndex > 50` case:**
        *   Define `regressionBaseXValues_gt50 = Arrays.asList(50, 60, 80, 100);`
        *   Prepare `dataPointsX_gt50`, `dataPointsY_gt50` using `yList` and `regressionBaseXValues_gt50`.
        *   Call `QuadraticCoefficients coeffs_gt50 = performQuadraticRegression(dataPointsX_gt50, dataPointsY_gt50);`
    *   In the loop calling `getYByX`:
        *   Select `coeffs_le50` or `coeffs_gt50` based on `idx`.
        *   Pass these coefficients (or `a, b, c` directly) to `getYByX`.
4.  Modify `getYByX`:
    *   Change its signature to accept `QuadraticCoefficients coeffs` (or `double a, double b, double c`).
    *   Remove the logic for preparing `regressionBaseXValues`, `dataPointsX`, `dataPointsY`.
    *   Remove the direct call to `performQuadraticRegression`.
    *   If `coeffs` is not null and valid:
        *   Calculate `predictedY = coeffs.a * inputIndex * inputIndex + coeffs.b * inputIndex + coeffs.c;`
    *   Else (if `coeffs` is null, indicating regression failed or not enough points):
        *   Implement fallback: find `inputIndex` in `yStepsList` and return corresponding `yList.get(i)`. If not found, return 0. This fallback logic is currently inside `performQuadraticRegression` and needs to be moved or replicated in `getYByX` or its caller.

Let's re-evaluate the fallback logic. `performQuadraticRegression` currently has fallback logic. If it returns `null` (or a special object) when regression can't be performed, then `getYByX` must handle this.

Revised Plan v4:
1.  Define a new private static class `QuadraticCoefficients` to hold `double a, b, c;` and a boolean `isValid` (or similar).
2.  Modify `performQuadraticRegression`:
    *   Its signature will be `private static QuadraticCoefficients calculateCoefficients(List<Double> dataPointsX, List<Double> dataPointsY)`.
    *   If `n < 3` or `Math.abs(detM) < epsilon`, it returns `new QuadraticCoefficients(0,0,0, false)` (or similar).
    *   Otherwise, it calculates `a, b, c` and returns `new QuadraticCoefficients(a, b, c, true)`.
    *   Remove parameters `inputIndex`, `yStepsList`, `yList` as they are for prediction/fallback, not coefficient calculation.
3.  In `checkPointV2`:
    *   Initialize `yList`.
    *   **Prepare data for `<= 50` case:**
        *   `List<Integer> regressionBaseXValues_le50 = Arrays.asList(5, 10, 20, 40, 50);`
        *   Create `dataPointsX_le50`, `dataPointsY_le50` by iterating `regressionBaseXValues_le50` and finding corresponding values from `yList` (using `yStepsList` for indexing). This data preparation logic is currently in `getYByX` and needs to be extracted or duplicated. Let's call a helper for this.
        *   `QuadraticCoefficients coeffs_le50 = calculateCoefficients(dataPointsX_le50, dataPointsY_le50);`
    *   **Prepare data for `> 50` case:**
        *   `List<Integer> regressionBaseXValues_gt50 = Arrays.asList(50, 60, 80, 100);`
        *   Create `dataPointsX_gt50`, `dataPointsY_gt50`.
        *   `QuadraticCoefficients coeffs_gt50 = calculateCoefficients(dataPointsX_gt50, dataPointsY_gt50);`
    *   In the loop: `for (int idx = 0; idx < 100; idx+= 5)`
        *   `QuadraticCoefficients currentCoeffs = (idx <= 50) ? coeffs_le50 : coeffs_gt50;`
        *   `getYByX(yList, yStepsList, idx, currentCoeffs);` (pass `yStepsList` for fallback).
4.  Modify `getYByX(List<Float> yList, List<Integer> yStepsList, int inputIndex, QuadraticCoefficients coeffs)`:
    *   Remove `regressionBaseXValues` determination and data point preparation.
    *   Check `coeffs.isValid`.
    *   If valid: `double predictedY = coeffs.a * inputIndex * inputIndex + coeffs.b * inputIndex + coeffs.c;`
    *   Else (fallback):
        *   `printLog("getYByX: Using fallback for inputIndex " + inputIndex);`
        *   Iterate `yStepsList` to find `inputIndex`. If found, `return yList.get(foundIndex);`.
        *   Else `return 0;`
    *   The rest of `getYByX` (logging, diff calculation) remains similar.
5.  Need a helper method to prepare `dataPointsX` and `dataPointsY`:
    `private static Pair<List<Double>, List<Double>> prepareDataPoints(List<Float> yList, List<Integer> yStepsList, List<Integer> regressionBaseXValues)`
    This helper will be called twice in `checkPointV2`. `Pair` can be a simple custom class if `android.util.Pair` is not desired/available. For simplicity, it could return a `List<List<Double>>` or populate two lists passed as arguments. Let's make it populate two passed-in lists.
    `private static void prepareDataPoints(List<Float> yList, List<Integer> yStepsList, List<Integer> regressionBaseXValues, List<Double> outDataPointsX, List<Double> outDataPointsY)`

This looks like a solid plan. The core idea is to pre-calculate coefficients and pass them, separating coefficient calculation from prediction.

Let's consider the structure:
- `HidIosCalibrationManager`
  - `QuadraticCoefficients` (inner static class)
  - `checkPointV2()`
    - Calls `prepareDataPointsAndCoefficients` (new helper) twice to get `coeffs_le50` and `coeffs_gt50`.
    - Loops and calls `getYByX`.
  - `prepareDataPointsAndCoefficients(List<Float> yList, List<Integer> yStepsList, List<Integer> baseXVals)`:
    - Prepares `dataPointsX`, `dataPointsY` from `yList`, `yStepsList`, `baseXVals`.
    - Calls `calculateCoefficients(dataPointsX, dataPointsY)` and returns `QuadraticCoefficients`.
  - `calculateCoefficients(List<Double> dataPointsX, List<Double> dataPointsY)`: (modified `performQuadraticRegression`)
    - Returns `QuadraticCoefficients`.
  - `getYByX(List<Float> yList, List<Integer> yStepsList, int inputIndex, QuadraticCoefficients coeffs)`:
    - Uses `coeffs` to predict.
    - Handles fallback if `coeffs` is invalid using `yList`, `yStepsList`.

This structure moves the data preparation logic out of `getYByX` and into a new helper that is called before the main loop in `checkPointV2`. The `performQuadraticRegression` method is renamed/refactored to `calculateCoefficients` and its responsibility is solely to calculate coefficients.

Double-check `yStepsList` generation. It's hardcoded in `getYByX`. It should be available in `checkPointV2` to be passed around or generated there. It is indeed hardcoded. It should probably be a static final list in the class or passed from `checkPointV2`.
Currently, `yStepsList` is defined inside `getYByX`. It must be accessible to `prepareDataPointsAndCoefficients` and `getYByX` (for fallback). It's best to define it once in `checkPointV2` (or as a static final field) and pass it down.

Let's make `yStepsList` a local variable in `checkPointV2` and pass it.

Refined Plan v5:
1.  **`QuadraticCoefficients` class:**
    ```java
    private static class QuadraticCoefficients {
        final double a, b, c;
        final boolean isValid;
        QuadraticCoefficients(double a, double b, double c, boolean isValid) {
            this.a = a; this.b = b; this.c = c; this.isValid = isValid;
        }
    }
    ```
2.  **`calculateCoefficients` method (refactored from `performQuadraticRegression`):**
    *   Signature: `private static QuadraticCoefficients calculateCoefficients(List<Double> dataPointsX, List<Double> dataPointsY)`
    *   Calculates `a,b,c`.
    *   Returns `new QuadraticCoefficients(a, b, c, true)` on success.
    *   Returns `new QuadraticCoefficients(0, 0, 0, false)` if `n < 3` or `detM` is near zero.
    *   Removes logging specific to `inputIndex` prediction. Keep logging for coefficient calculation itself if useful.
3.  **`prepareRegressionDataAndCoefficients` helper method:**
    *   Signature: `private static QuadraticCoefficients prepareRegressionDataAndCoefficients(List<Float> yList, List<Integer> yStepsList, List<Integer> regressionBaseXValues)`
    *   Inside:
        *   `List<Double> dataPointsX = new ArrayList<>();`
        *   `List<Double> dataPointsY = new ArrayList<>();`
        *   Logic to populate `dataPointsX`, `dataPointsY` from `yList`, `yStepsList`, and `regressionBaseXValues` (extracted from current `getYByX`). Log these datapoints.
        *   `return calculateCoefficients(dataPointsX, dataPointsY);`
4.  **`checkPointV2` method modifications:**
    *   `List<Integer> yStepsList = Arrays.asList(5, 10, ..., 100);` (defined once here).
    *   Check `yList` and `yStepsList` compatibility early. If not compatible, perhaps return early or handle. (Current `getYByX` does this, should be done before coefficient calculations).
        ```java
        if (yList == null || yList.isEmpty()) {
            printLog("checkPointV2: yList 为 null 或为空。");
            return false; // Or handle appropriately
        }
        if (yStepsList.size() != yList.size()) {
            printLog("checkPointV2: yStepsList 大小 (" + yStepsList.size() + ") 与 yList 大小 (" + yList.size() + ") 不匹配");
            return false; // Or handle appropriately
        }
        ```
    *   `List<Integer> baseXVals_le50 = Arrays.asList(5, 10, 20, 40, 50);`
    *   `QuadraticCoefficients coeffs_le50 = prepareRegressionDataAndCoefficients(yList, yStepsList, baseXVals_le50);`
    *   `List<Integer> baseXVals_gt50 = Arrays.asList(50, 60, 80, 100);`
    *   `QuadraticCoefficients coeffs_gt50 = prepareRegressionDataAndCoefficients(yList, yStepsList, baseXVals_gt50);`
    *   Loop: `for (int idx = 0; idx < 100; idx+= 5)`
        *   `QuadraticCoefficients currentCoeffs = (idx <= 50) ? coeffs_le50 : coeffs_gt50;`
        *   `getYByX(yList, yStepsList, idx, currentCoeffs);`
5.  **`getYByX` method modifications:**
    *   Signature: `public static float getYByX(List<Float> yList, List<Integer> yStepsList, int inputIndex, QuadraticCoefficients coeffs)`
    *   Remove `yStepsList` re-definition.
    *   Remove `yList` null/empty check and `yStepsList.size() != yList.size()` check (done in `checkPointV2`).
    *   Input validation for `inputIndex` against `yStepsList.get(0)` and `yStepsList.get(yStepsList.size() - 1)` remains.
    *   Remove `regressionBaseXValues` logic and `dataPointsX/Y` preparation.
    *   If `coeffs != null && coeffs.isValid`:
        *   `double predictedY = coeffs.a * inputIndex * inputIndex + coeffs.b * inputIndex + coeffs.c;`
        *   Log coefficients and predictedY.
        *   `float yValue = (float) predictedY;`
    *   Else (fallback):
        *   `printLog("getYByX: Coefficients invalid for inputIndex " + inputIndex + ". Using fallback.");`
        *   Find `inputIndex` in `yStepsList`.
        *   `int originalValueIndex = yStepsList.indexOf(inputIndex);`
        *   If `originalValueIndex != -1`: `return yList.get(originalValueIndex);`
        *   Else: `printLog("getYByX fallback: inputIndex " + inputIndex + " not in yStepsList. Returning 0."); return 0;`
    *   The final logging of original value vs computed value should use the `yValue` calculated (either from regression or fallback).

This seems more robust.
The `printLog` messages in `performQuadraticRegression` related to fallback (e.g., "performQuadraticRegression回退...") should be removed or adapted as the fallback logic is now moving to `getYByX`. The `calculateCoefficients` method should primarily log about the coefficient calculation itself (e.g., "二次回归系数: a=...") or why it's returning `isValid = false` (e.g., "点数不足", "矩阵行列式接近于零").

Let's consider the impact on `printLog` calls in `performQuadraticRegression`.
The `performQuadraticRegression` method currently has:
- `printLog("performQuadraticRegression: 二次回归数据点不足 (" + n + ")。至少需要3个点。");` -> Keep, relevant to `isValid=false`.
- Fallback printLogs:
  - `printLog("performQuadraticRegression回退（点不足）：为 inputIndex " + inputIndex + " 返回 yList 中的直接值: " + yList.get(i));` -> Remove, fallback handled by caller.
  - `printLog("performQuadraticRegression回退（点不足）：inputIndex " + inputIndex + " 无直接匹配。返回 0。");` -> Remove.
- `printLog("performQuadraticRegression: 矩阵行列式接近于零 (detM = " + detM + ")。无法进行二次回归。");` -> Keep, relevant to `isValid=false`.
- Fallback printLogs for determinant:
  - `printLog("performQuadraticRegression回退（行列式为零）：为 inputIndex " + inputIndex + " 返回 yList 中的直接值: " + yList.get(i));` -> Remove.
  - `printLog("performQuadraticRegression回退（行列式为零）：inputIndex " + inputIndex + " 无直接匹配。返回 0。");` -> Remove.
- `printLog("performQuadraticRegression: 二次回归系数: a=" + a + ", b=" + b + ", c=" + c);` -> Keep.
- `printLog("performQuadraticRegression: 根据 inputIndex " + inputIndex + " 预测的 Y 值: " + predictedY);` -> Remove, prediction happens in `getYByX`.

The logging in the new `prepareRegressionDataAndCoefficients` method:
Current `getYByX` has:
`printLog("用于回归的数据点 (X, Y)，针对 inputIndex " + inputIndex + ":");`
This should be adapted in `prepareRegressionDataAndCoefficients` to reflect that it's preparing data for a *set* of base X values, not a single `inputIndex`.
Example: `printLog("用于回归的数据点 (X, Y) for base values " + regressionBaseXValues + ":");`

The logging in the modified `getYByX`:
Current `getYByX` has:
`printLog("index: " + inputIndex + " 原始值: " + originalValue + " 计算值: " + yValue + " 差值: " + diff);`
This part should remain largely the same, where `yValue` is the result of either regression or fallback.
If using regression: `printLog("getYByX: For inputIndex " + inputIndex + ", using calculated coeffs: a=" + coeffs.a + ", b=" + coeffs.b + ", c=" + coeffs.c);` before calculating `predictedY`.

Final structure seems good. I will proceed with generating the edit for `HidIosCalibrationManager.java`.</details></think>

---

Okay, I'll adjust the logic to pre-calculate the quadratic regression coefficients for the two cases (`inputIndex <= 50` and `inputIndex > 50`) in the `HidIosCalibrationManager.java` file.

Here's how I'll approach it:

1.  Introduce a new private static inner class `QuadraticCoefficients` to store the coefficients `a`, `b`, `c` and a flag `isValid` to indicate if the coefficients could be successfully computed.
2.  Refactor the existing `performQuadraticRegression` method into a new method, say `calculateCoefficients`. This new method will take `dataPointsX` and `dataPointsY` as input and return an instance of `QuadraticCoefficients`. It will no longer handle prediction or fallback based on `inputIndex`.
3.  Create a new helper method, `prepareRegressionDataAndCoefficients`, which will:
    *   Take `yList`, `yStepsList`, and a list of `regressionBaseXValues` as input.
    *   Prepare the `dataPointsX` and `dataPointsY` lists based on these inputs (this logic will be extracted from the current `getYByX` method).
    *   Call `calculateCoefficients` with the prepared data points and return the resulting `QuadraticCoefficients`.
4.  In `checkPointV2`:
    *   Define `yStepsList` once.
    *   Perform initial checks on `yList` and `yStepsList` compatibility.
    *   Call `prepareRegressionDataAndCoefficients` twice:
        *   Once for `inputIndex <= 50` (with base X values `5, 10, 20, 40, 50`).
        *   Once for `inputIndex > 50` (with base X values `50, 60, 80, 100`).
    *   Store these two sets of coefficients.
    *   In the loop that calls `getYByX`, pass the appropriate pre-calculated `QuadraticCoefficients` based on the current `idx`.
5.  Modify `getYByX`:
    *   Its signature will change to accept `yList`, `yStepsList`, `inputIndex`, and the pre-calculated `QuadraticCoefficients`.
    *   It will use the provided coefficients to calculate `predictedY` if they are valid.
    *   If the coefficients are not valid (or not provided), it will implement a fallback mechanism: attempt to find the `inputIndex` directly in `yStepsList` and return the corresponding value from `yList`.
    *   The logic for selecting `regressionBaseXValues` and preparing data points will be removed from this method as it's now handled by `prepareRegressionDataAndCoefficients`.

I'll now apply these changes to the `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` file.

---

```diff
          android.util.Log.d("POINT_TAG", msg);
      }
-     /**
-      * 新的反控校准逻辑
-      * 需要检查如下要素：
-      * 1、点的数目，理论不会丢失。
-      * @return
-      */
-     public static boolean checkPointV2() {
-         // 校验点数目
-         // 准备工作，将 checkMyPoint 中的点（x，y），倒序。 并且当前值-前一个值（第一个是 0） 取绝对值，整理成新的数组列表      
-         List<Float> yList = new ArrayList<>();
-         for (int i = checkMyPoint.size() - 1; i > 0; i--) {
-             MyPoint point = checkMyPoint.get(i);
-             float diff = point.getY() - (checkMyPoint.get(i - 1).getY());
-             yList.add(Math.abs(diff));
-         }
- 
-         // 控制台打印输出 yList
-         printLog("yList = " + yList.size() + "  " + yList);
-         for (int idx = 0; idx < 100; idx+= 5) {
-             getYByX(yList, idx);
-         }
- 
- 
-         // 检验点规则。 使用算法挨个计算点规则，是否有偏差较远的点。
-         return false;
-     }
- 
-     /**
-      * 新的反控校准逻辑, 根据输入索引点 index，计算对应手机侧屏幕移动量的 y 值。 
-      * @param yList 手机侧屏幕移动量列表
-      * @param inputIndex 索引点
-      * @return 手机侧屏幕移动量
-      */
-     public static float getYByX(List<Float> yList, int inputIndex){
-         List<Integer> yStepsList = Arrays.asList(5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100);
- 
-         if (yList == null || yList.isEmpty()) {
-             printLog("yList 为 null 或为空。");
-             return 0;
-         }
- 
-         // 判断 yStepList 是否与 yList 对应
-         if (yStepsList.size() != yList.size()) {
-             printLog("yStepsList 大小 (" + yStepsList.size() + ") 与 yList 大小 (" + yList.size() + ") 不匹配");
-             return 0;
-         }
- 
-         // 异常处理：如果索引超出范围
-         // inputIndex 的有效范围基于 yStepsList 的值。
-         if (inputIndex < yStepsList.get(0) || inputIndex > yStepsList.get(yStepsList.size() - 1)) {
-             printLog("无效的 inputIndex: " + inputIndex + ". 必须在 " + yStepsList.get(0) + " 和 " + yStepsList.get(yStepsList.size() - 1) + " 之间。");
-             return 0;
-         }
-         
-         // 根据条件选择用于回归的X值
-         // 如果 inputIndex 小于等于 50， 则使用 5, 10， 20, 40， 50 作为输入数据X
-         // 否则 则使用 50， 60， 80， 100 作为输入数据X
-         List<Integer> regressionBaseXValues;
-         if (inputIndex <= 50) {
-             regressionBaseXValues = Arrays.asList(5, 10, 20, 40, 50);
-         } else {
-             regressionBaseXValues = Arrays.asList(50, 60, 80, 100);
-         }
- 
-         List<Double> dataPointsX = new ArrayList<>();
-         List<Double> dataPointsY = new ArrayList<>();
- 
-         for (int baseX : regressionBaseXValues) {
-             int indexInYSteps = -1;
-             // 找到 baseX 在 yStepsList 中的索引，以获取对应的 yList 值
-             for (int i = 0; i < yStepsList.size(); i++) {
-                 if (yStepsList.get(i).equals(baseX)) { // Integer比较使用.equals
-                     indexInYSteps = i;
-                     break;
-                 }
-             }
- 
-             if (indexInYSteps != -1) { 
-                 // 由于之前的检查，yList.size() 保证与 yStepsList.size() 相同
-                 dataPointsX.add((double) baseX);
-                 dataPointsY.add((double) yList.get(indexInYSteps));
-             } else {
-                 // 如果 regressionBaseXValues 包含不在 yStepsList 中的值，则可能发生这种情况,
-                 // 但如果 yStepsList 是全面的并且 regressionBaseXValues 是其子集，则不应发生。
-                 printLog("警告: 基准 X 值 " + baseX + " 在 yStepsList 中未找到。跳过此点进行回归。");
-             }
-         }
- 
-         printLog("用于回归的数据点 (X, Y)，针对 inputIndex " + inputIndex + ":");
-         for (int i = 0; i < dataPointsX.size(); i++) {
-             printLog("(" + dataPointsX.get(i) + ", " + dataPointsY.get(i) + ")");
-         }
-         
-         // 二维数据数组 (dataPointsX, dataPointsY) 现已准备好。
-         // 现在，用它来进行二次拟合。
-         // 我们至少需要3个点来进行二次拟合 (解 y = ax^2 + bx + c 中的 a, b, c)。
-         float yValue = performQuadraticRegression(dataPointsX, dataPointsY, inputIndex, yStepsList, yList);
-         // 查找并打印inputIndex对应的yList值
-         int indexInYSteps = yStepsList.indexOf(inputIndex);
-         if (indexInYSteps != -1) {
-             float originalValue = yList.get(indexInYSteps);
-             float diff = yValue - originalValue;
-             printLog("index: " + inputIndex + " 原始值: " + originalValue + " 计算值: " + yValue + " 差值: " + diff);
-         } else {
-             printLog("index: " + inputIndex + " 计算值: " + yValue);
-         }
-         return yValue;
-     }
- 
-     /**
-      * 执行二次多项式回归拟合。
-      * @param dataPointsX X轴数据点列表
-      * @param dataPointsY Y轴数据点列表
-      * @param inputIndex  用于预测的X值
-      * @param yStepsList  原始的Y轴步长列表 (用于回退)
-      * @param yList       原始的Y轴值列表 (用于回退)
-      * @return 预测的Y值，如果无法拟合或发生错误则返回回退值
-      */
-     private static float performQuadraticRegression(List<Double> dataPointsX, List<Double> dataPointsY, int inputIndex, List<Integer> yStepsList, List<Float> yList) {
+ 
+     private static class QuadraticCoefficients {
+         final double a, b, c;
+         final boolean isValid;
+ 
+         QuadraticCoefficients(double a, double b, double c, boolean isValid) {
+             this.a = a;
+             this.b = b;
+             this.c = c;
+             this.isValid = isValid;
+         }
+     }
+ 
+     private static QuadraticCoefficients calculateCoefficients(List<Double> dataPointsX, List<Double> dataPointsY) {
          int n = dataPointsX.size();
-         // 点数不足3个，则直接在此处处理回退，因为 getYByX 已经检查过
-         // 但作为独立函数，再次检查是良好的实践
          if (n < 3) {
-             printLog("performQuadraticRegression: 二次回归数据点不足 (" + n + ")。至少需要3个点。");
-             for (int i = 0; i < yStepsList.size(); i++) {
-                 if (yStepsList.get(i).equals(inputIndex)) {
-                     printLog("performQuadraticRegression回退（点不足）：为 inputIndex " + inputIndex + " 返回 yList 中的直接值: " + yList.get(i));
-                     return yList.get(i);
-                 }
-             }
-             printLog("performQuadraticRegression回退（点不足）：inputIndex " + inputIndex + " 无直接匹配。返回 0。");
-             return 0;
+             printLog("calculateCoefficients: 二次回归数据点不足 (" + n + ")。至少需要3个点。");
+             return new QuadraticCoefficients(0, 0, 0, false);
          }
  
```

```diff
          }
  
-         // 系数矩阵 M
-         // | sumX4  sumX3  sumX2 |
-         // | sumX3  sumX2  sumX  |
-         // | sumX2  sumX   n     |
-         
-         // 行列式 detM
          double detM = sumX4 * (sumX2 * n - sumX * sumX)
                      - sumX3 * (sumX3 * n - sumX2 * sumX)
                      + sumX2 * (sumX3 * sumX - sumX2 * sumX2);
  
-         // 检查 detM 是否接近于零以避免除以零
-         double epsilon = 1e-9; // 定义一个小的 epsilon 值
+         double epsilon = 1e-9;
          if (Math.abs(detM) < epsilon) {
-             printLog("performQuadraticRegression: 矩阵行列式接近于零 (detM = " + detM + ")。无法进行二次回归。");
-             // 回退逻辑：尝试返回直接匹配
-             for (int i = 0; i < yStepsList.size(); i++) {
-                 if (yStepsList.get(i).equals(inputIndex)) {
-                     printLog("performQuadraticRegression回退（行列式为零）：为 inputIndex " + inputIndex + " 返回 yList 中的直接值: " + yList.get(i));
-                     return yList.get(i);
-                 }
-             }
-             printLog("performQuadraticRegression回退（行列式为零）：inputIndex " + inputIndex + " 无直接匹配。返回 0。");
-             return 0;
-         }
- 
-         // 行列式 detA, detB, detC (克莱默法则)
-         double detA_num = sumX2Y * (sumX2 * n - sumX * sumX)
-                        - sumXY  * (sumX3 * n - sumX2 * sumX)
-                        + sumY   * (sumX3 * sumX - sumX2 * sumX2);
- 
-         double detB_num = sumX4  * (sumXY * n - sumY * sumX)
-                        - sumX2Y * (sumX3 * n - sumX2 * sumX) // Note: sumX2Y used here
-                        + sumY   * (sumX2 * sumX2 - sumX3 * sumX2); // Correction: sumX2*sumX2 - sumX3*sumX (X*Y column replaces B coeff column)
-                                                                     // Correct determinant for B (replace B column with Y vector)
-                                                                     // | sumX4  sumX2Y  sumX2 |
-                                                                     // | sumX3  sumXY   sumX  |
-                                                                     // | sumX2  sumY    n     |
-         detB_num = sumX4 * (sumXY * n - sumY * sumX) 
-                  - sumX3 * (sumX2Y * n - sumY * sumX2) 
-                  + sumX2 * (sumX2Y * sumX - sumXY * sumX2);
- 
- 
-         double detC_num = sumX4 * (sumX2 * sumY - sumX * sumXY)
-                        - sumX3 * (sumX3 * sumY - sumX2 * sumXY)
-                        + sumX2Y * (sumX3 * sumX - sumX2 * sumX2); // This is actually detA for the matrix system for c if ordered a,b,c (c is last coeff)
-                                                                   // Correct determinant for C (replace C column with Y vector)
-                                                                   // | sumX4  sumX3  sumX2Y |
-                                                                   // | sumX3  sumX2  sumXY  |
-                                                                   // | sumX2  sumX   sumY   |
-          detC_num = sumX4  * (sumX2 * sumY - sumXY * sumX)
-                   - sumX3  * (sumX3 * sumY - sumXY * sumX2)
-                   + sumX2Y * (sumX3 * sumX - sumX2 * sumX2); // This is detA based on my notes... actually should be for C:
- 
-         // Let's re-verify the Cramer's rule numerators for a, b, c where y = ax^2 + bx + c (or c_2 x^2 + c_1 x + c_0)
-         // The system is:
-         // (sumX4)a + (sumX3)b + (sumX2)c = sumX2Y
-         // (sumX3)a + (sumX2)b + (sumX )c = sumXY
-         // (sumX2)a + (sumX )b + (n   )c = sumY
- 
-         // Numerator for 'a' (replace first column of M with [sumX2Y, sumXY, sumY])
+             printLog("calculateCoefficients: 矩阵行列式接近于零 (detM = " + detM + ")。无法进行二次回归。");
+             return new QuadraticCoefficients(0, 0, 0, false);
+         }
+ 
          double num_a = sumX2Y * (sumX2 * n - sumX * sumX)
                       - sumXY  * (sumX3 * n - sumX2 * sumX)
                       + sumY   * (sumX3 * sumX - sumX2 * sumX2);
  
-         // Numerator for 'b' (replace second column of M with [sumX2Y, sumXY, sumY])
          double num_b = sumX4  * (sumXY * n - sumY * sumX)
                       - sumX3  * (sumX2Y * n - sumY * sumX2)
                       + sumX2  * (sumX2Y * sumX - sumXY * sumX2);
  
-         // Numerator for 'c' (replace third column of M with [sumX2Y, sumXY, sumY])
          double num_c = sumX4  * (sumX2 * sumY - sumX * sumXY)
                       - sumX3  * (sumX3 * sumY - sumX2 * sumXY)
```

```diff
          double c = num_c / detM;
  
-         printLog("performQuadraticRegression: 二次回归系数: a=" + a + ", b=" + b + ", c=" + c);
- 
-         // 计算预测的 Y 值
-         double predictedY = a * inputIndex * inputIndex + b * inputIndex + c;
-         printLog("performQuadraticRegression: 根据 inputIndex " + inputIndex + " 预测的 Y 值: " + predictedY);
-         
-         return (float) predictedY;
-     }
+         printLog("calculateCoefficients: 二次回归系数: a=" + a + ", b=" + b + ", c=" + c);
+         return new QuadraticCoefficients(a, b, c, true);
+     }
+ 
+     private static QuadraticCoefficients prepareRegressionDataAndCoefficients(List<Float> yList, List<Integer> yStepsList, List<Integer> regressionBaseXValues) {
+         List<Double> dataPointsX = new ArrayList<>();
+         List<Double> dataPointsY = new ArrayList<>();
+ 
+         printLog("准备用于回归的数据点 for base X values: " + regressionBaseXValues);
+         for (int baseX : regressionBaseXValues) {
+             int indexInYSteps = -1;
+             for (int i = 0; i < yStepsList.size(); i++) {
+                 if (yStepsList.get(i).equals(baseX)) {
+                     indexInYSteps = i;
+                     break;
+                 }
+             }
+ 
+             if (indexInYSteps != -1) {
+                 dataPointsX.add((double) baseX);
+                 dataPointsY.add((double) yList.get(indexInYSteps));
+                 printLog("  Adding data point: (" + baseX + ", " + yList.get(indexInYSteps) + ")");
+             } else {
+                 printLog("  警告: 基准 X 值 " + baseX + " 在 yStepsList 中未找到。跳过此点进行回归。");
+             }
+         }
+         return calculateCoefficients(dataPointsX, dataPointsY);
+     }
+ 
+     /**
+      * 新的反控校准逻辑
+      * 需要检查如下要素：
+      * 1、点的数目，理论不会丢失。
+      * @return
+      */
+     public static boolean checkPointV2() {
+         // 准备工作，将 checkMyPoint 中的点（x，y），倒序。 并且当前值-前一个值（第一个是 0） 取绝对值，整理成新的数组列表      
+         List<Float> yList = new ArrayList<>();
+         for (int i = checkMyPoint.size() - 1; i > 0; i--) {
+             MyPoint point = checkMyPoint.get(i);
+             float diff = point.getY() - (checkMyPoint.get(i - 1).getY());
+             yList.add(Math.abs(diff));
+         }
+ 
+         // 控制台打印输出 yList
+         printLog("yList = " + yList.size() + "  " + yList);
+ 
+         if (yList == null || yList.isEmpty()) {
+             printLog("checkPointV2: yList 为 null 或为空。");
+             return false;
+         }
+ 
+         List<Integer> yStepsList = Arrays.asList(5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100);
+         if (yStepsList.size() != yList.size()) {
+             printLog("checkPointV2: yStepsList 大小 (" + yStepsList.size() + ") 与 yList 大小 (" + yList.size() + ") 不匹配");
+             return false;
+         }
+ 
+         List<Integer> baseXVals_le50 = Arrays.asList(5, 10, 20, 40, 50);
+         QuadraticCoefficients coeffs_le50 = prepareRegressionDataAndCoefficients(yList, yStepsList, baseXVals_le50);
+ 
+         List<Integer> baseXVals_gt50 = Arrays.asList(50, 60, 80, 100);
+         QuadraticCoefficients coeffs_gt50 = prepareRegressionDataAndCoefficients(yList, yStepsList, baseXVals_gt50);
+ 
+         for (int idx = 0; idx < 100; idx+= 5) {
+             QuadraticCoefficients currentCoeffs = (idx <= 50) ? coeffs_le50 : coeffs_gt50;
+             getYByX(yList, yStepsList, idx, currentCoeffs);
+         }
+ 
+ 
+         // 检验点规则。 使用算法挨个计算点规则，是否有偏差较远的点。
+         return false;
+     }
+ 
+     /**
+      * 新的反控校准逻辑, 根据输入索引点 index，计算对应手机侧屏幕移动量的 y 值。 
+      * @param yList 手机侧屏幕移动量列表
+      * @param yStepsList 预定义的X轴步长列表
+      * @param inputIndex 索引点
+      * @param coeffs 预计算的二次回归系数
+      * @return 手机侧屏幕移动量
+      */
+     public static float getYByX(List<Float> yList, List<Integer> yStepsList, int inputIndex, QuadraticCoefficients coeffs){
+ 
+         // 异常处理：如果索引超出范围 (yStepsList is guaranteed non-empty by checkPointV2)
+         if (inputIndex < yStepsList.get(0) || inputIndex > yStepsList.get(yStepsList.size() - 1)) {
+             printLog("getYByX: 无效的 inputIndex: " + inputIndex + ". 必须在 " + yStepsList.get(0) + " 和 " + yStepsList.get(yStepsList.size() - 1) + " 之间。");
+             return 0;
+         }
+         
+         float yValue;
+ 
+         if (coeffs != null && coeffs.isValid) {
+             printLog("getYByX: For inputIndex " + inputIndex + ", using calculated coeffs: a=" + coeffs.a + ", b=" + coeffs.b + ", c=" + coeffs.c);
+             double predictedY = coeffs.a * inputIndex * inputIndex + coeffs.b * inputIndex + coeffs.c;
+             yValue = (float) predictedY;
+             printLog("getYByX: 根据 inputIndex " + inputIndex + " 预测的 Y 值: " + yValue);
+         } else {
+             printLog("getYByX: Coefficients invalid or null for inputIndex " + inputIndex + ". Using fallback.");
+             int originalValueIndex = yStepsList.indexOf(inputIndex);
+             if (originalValueIndex != -1) {
+                 yValue = yList.get(originalValueIndex);
+                 printLog("getYByX fallback: Found direct value for " + inputIndex + ": " + yValue);
+             } else {
+                 printLog("getYByX fallback: inputIndex " + inputIndex + " not in yStepsList. Returning 0.");
+                 yValue = 0;
+             }
+         }
+         
+         // 查找并打印inputIndex对应的yList值 (original value for comparison)
+         int indexInYSteps = yStepsList.indexOf(inputIndex);
+         if (indexInYSteps != -1) {
+             float originalValue = yList.get(indexInYSteps);
+             float diff = yValue - originalValue;
+             printLog("index: " + inputIndex + " 原始值: " + originalValue + " 计算值: " + yValue + " 差值: " + diff);
+         } else {
+             // This case should ideally not happen if inputIndex validation is correct
+             // and yList/yStepsList are correctly aligned.
+             // However, if it does, it means inputIndex was valid against overall range but not a specific step.
+             printLog("index: " + inputIndex + " 计算值: " + yValue + " (原始值在yStepsList中未找到进行比较)");
+         }
+         return yValue;
+     }
+ 
      /**
       * 反控校准逻辑
```

---

The code has been refactored as planned.

Here's a summary of the changes:
1.  A new `QuadraticCoefficients` inner class was added to hold regression coefficients (`a`, `b`, `c`) and an `isValid` flag.
2.  The `performQuadraticRegression` method was replaced by `calculateCoefficients`. This method now only calculates the coefficients and returns a `QuadraticCoefficients` object. It no longer performs predictions or fallbacks based on `inputIndex`.
3.  A new helper method `prepareRegressionDataAndCoefficients` was introduced. It prepares the data points for regression based on `yList`, `yStepsList`, and `regressionBaseXValues`, then calls `calculateCoefficients`.
4.  `checkPointV2` was updated to:
    *   Define `yStepsList`.
    *   Call `prepareRegressionDataAndCoefficients` twice to pre-calculate coefficients for `inputIndex <= 50` and `inputIndex > 50`.
    *   Pass these pre-calculated coefficients to `getYByX` in its loop.
5.  `getYByX` was modified to:
    *   Accept the pre-calculated `QuadraticCoefficients`.
    *   Use these coefficients for prediction if they are valid.
    *   Implement a fallback to use direct values from `yList` if coefficients are invalid or null.
    *   Removed the internal logic for data point preparation and coefficient calculation.

The old `performQuadraticRegression` method (which was quite long and included prediction logic) is still present in the diff you see, but it should have been effectively replaced by the new `calculateCoefficients` and the prediction logic now in `getYByX`. If the `performQuadraticRegression` method is still present and unaltered in the actual file, that might be an issue with the diff application. Based on the instructions, it should be largely replaced/refactored.

Please review the changes in the file to ensure `performQuadraticRegression` is either removed or correctly refactored into `calculateCoefficients` and that the new methods are correctly integrated.

---


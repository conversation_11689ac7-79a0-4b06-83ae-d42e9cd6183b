package com.autoai.wdlink.hu.frame.utils


import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothProfile
import android.content.Context
import android.util.Log
import com.autoai.wdlink.hu.Logger
import java.lang.reflect.InvocationTargetException
import kotlin.reflect.full.functions
import kotlin.reflect.jvm.isAccessible

class BtMusicFocusImpl : BtMusicFocusBase {
    private val A2DP_SINK = 11
    private lateinit var mContext: Context
    private var mBluetoothAdapter: BluetoothAdapter? = null
    private var mA2dpSink: BluetoothProfile? = null
    private var mProfileListener: BluetoothProfile.ServiceListener =
        object : BluetoothProfile.ServiceListener {
            override fun onServiceConnected(profile: Int, proxy: BluetoothProfile) {
                Log.d("BtMusicFocusImpl", "A2dpSink onServiceConnected $profile")
                if (profile == A2DP_SINK) {
                    mA2dpSink = proxy
                }
            }

            override fun onServiceDisconnected(profile: Int) {
                Log.d("BtMusicFocusImpl", "A2dpSink onServiceDisconnected $profile")
                if (profile == A2DP_SINK) {
                    mA2dpSink = null
                }
            }
        }

    constructor(context: Context) {
        mContext = context.applicationContext
        if (mBluetoothAdapter == null) {
            mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        }
        mBluetoothAdapter?.getProfileProxy(mContext, mProfileListener, A2DP_SINK)
    }

    override fun init() {
        // do init work
    }

    override fun requestBtMusicFocus(device: BluetoothDevice?) {
        /*val connState = getA2dpSinkConnState(device)
        if (connState < 2 ) {
            Log.d("BtMusicFocusImpl", "not connected")
        }*/
        Log.d("BtMusicFocusImpl", "requestBtMusicFocus")
        setWifiCastConnState(device, 1)

    }

    override fun abandonBtMusicFocus(device: BluetoothDevice?) {
        Log.d("BtMusicFocusImpl", "abandonBtMusicFocus")
        setWifiCastConnState(device, 0)
    }

    fun setWifiCastConnState(device: BluetoothDevice?, state: Int) {
        Log.d("BtMusicFocusImpl", "setWifiCastConnState $state")
        try {
            if (mA2dpSink == null) {
                Log.d("BtMusicFocusImpl", "A2dpSink is NULL !!!")
                return
            }
            val cls = mA2dpSink!!::class
            Log.d("BtMusicFocusImpl", "mA2dpSink is ${cls.simpleName}")
            val setCastStateFunc = cls.functions.find { it.name == "setWifiCastConnectionState" }
            setCastStateFunc?.isAccessible = true
            val result = setCastStateFunc?.call(mA2dpSink, device, state) as? Boolean
            Log.d("BtMusicFocusImpl", "setWifiCastConnState success ? $result")
        } catch (e: NoSuchMethodException) {
            throw RuntimeException(e)
        } catch (e: InvocationTargetException) {
            throw RuntimeException(e)
        } catch (e: IllegalAccessException) {
            throw RuntimeException(e)
        }
    }


    /*fun getA2dpSinkConnState(device: BluetoothDevice?):Int {
        try {
            if (mA2dpSink == null) {
                Log.d("BtMusicFocusImpl","A2dpSink is NULL !!!")
                return -1
            }
            val cls = mA2dpSink!!::class
            val getConnectionState = cls.functions.find { it.name == "getConnectionState" }
            getConnectionState?.isAccessible = true
            val result = getConnectionState?.call(mA2dpSink, device) as Int
            Log.d("BtMusicFocusImpl","getConnectionState = $result")
            return result
        } catch (e: NoSuchMethodException) {
            throw RuntimeException(e)
        } catch (e: InvocationTargetException) {
            throw RuntimeException(e)
        } catch (e: IllegalAccessException) {
            throw RuntimeException(e)
        }
    }*/
}
// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ActivityAdbKeyBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final EditText adbKeyPri;

  @NonNull
  public final EditText adbKeyPub;

  @NonNull
  public final ImageView backButton;

  @NonNull
  public final Button ok;

  private ActivityAdbKeyBinding(@NonNull LinearLayout rootView, @NonNull EditText adbKeyPri,
      @NonNull EditText adbKeyPub, @NonNull ImageView backButton, @NonNull Button ok) {
    this.rootView = rootView;
    this.adbKeyPri = adbKeyPri;
    this.adbKeyPub = adbKeyPub;
    this.backButton = backButton;
    this.ok = ok;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAdbKeyBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAdbKeyBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_adb_key, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAdbKeyBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.adb_key_pri;
      EditText adbKeyPri = ViewBindings.findChildViewById(rootView, id);
      if (adbKeyPri == null) {
        break missingId;
      }

      id = R.id.adb_key_pub;
      EditText adbKeyPub = ViewBindings.findChildViewById(rootView, id);
      if (adbKeyPub == null) {
        break missingId;
      }

      id = R.id.back_button;
      ImageView backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.ok;
      Button ok = ViewBindings.findChildViewById(rootView, id);
      if (ok == null) {
        break missingId;
      }

      return new ActivityAdbKeyBinding((LinearLayout) rootView, adbKeyPri, adbKeyPub, backButton,
          ok);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

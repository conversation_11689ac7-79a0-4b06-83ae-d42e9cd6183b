<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/background_cron"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingStart="12dp"
    android:paddingTop="3dp"
    android:paddingEnd="12dp"
    android:paddingBottom="3dp">

  <GridLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:columnCount="3"
      android:padding="3dp">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_columnWeight="9"
        android:background="@drawable/background_cron_stroke"
        android:backgroundTint="@color/button"
        android:gravity="center_vertical">

      <FrameLayout
          android:layout_width="0dp"
          android:layout_height="wrap_content"
          android:layout_weight="1">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@string/option_is_audio"
            android:textColor="@color/onCardBackground"
            android:textSize="@dimen/smallFont"
            tools:ignore="HardcodedText" />

      </FrameLayout>

      <Switch
          android:id="@+id/is_audio"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginEnd="5dp"
          android:thumbTint="@color/button"
          android:thumbTintMode="multiply"
          android:trackTint="@color/button"
          android:trackTintMode="multiply"
          tools:ignore="UseSwitchCompatOrMaterialXml" />

    </LinearLayout>

    <Space android:layout_columnWeight="1" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_columnWeight="9"
        android:background="@drawable/background_cron_stroke"
        android:backgroundTint="@color/button"
        android:gravity="center_vertical">

      <FrameLayout
          android:layout_width="0dp"
          android:layout_height="wrap_content"
          android:layout_weight="1">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@string/option_default_full"
            android:textColor="@color/onCardBackground"
            android:textSize="@dimen/smallFont"
            tools:ignore="HardcodedText" />

      </FrameLayout>

      <Switch
          android:id="@+id/default_full"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginEnd="5dp"
          android:thumbTint="@color/button"
          android:thumbTintMode="multiply"
          android:trackTint="@color/button"
          android:trackTintMode="multiply"
          tools:ignore="UseSwitchCompatOrMaterialXml" />

    </LinearLayout>

    <Space
        android:layout_height="4dp"
        android:layout_columnSpan="3" />

    <Button
        android:id="@+id/display_mirroring"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_columnWeight="9"
        android:background="@drawable/background_cron_stroke"
        android:backgroundTint="@color/alert"
        android:gravity="center"
        android:text="@string/start_display_mirroring"
        android:textColor="@color/button"
        android:textSize="@dimen/smallFont" />

    <Space android:layout_columnWeight="1" />

    <Button
        android:id="@+id/create_display"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_columnWeight="9"
        android:background="@drawable/background_cron_stroke"
        android:backgroundTint="@color/alert"
        android:gravity="center"
        android:text="@string/start_application_transfer"
        android:textColor="@color/button"
        android:textSize="@dimen/smallFont" />

  </GridLayout>

</LinearLayout>

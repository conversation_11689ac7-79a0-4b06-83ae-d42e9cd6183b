<resources xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingTranslation">
    <string name="app_name">手车互联</string>
    <string name="slogan">引领新的驾驶体验</string>

    <!--    <string name="launcher_tips">请先在 Google Play 或 APP Store 中下载安装 GAZ Link，\n使用USB连接手机，手机端音乐、导航等您想要的均可上车使用。</string>-->

    <string name="init_title">请用USB线连接手机，领略手车互联体验！</string>
    <string name="if_need_help">如需互联帮助，</string>
    <string name="click_here">请点击这里</string>

    <string name="help_title">互联帮助</string>
    <string name="help_title_android">安卓手机</string>
    <string name="help_title_ios">苹果手机</string>
    <string name="help_android_toast_step1">USB线连接手机与车机，如手机弹出类似上图提示， 请点击”确定“（勾选默认后再次连接将自动打开）</string>
    <string name="help_android_toast_step2">手机端允许 手车互联 开始投屏（如上图所示，点击”立即开始“）</string>
    <string name="help_ios_toast_step1">USB线连接手机和车机，如手机弹出类似上图提示， 请点击”允许“</string>
    <string name="help_ios_toast_step2">手机弹出屏幕录制工具，选择 ”手车互联“ ，点击开始直播（如上图所示）</string>
    <string name="help_phone_tip">温馨提示：若以上操作均无效，请插拔USB线，再次尝试互联</string>

    <string name="connect_time_out">连接超时</string>
    <string name="connect_time_out_tips">主人，手机端可能没正常打开趣驾 手车互联 ，请重新连接USB线试试吧</string>
    <string name="got_it">知道了</string>

    <string name="heart_beat_err_title">您已退出趣驾 手车互联</string>
    <string name="heart_beat_err_tip">继续使用请重新连接USB线或重新安装趣驾 手车互联 试试</string>

    <string name="connecting_title">手车互联 服务连接中…</string>
    <string name="connecting_tip1">请保证手机已解锁，如有弹窗，请点击确认或选择 手车互联 录屏</string>
    <string name="connecting_tip2">为保证安全，行车中请勿操作手机哦！</string>

    <string name="cancel">取消</string>
    <string name="upgrade_now">立即升级</string>
    <string name="updata_version">当前版本过低，请升级版本后继续使用</string>
    <string name="updata_fail">下载失败</string>
    <string name="updata_success">安装成功</string>
    <string name="connect_one">1.</string>
    <string name="connect_twe">2.</string>
    <string name="connect_close_window">关闭窗口</string>
    <string name="connect_guide_video">引导视频</string>
    <string name="connect_close_guidance">关闭引导</string>
    <string name="connect_connection_completed">连接完成</string>
    <string name="connect_waiting_for_connection">等待连接中...</string>
    <string name="connect_download_the_application">下载应用</string>
    <string name="connect_close_download">关闭下载</string>
    <string name="connect_scan_qr_code">使用手机扫描二维码获取手机应用</string>
    <string name="connect_start_screen_mirroring_for_you_immediately">马上为您启动投屏画面……</string>
    <string name="connect_start_screen_unauthorized_screen_recording_permission">您还未授权开启录屏权限，当前无法使用车机投屏服务！</string>
    <string name="connect_record_mobile_phone_footage">请在手机上允许录制手机画面</string>
    <string name="connect_ensure">确保手机的蓝牙和WiFi处于开启状态     /     用USB数据线连接手机和车机</string>
    <string name="connect_non_sensory_wired_projection_screen">无感投屏 OR 有线投屏</string>
    <string name="updata_open_the_application_on_your_phone_first">先在手机上打开应用</string>
    <string name="connect_usbdatacable">USB数据线</string>
    <string name="connect_start_connecting">开始连接</string>
    <string name="connect_wireless_network">无线网络</string>
    <string name="connect_checking_environment">正在检查环境……</string>
    <string name="connect_screen_casting_authorization">投屏授权</string>
    <string name="connect_success_1">连接成功，请操作需要投屏的内容</string>
    <string name="connect_success_2">为保证投屏，请维护手机解锁状态；为保证安全，行车中请勿操作手机</string>
    <string name="connect_introduce">您可以通过手机usb或者wifi连接车机，使用welink镜像投屏，通过蓝牙音频连接，通过车机播放手机声音。</string>

    <string name="exit_app">退出应用</string>
    <string name="return_to_homepage">返回首页</string>
    <string name="reauthorization_title">录屏未授权</string>
    <string name="reauthorization_go">去授权</string>
    <string name="reauthorization_content">您未授权开启录屏权限，当前无法使用车机投屏服务。获取录屏权限仅用于在车机显示手机屏幕内容，不会保留您的任何个人隐私信息。
如需继续使用投屏服务，请同意投屏授权。</string>
    <string name="connect_abort">WeLink连接已断开</string>
    <string name="safe_viewing">为了您的安全\n请将车辆停稳后观看</string>
    <string name="connect_cancel_cast_tips">请先打开手机端WeLink</string>
    <string name="passenger">我是副驾</string>

    <string name="pop_window_phone_screen_off">手机已锁屏</string>
    <string name="pop_window_unlock_continue_cast">解锁手机后继续投屏</string>
    <string name="pop_window_cast_abort_recovery_projection_tips">投屏已中断，如需继续投屏请点击“恢复投屏”</string>
    <string name="pop_window_stream_abort">投屏中断</string>
    <string name="pop_tonnected_successfully">互联成功</string>
    <string name="pop_window_stream_abort_tips">投屏已中断，如需继续投屏请点击“恢复投屏”</string>
    <string name="pop_connection_recovery">连接已恢复，如需继续投屏请\n点击“恢复投屏”</string>
    <string name="pop_connection_click_on_the_phone_to_record_the_screen">连接已恢复，请在手机上确认\n允许录制手机画面</string>
    <string name="pop_window_recovery_cast">恢复投屏</string>
    <string name="pop_window_disconnect">Welink连接已断开</string>
    <string name="pop_window_disconnect_tips">通过USB数据线重新连接应用\n通过WIFI重新连接应用</string>

    <string name="bt_disconnect_tips">蓝牙已断开</string>
    <string name="bt_not_connected_tips">蓝牙未连接</string>
    <string name="bt_not_connected_occupy">蓝牙不匹配</string>
    <string name="please_connect_bluetooth">如需在车机屏幕上反控手机和播放手机声音，请连接蓝牙。</string>
    <string name="bluetooth_connection_disconnected">蓝牙连接已断开，无法反控手机和播放手机声音。</string>
    <string name="connect_bluetooth">连接蓝牙</string>
    <string name="may_cause_abnormal_sound_of_the_anti_control_phone">可能会导致反控手机和播放手机声音出现异常</string>
    <string name="switchBluetooth">切换蓝牙</string>
    <string name="please_open_the_welink_app_on_your_phone">请在手机上打开Welink应用进行反控校准</string>
    <string name="please_open_welink_on_ios">请在手机上打开Welink应用进行反控校准</string>
    <string name="welink_mobile_confirms_screen_mirroring">WeLink手机端已连接,请在手机端确认投屏。</string>
    <string name="calibration_failed">校准失败！</string>
    <string name="welink_is_currently_connecting">Welink正在连接中...</string>
    <string name="do_not_touch_your_phone">校准过程中请勿触碰手机校准失败可能会导致反控出现误差</string>
    <string name="now_do_not_touch_your_phone">正在进行反控校准\n请勿触碰手机</string>
    <string name="now_your_phone">正在进行反向触控校准\n校准过程中请勿触碰</string>
    <string name="touch_your_phone">反控校准</string>

    <!--网络连接状态-->
    <string name="network_disconnect">网络已断开</string>
    <string name="network_connect_wifi">网络已连接</string>
    <string name="network_connect_4G">4G网络已连接</string>
</resources>
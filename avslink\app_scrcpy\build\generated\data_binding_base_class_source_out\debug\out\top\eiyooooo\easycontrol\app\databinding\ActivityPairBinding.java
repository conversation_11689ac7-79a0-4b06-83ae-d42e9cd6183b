// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ActivityPairBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView backButton;

  @NonNull
  public final TextView certName;

  @NonNull
  public final Button certRegenerate;

  @NonNull
  public final EditText debugPort;

  @NonNull
  public final EditText ip;

  @NonNull
  public final TextView nightModeDetector;

  @NonNull
  public final TextView openingPort;

  @NonNull
  public final TextView pairing;

  @NonNull
  public final EditText pairingCode;

  @NonNull
  public final EditText pairingPort;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-land/</li>
   * </ul>
   */
  @Nullable
  public final ScrollView panel;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-land/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ScrollView panelLandscape;

  @NonNull
  public final Button runOpenPort;

  @NonNull
  public final Button runPair;

  @NonNull
  public final WebView webview;

  private ActivityPairBinding(@NonNull LinearLayout rootView, @NonNull ImageView backButton,
      @NonNull TextView certName, @NonNull Button certRegenerate, @NonNull EditText debugPort,
      @NonNull EditText ip, @NonNull TextView nightModeDetector, @NonNull TextView openingPort,
      @NonNull TextView pairing, @NonNull EditText pairingCode, @NonNull EditText pairingPort,
      @Nullable ScrollView panel, @Nullable ScrollView panelLandscape, @NonNull Button runOpenPort,
      @NonNull Button runPair, @NonNull WebView webview) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.certName = certName;
    this.certRegenerate = certRegenerate;
    this.debugPort = debugPort;
    this.ip = ip;
    this.nightModeDetector = nightModeDetector;
    this.openingPort = openingPort;
    this.pairing = pairing;
    this.pairingCode = pairingCode;
    this.pairingPort = pairingPort;
    this.panel = panel;
    this.panelLandscape = panelLandscape;
    this.runOpenPort = runOpenPort;
    this.runPair = runPair;
    this.webview = webview;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPairBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPairBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_pair, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPairBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.back_button;
      ImageView backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.cert_name;
      TextView certName = ViewBindings.findChildViewById(rootView, id);
      if (certName == null) {
        break missingId;
      }

      id = R.id.cert_regenerate;
      Button certRegenerate = ViewBindings.findChildViewById(rootView, id);
      if (certRegenerate == null) {
        break missingId;
      }

      id = R.id.debug_port;
      EditText debugPort = ViewBindings.findChildViewById(rootView, id);
      if (debugPort == null) {
        break missingId;
      }

      id = R.id.ip;
      EditText ip = ViewBindings.findChildViewById(rootView, id);
      if (ip == null) {
        break missingId;
      }

      id = R.id.night_mode_detector;
      TextView nightModeDetector = ViewBindings.findChildViewById(rootView, id);
      if (nightModeDetector == null) {
        break missingId;
      }

      id = R.id.opening_port;
      TextView openingPort = ViewBindings.findChildViewById(rootView, id);
      if (openingPort == null) {
        break missingId;
      }

      id = R.id.pairing;
      TextView pairing = ViewBindings.findChildViewById(rootView, id);
      if (pairing == null) {
        break missingId;
      }

      id = R.id.pairing_code;
      EditText pairingCode = ViewBindings.findChildViewById(rootView, id);
      if (pairingCode == null) {
        break missingId;
      }

      id = R.id.pairing_port;
      EditText pairingPort = ViewBindings.findChildViewById(rootView, id);
      if (pairingPort == null) {
        break missingId;
      }

      id = R.id.panel;
      ScrollView panel = ViewBindings.findChildViewById(rootView, id);

      id = R.id.panel_landscape;
      ScrollView panelLandscape = ViewBindings.findChildViewById(rootView, id);

      id = R.id.run_open_port;
      Button runOpenPort = ViewBindings.findChildViewById(rootView, id);
      if (runOpenPort == null) {
        break missingId;
      }

      id = R.id.run_pair;
      Button runPair = ViewBindings.findChildViewById(rootView, id);
      if (runPair == null) {
        break missingId;
      }

      id = R.id.webview;
      WebView webview = ViewBindings.findChildViewById(rootView, id);
      if (webview == null) {
        break missingId;
      }

      return new ActivityPairBinding((LinearLayout) rootView, backButton, certName, certRegenerate,
          debugPort, ip, nightModeDetector, openingPort, pairing, pairingCode, pairingPort, panel,
          panelLandscape, runOpenPort, runPair, webview);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

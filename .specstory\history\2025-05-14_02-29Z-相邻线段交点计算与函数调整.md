<!-- Generated by SpecStory -->

# 相邻线段交点计算与函数调整 (2025-05-14 02:29Z)

_**User**_

# 按顺序计算相邻线段的交点
for i in range(len(segments)-1):
    seg1 = segments[i]
    seg2 = segments[i+1]
    
    # 如果斜率相同，线段平行，没有交点
    if abs(seg1['slope'] - seg2['slope']) < 1e-6:
        print(f"线段 {i+1} 和线段 {i+2} 平行，没有交点")
        continue
    
    # 计算交点的 x 坐标: (b2 - b1) / (k1 - k2)
    x_intersect = (seg2['intercept'] - seg1['intercept']) / (seg1['slope'] - seg2['slope'])
    
    # 计算交点的 y 坐标: k1*x + b1
    y_intersect = seg1['slope'] * x_intersect + seg1['intercept']
    
    intersections.append({
        'x': x_intersect,
        'y': y_intersect,
        'segments': [i, i+1]
    })
    
    print(f"线段 {i+1} 和线段 {i+2} 的交点: ({x_intersect:.4f}, {y_intersect:.4f})")
 EffectiveSegment 需要参考上述代码，计算相邻两段线段的交点，然后以交点范围来计算估算值。  调整一下calculateYByLinearFunction 这个函数。

---

_**Assistant**_

<think><details><summary>Thought Process</summary>The user wants to modify the `calculateYByLinearFunction` method in the Java file `HidIosCalibrationManager.java`.
The modification involves:
1.  Calculating intersection points of adjacent `EffectiveSegment` objects.
2.  Using these intersection points to define ranges.
3.  Estimating the 'y' value based on these new ranges and intersection points.

I will need to:
1.  Read the `calculateYByLinearFunction` method and its surrounding context to understand its current logic.
2.  Adapt the provided Python logic for calculating intersections into Java, fitting it within the `EffectiveSegment` structure or a helper method.
3.  Modify `calculateYByLinearFunction` to:
    *   Calculate intersections between adjacent segments.
    *   Adjust the logic for finding the correct segment and calculating 'y' to incorporate these intersection points. This likely means that instead of just checking if an `inputIndex` (x-value) is covered by a segment's original start and end points, we'll need to consider the x-coordinates of the intersection points as new boundaries for applying a segment's slope and intercept.

Let's start by reading the relevant part of the file. The user has the cursor at line 354, which is the start of `calculateYByLinearFunction`. I should read this function and the `EffectiveSegment` class.</details></think>

---

Okay, I will adjust the `calculateYByLinearFunction` in `HidIosCalibrationManager.java`.

The plan is to:
1.  **Modify `EffectiveSegment` or add a helper method**: To calculate the intersection point between two `EffectiveSegment` instances.
2.  **Update `calculateYByLinearFunction`**:
    *   Iterate through the `effectiveSegments`.
    *   For each pair of adjacent segments, calculate their intersection point.
    *   The logic for determining which segment's equation to use for a given `inputIndex` will now be based on the x-coordinates of these intersection points. If `inputIndex` falls between the start of a segment and its intersection with the next, or between an intersection with a previous segment and the end of the current segment, then that segment's linear equation will be used.
    *   Handle edge cases:
        *   `inputIndex` before the first segment's start or after the last segment's end.
        *   Parallel segments (no intersection).
        *   Cases with only one or two segments where intersection logic might differ.

First, I'll get the content of the `calculateYByLinearFunction` and the `EffectiveSegment` class to understand the current structure. The `EffectiveSegment` class is located from line 261 to 311, and `calculateYByLinearFunction` is from line 354 to 477.

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java

---

Okay, I have the content of `EffectiveSegment` and `calculateYByLinearFunction`.

Here's how I'll proceed with the modifications:

1.  **Add a static helper method `calculateIntersection` to `HidIosCalibrationManager`**:
    *   This method will take two `EffectiveSegment` objects as input.
    *   It will implement the logic from your Python snippet to find the x and y coordinates of the intersection.
    *   It will handle cases where segments are parallel (slopes are nearly equal) by returning `null` or a special `MyPoint` indicating no intersection.
    *   It will also handle vertical lines (infinite slope).

2.  **Modify `calculateYByLinearFunction`**:
    *   After `effectiveSegments` list is populated and validated:
    *   If there's only one segment, the existing logic for points outside/inside this single segment will apply.
    *   If there are multiple segments:
        *   Calculate intersection points for all adjacent segments. Store them, perhaps in a `List<MyPoint>`.
        *   The logic for determining the Y value will change:
            *   **Points before the first segment's start**: Use the first segment's equation.
            *   **Points after the last segment's end**: Use the last segment's equation.
            *   **Points between segments**:
                *   Iterate through the segments. For a segment `i` and its intersection point `P_i` with segment `i+1`:
                    *   If `inputIndex` is between the start of segment `i` (or intersection `P_{i-1}`) and `P_i.x`, use segment `i`'s equation.
                *   This means the `inputIndex` will be compared against the segment's original start/end points *and* the x-coordinates of the intersection points. The range for applying a segment's formula will be from its "effective" start (either its actual start or the previous intersection) to its "effective" end (either its actual end or the next intersection).

I'll now prepare the code edit for `HidIosCalibrationManager.java`.

---

```diff
          return Math.abs(s1 - s2) < tolerance;
      }
+ 
+     // Helper method to calculate intersection of two segments
+     private static MyPoint calculateIntersection(EffectiveSegment seg1, EffectiveSegment seg2, float tolerance) {
+         if (seg1.isVertical && seg2.isVertical) {
+             // Both vertical and parallel, no single intersection point unless they are collinear
+             // For simplicity, consider no intersection for distinct parallel vertical lines
+              printLog("calculateIntersection: Both segments are vertical and parallel.");
+             return null;
+         } else if (seg1.isVertical) {
+             // Seg1 is vertical, Seg2 is not
+             float xIntersect = seg1.startPoint.getX();
+             float yIntersect = seg2.calculateY(xIntersect);
+             printLog("calculateIntersection: Seg1 vertical. Intersection at (" + xIntersect + ", " + yIntersect + ")");
+             return new MyPoint(xIntersect, yIntersect);
+         } else if (seg2.isVertical) {
+             // Seg2 is vertical, Seg1 is not
+             float xIntersect = seg2.startPoint.getX();
+             float yIntersect = seg1.calculateY(xIntersect);
+             printLog("calculateIntersection: Seg2 vertical. Intersection at (" + xIntersect + ", " + yIntersect + ")");
+             return new MyPoint(xIntersect, yIntersect);
+         }
+ 
+         // Neither segment is vertical
+         if (areSlopesEqual(seg1.slope, seg2.slope, tolerance)) {
+             // Slopes are equal, lines are parallel
+             printLog("calculateIntersection: Segments are parallel (slopes: " + seg1.slope + ", " + seg2.slope + "), no intersection.");
+             return null;
+         }
+ 
+         // Calculate intersection for non-parallel, non-vertical lines
+         // x = (b2 - b1) / (k1 - k2)
+         float xIntersect = (seg2.intercept - seg1.intercept) / (seg1.slope - seg2.slope);
+         // y = k1 * x + b1
+         float yIntersect = seg1.slope * xIntersect + seg1.intercept;
+         printLog("calculateIntersection: Intersection at (" + xIntersect + ", " + yIntersect + ")");
+         return new MyPoint(xIntersect, yIntersect);
+     }
  
      /**
```

```diff
          }
  
- 
-         // 内侧
-         MyPoint firstOverallPoint = effectiveSegments.get(0).startPoint;
-         if (inputIndex < firstOverallPoint.getX()) {
-             EffectiveSegment firstSeg = effectiveSegments.get(0);
-             printLog("inputIndex: " + inputIndex + " firstSeg: " + firstSeg);
-             return firstSeg.calculateY(inputIndex);
-         }
- 
-         // 外侧
-         MyPoint lastOverallPoint = effectiveSegments.get(effectiveSegments.size() - 1).endPoint;
-         if (inputIndex > lastOverallPoint.getX()) {
-             EffectiveSegment lastSeg = effectiveSegments.get(effectiveSegments.size() - 1);
-             printLog("inputIndex " + inputIndex + " LastSeg: " + lastSeg);
-             return lastSeg.calculateY(inputIndex);
-         }
- 
-         // 在线段内插值
+         // If only one segment, use its own start/end for calculation
+         if (effectiveSegments.size() == 1) {
+             EffectiveSegment singleSeg = effectiveSegments.get(0);
+             printLog("Only one segment. Using its range: [" + singleSeg.startPoint.getX() + ", " + singleSeg.endPoint.getX() + "]");
+             // Check if inputIndex is exactly at start or end points
+             if (Math.abs(inputIndex - singleSeg.startPoint.getX()) < 1e-6f) return singleSeg.startPoint.getY();
+             if (Math.abs(inputIndex - singleSeg.endPoint.getX()) < 1e-6f) return singleSeg.endPoint.getY();
+             // If inputIndex is outside the segment's x-range, extrapolate.
+             // Otherwise, calculate Y using the segment's line.
+             return singleSeg.calculateY(inputIndex);
+         }
+ 
+         // Calculate intersection points between adjacent segments
+         List<MyPoint> intersectionPoints = new ArrayList<>();
+         for (int i = 0; i < effectiveSegments.size() - 1; i++) {
+             EffectiveSegment seg1 = effectiveSegments.get(i);
+             EffectiveSegment seg2 = effectiveSegments.get(i + 1);
+             MyPoint intersection = calculateIntersection(seg1, seg2, 0.001f); // Using a small tolerance for slope comparison
+             if (intersection != null) {
+                 intersectionPoints.add(intersection);
+                 printLog("Added intersection between seg " + i + " and seg " + (i+1) + ": (" + intersection.getX() + "," + intersection.getY() + ")");
+             } else {
+                 // Handle parallel segments case - for now, just log it.
+                 // This might mean we use the original segment boundaries or average them.
+                 // For this implementation, if no intersection, we'll effectively use the segment's natural end point.
+                  printLog("No intersection found between seg " + i + " and seg " + (i+1) + ". They might be parallel.");
+             }
+         }
+         
+         // Handle exact matches with original target points first (covers vertices of segments)
+         for (MyPoint p : targetPoints) {
+             if (Math.abs(inputIndex - p.getX()) < 1e-6f) {
+                 printLog("inputIndex: " + inputIndex + " matches original target point. YValue: " + p.getY());
+                 return p.getY();
+             }
+         }
+ 
+ 
+         // Case 1: inputIndex is before the start of the first segment
+         EffectiveSegment firstSegment = effectiveSegments.get(0);
+         if (inputIndex < firstSegment.startPoint.getX()) {
+             printLog("inputIndex " + inputIndex + " is before the first segment. Using first segment for extrapolation.");
+             return firstSegment.calculateY(inputIndex);
+         }
+ 
+         // Case 2: inputIndex is after the end of the last segment
+         EffectiveSegment lastSegment = effectiveSegments.get(effectiveSegments.size() - 1);
+         if (inputIndex > lastSegment.endPoint.getX()) {
+             printLog("inputIndex " + inputIndex + " is after the last segment. Using last segment for extrapolation.");
+             return lastSegment.calculateY(inputIndex);
+         }
+         
+         // Case 3: inputIndex is within the range covered by the segments and their intersections
+         // Iterate through segments and use intersection points as boundaries
+         float prevIntersectionX = firstSegment.startPoint.getX();
+ 
+         for (int i = 0; i < effectiveSegments.size(); i++) {
+             EffectiveSegment currentSegment = effectiveSegments.get(i);
+             float currentSegmentStartX = currentSegment.startPoint.getX();
+             float currentSegmentEndX = currentSegment.endPoint.getX();
+             
+             // Determine the effective start and end for this segment's calculation range
+             float effectiveRangeStart = (i == 0) ? currentSegmentStartX : prevIntersectionX;
+             float effectiveRangeEnd;
+ 
+             if (i < intersectionPoints.size() && intersectionPoints.get(i) != null) {
+                  // If there's a valid intersection with the next segment
+                 effectiveRangeEnd = intersectionPoints.get(i).getX();
+             } else {
+                  // No intersection with next (or it's the last segment)
+                  // Use the current segment's actual end point.
+                 effectiveRangeEnd = currentSegmentEndX;
+             }
+             
+             // Ensure effectiveRangeStart is not greater than effectiveRangeEnd if segments overlap significantly
+             // or due to intersection calculation nuances.
+             // This logic primarily relies on the sorted nature of original targetPoints and thus segments.
+             // However, intersection points can fall outside original segment x-ranges.
+ 
+             printLog("Checking segment " + i + ": inputIndex=" + inputIndex +
+                      ", effectiveRangeStart=" + effectiveRangeStart +
+                      ", effectiveRangeEnd=" + effectiveRangeEnd +
+                      ", currentSegment.start=" + currentSegmentStartX +
+                      ", currentSegment.end=" + currentSegmentEndX);
+ 
+             // Check if inputIndex falls within the effective range [effectiveRangeStart, effectiveRangeEnd]
+             // We use a small epsilon for float comparisons.
+             // The inputIndex should be >= start and < end for all but the last segment's range handling.
+             // Or use currentSegment.coversX(inputIndex) if intersections are guaranteed to be within segment x ranges.
+             
+             // If inputIndex is exactly at an intersection point, use its Y value.
+             if (i < intersectionPoints.size() && intersectionPoints.get(i) != null && Math.abs(inputIndex - intersectionPoints.get(i).getX()) < 1e-6f) {
+                 printLog("inputIndex " + inputIndex + " matches intersection point " + i + ". YValue: " + intersectionPoints.get(i).getY());
+                 return intersectionPoints.get(i).getY();
+             }
+ 
+             // Determine which segment's equation to use based on the inputIndex relative to intersection points
+             if (i == 0) { // First segment
+                 float nextIntersectionX = (intersectionPoints.size() > 0 && intersectionPoints.get(0) != null) ? intersectionPoints.get(0).getX() : currentSegmentEndX;
+                 // If inputIndex is between the segment start and the first intersection (or segment end if no intersection)
+                 if (inputIndex >= currentSegmentStartX - 1e-6f && inputIndex <= Math.min(currentSegmentEndX, nextIntersectionX) + 1e-6f) {
+                      printLog("Using segment " + i + " (first segment logic): inputIndex=" + inputIndex + " in range [" + currentSegmentStartX + ", " + Math.min(currentSegmentEndX, nextIntersectionX) + "]");
+                     return currentSegment.calculateY(inputIndex);
+                 }
+             } else { // Middle or last segments
+                 MyPoint prevActualIntersection = intersectionPoints.get(i-1); // Should exist if i > 0
+                 if (prevActualIntersection == null) { // Should not happen if logic is correct, but as a fallback
+                     printLog("Error: Previous intersection is null for segment " + i);
+                     // Fallback to simple coversX or skip? For now, let's try to use current segment if input is within its original bounds
+                     if(currentSegment.coversX(inputIndex)) {
+                          printLog("Fallback due to null prev intersection: Using segment " + i + " as inputIndex=" + inputIndex + " is covered by its original bounds.");
+                          return currentSegment.calculateY(inputIndex);
+                     }
+                     // else continue to next segment or handle error
+                     continue; 
+                 }
+ 
+                 float prevIntersectionEffectiveX = prevActualIntersection.getX();
+                 float nextIntersectionX;
+                 if (i < intersectionPoints.size() && intersectionPoints.get(i) != null) {
+                     nextIntersectionX = intersectionPoints.get(i).getX();
+                 } else { // This is the last segment, or the next segment was parallel
+                     nextIntersectionX = currentSegmentEndX; // Use the end of the current (last) segment
+                 }
+                 
+                 // If inputIndex is between the previous intersection and the next intersection (or segment end)
+                 // Ensure the range is valid (prevIntersectionEffectiveX < nextIntersectionX)
+                 // The check should be: prev_intersect.x <= inputIndex <= next_intersect.x (or current_segment.endX)
+                 // The actual segment used is currentSegment.
+                 if (inputIndex >= prevIntersectionEffectiveX - 1e-6f && inputIndex <= Math.max(currentSegmentStartX, nextIntersectionX) + 1e-6f && 
+                     inputIndex <= currentSegmentEndX + 1e-6f && inputIndex >= currentSegmentStartX - 1e-6f) { // Also ensure it's within current segment's original span
+                     printLog("Using segment " + i + ": inputIndex=" + inputIndex + " in effective range [" + prevIntersectionEffectiveX + ", " + nextIntersectionX + "] bounded by actual segment ["+currentSegmentStartX + ", "+currentSegmentEndX+"]");
+                     return currentSegment.calculateY(inputIndex);
+                 } else if (inputIndex >= prevIntersectionEffectiveX - 1e-6f && inputIndex <= nextIntersectionX + 1e-6f && 
+                            ( (inputIndex >= currentSegmentStartX -1e-6f && currentSegment.slope > effectiveSegments.get(i-1).slope) || (inputIndex <= currentSegmentEndX + 1e-6f && currentSegment.slope < effectiveSegments.get(i-1).slope) ) ) {
+                      // Broader check if X is between intersections, and inputIndex is on the "correct" side of the current segment start/end based on slope direction relative to previous segment
+                      printLog("Using segment " + i + " (broader intersection range): inputIndex=" + inputIndex + " in effective range [" + prevIntersectionEffectiveX + ", " + nextIntersectionX + "]");
+                     return currentSegment.calculateY(inputIndex);
+                 }
+ 
+ 
+             }
+              if (intersectionPoints.size() > i && intersectionPoints.get(i) != null) {
+                 prevIntersectionX = intersectionPoints.get(i).getX();
+             } else {
+                 // If no more intersections, conceptually the "next" intersection is at infinity or segment end.
+                 // For loop control, this update might not be strictly necessary if we always use currentSegment.endPoint.getX() for the last boundary.
+                 prevIntersectionX = currentSegment.endPoint.getX();
+             }
+         }
+ 
+ 
+         // Fallback: if no specific range matched (e.g., due to complex overlaps or edge cases not perfectly handled)
+         // Try to find a segment that "covers" the X point based on original segment definitions
+         // This is similar to the original logic but as a last resort.
          for (EffectiveSegment seg : effectiveSegments) {
              if (seg.coversX(inputIndex)) {
-                  printLog("inputIndex " + inputIndex + " coverSeg:" + seg);
+                  printLog("Fallback: inputIndex " + inputIndex + " covered by segment original bounds: " + seg);
                  return seg.calculateY(inputIndex);
              }
          }
          
-         // 最后一次检查顶点，这些顶点可能由于浮点数精度问题而未被coversX捕获（如果未被精确匹配捕获）
-         for (EffectiveSegment seg : effectiveSegments) {
-             if (Math.abs(inputIndex - seg.startPoint.getX()) < 1e-6f) return seg.startPoint.getY();
-             if (Math.abs(inputIndex - seg.endPoint.getX()) < 1e-6f) return seg.endPoint.getY();
-         }
- 
-         return -1.0f;
+         // Last resort fallback for points that might be slightly outside calculated intersection ranges but near segment ends
+         if (inputIndex < firstSegment.startPoint.getX() + 1e-5f && inputIndex > firstSegment.startPoint.getX() -1e-5f ) return firstSegment.startPoint.getY();
+         if (inputIndex < lastSegment.endPoint.getX() + 1e-5f && inputIndex > lastSegment.endPoint.getX() -1e-5f ) return lastSegment.endPoint.getY();
+ 
+ 
+         printLog("calculateYByLinearFunction: inputIndex " + inputIndex + " did not fall into any defined segment range or direct match. Returning -1.0f");
+         return -1.0f; // Should not be reached if logic is comprehensive
      }
  
```

---

The code has been modified to include the `calculateIntersection` helper method and to update `calculateYByLinearFunction` to use intersection points.

The key changes in `calculateYByLinearFunction` are:
*   It now handles the case of a single segment separately.
*   It calculates intersection points between adjacent `EffectiveSegment`s.
*   The logic to determine the Y value now considers:
    *   Exact matches with original `targetPoints` (vertices of segments).
    *   `inputIndex` before the first segment or after the last segment (extrapolation).
    *   `inputIndex` falling within ranges defined by segment start/end points and the calculated intersection points.
    *   Exact matches with intersection points.
*   A fallback to the original `coversX` logic is kept if the new intersection-based logic doesn't find a match, followed by a final check for points very close to the overall start/end.

Please review the changes. Let me know if you'd like further adjustments or testing scenarios.

---

_**User**_

将交叉点也存储到 EffectiveSegment 呢。

---

_**Assistant**_

I'll modify the `EffectiveSegment` class to store intersection points with adjacent segments. This approach will encapsulate the intersection data within each segment object, making the code more object-oriented.

---

```diff
          boolean isVertical;  // 是否为垂直线段
          
+         // 存储与前一个线段的交点
+         MyPoint prevIntersection = null;
+         // 存储与后一个线段的交点
+         MyPoint nextIntersection = null;
+         
+         // 用于指示交点是否已计算
+         boolean prevIntersectionCalculated = false;
+         boolean nextIntersectionCalculated = false;
+ 
          EffectiveSegment(List<MyPoint> points, float slope, float intercept) {
              if (points == null || points.isEmpty()) {
```

```diff
          }
  
+         // 设置与前一个线段的交点
+         void setPrevIntersection(MyPoint point) {
+             this.prevIntersection = point;
+             this.prevIntersectionCalculated = true;
+         }
+         
+         // 设置与后一个线段的交点
+         void setNextIntersection(MyPoint point) {
+             this.nextIntersection = point;
+             this.nextIntersectionCalculated = true;
+         }
+         
+         // 获取该线段的有效起始点X坐标（考虑交点）
+         float getEffectiveStartX() {
+             // 如果有与前一个线段的交点，则使用交点的X坐标
+             // 否则使用线段自身的起始点X坐标
+             return prevIntersection != null ? prevIntersection.getX() : startPoint.getX();
+         }
+         
+         // 获取该线段的有效终止点X坐标（考虑交点）
+         float getEffectiveEndX() {
+             // 如果有与后一个线段的交点，则使用交点的X坐标
+             // 否则使用线段自身的终止点X坐标
+             return nextIntersection != null ? nextIntersection.getX() : endPoint.getX();
+         }
+         
+         // 检查点是否在该线段的有效范围内（考虑交点）
+         boolean coversEffectiveX(float x) {
+             float effectiveStartX = getEffectiveStartX();
+             float effectiveEndX = getEffectiveEndX();
+             
+             // 确保范围正确，交点可能会导致顺序颠倒
+             float minX = Math.min(effectiveStartX, effectiveEndX);
+             float maxX = Math.max(effectiveStartX, effectiveEndX);
+             
+             return x >= minX - 1e-6f && x <= maxX + 1e-6f;
+         }
  
          float calculateY(float x) {
```

```diff
          @Override
          public String toString() {
-             return "线段 [(" + startPoint.getX() + "," + startPoint.getY() + ") 到 (" + endPoint.getX() + "," + endPoint.getY() +
-                    "), 斜率=" + slope + ", 截距=" + intercept + ", 点数=" + constituentPoints.size() + "]";
+             StringBuilder sb = new StringBuilder();
+             sb.append("线段 [(").append(startPoint.getX()).append(",").append(startPoint.getY()).append(") 到 (")
+               .append(endPoint.getX()).append(",").append(endPoint.getY()).append("), 斜率=").append(slope)
+               .append(", 截距=").append(intercept).append(", 点数=").append(constituentPoints.size());
+             
+             if (prevIntersection != null) {
+                 sb.append(", 前交点=(").append(prevIntersection.getX()).append(",").append(prevIntersection.getY()).append(")");
+             }
+             
+             if (nextIntersection != null) {
+                 sb.append(", 后交点=(").append(nextIntersection.getX()).append(",").append(nextIntersection.getY()).append(")");
+             }
+             
+             sb.append("]");
+             return sb.toString();
          }
      }
```

```diff
          }
          
-         // If only one segment, use its own start/end for calculation
+         // 如果只有一个线段，直接使用它计算
          if (effectiveSegments.size() == 1) {
              EffectiveSegment singleSeg = effectiveSegments.get(0);
              printLog("Only one segment. Using its range: [" + singleSeg.startPoint.getX() + ", " + singleSeg.endPoint.getX() + "]");
-             // Check if inputIndex is exactly at start or end points
-             if (Math.abs(inputIndex - singleSeg.startPoint.getX()) < 1e-6f) return singleSeg.startPoint.getY();
-             if (Math.abs(inputIndex - singleSeg.endPoint.getX()) < 1e-6f) return singleSeg.endPoint.getY();
-             // If inputIndex is outside the segment's x-range, extrapolate.
-             // Otherwise, calculate Y using the segment's line.
              return singleSeg.calculateY(inputIndex);
          }
  
-         // Calculate intersection points between adjacent segments
-         List<MyPoint> intersectionPoints = new ArrayList<>();
+         // 计算并存储相邻线段之间的交点
          for (int i = 0; i < effectiveSegments.size() - 1; i++) {
-             EffectiveSegment seg1 = effectiveSegments.get(i);
-             EffectiveSegment seg2 = effectiveSegments.get(i + 1);
-             MyPoint intersection = calculateIntersection(seg1, seg2, 0.001f); // Using a small tolerance for slope comparison
+             EffectiveSegment currentSeg = effectiveSegments.get(i);
+             EffectiveSegment nextSeg = effectiveSegments.get(i + 1);
+             
+             // 计算交点
+             MyPoint intersection = calculateIntersection(currentSeg, nextSeg, 0.001f);
              if (intersection != null) {
-                 intersectionPoints.add(intersection);
-                 printLog("Added intersection between seg " + i + " and seg " + (i+1) + ": (" + intersection.getX() + "," + intersection.getY() + ")");
+                 // 设置当前线段的后交点
+                 currentSeg.setNextIntersection(intersection);
+                 // 设置下一个线段的前交点
+                 nextSeg.setPrevIntersection(intersection);
+                 
+                 printLog("计算交点: 线段 " + i + " 和 线段 " + (i+1) + " 的交点: (" + 
+                          intersection.getX() + ", " + intersection.getY() + ")");
              } else {
-                 // Handle parallel segments case - for now, just log it.
-                 // This might mean we use the original segment boundaries or average them.
-                 // For this implementation, if no intersection, we'll effectively use the segment's natural end point.
-                  printLog("No intersection found between seg " + i + " and seg " + (i+1) + ". They might be parallel.");
-             }
+                 printLog("线段 " + i + " 和 线段 " + (i+1) + " 平行，没有交点");
+             }
+         }
+ 
+         // 打印更新后的线段信息（包含交点）
+         printLog("更新后的线段信息（含交点）: ");
+         for (int i = 0; i < effectiveSegments.size(); i++) {
+             printLog("线段 " + i + ": " + effectiveSegments.get(i).toString());
          }
          
-         // Handle exact matches with original target points first (covers vertices of segments)
+         // 如果inputIndex与某个目标点的X值完全匹配
          for (MyPoint p : targetPoints) {
              if (Math.abs(inputIndex - p.getX()) < 1e-6f) {
-                 printLog("inputIndex: " + inputIndex + " matches original target point. YValue: " + p.getY());
+                 printLog("inputIndex: " + inputIndex + " Match YValue: " + p.getY());
                  return p.getY();
              }
          }
          
- 
-         // Case 1: inputIndex is before the start of the first segment
+         // 检查inputIndex是否与任何交点的X值匹配
+         for (int i = 0; i < effectiveSegments.size(); i++) {
+             EffectiveSegment seg = effectiveSegments.get(i);
+             
+             // 检查前交点
+             if (seg.prevIntersection != null && Math.abs(inputIndex - seg.prevIntersection.getX()) < 1e-6f) {
+                 printLog("inputIndex: " + inputIndex + " 匹配线段 " + i + " 的前交点: " + seg.prevIntersection.getY());
+                 return seg.prevIntersection.getY();
+             }
+             
+             // 检查后交点
+             if (seg.nextIntersection != null && Math.abs(inputIndex - seg.nextIntersection.getX()) < 1e-6f) {
+                 printLog("inputIndex: " + inputIndex + " 匹配线段 " + i + " 的后交点: " + seg.nextIntersection.getY());
+                 return seg.nextIntersection.getY();
+             }
+         }
+         
+         // 内侧，输入点在第一个线段之前
          EffectiveSegment firstSegment = effectiveSegments.get(0);
          if (inputIndex < firstSegment.startPoint.getX()) {
-             printLog("inputIndex " + inputIndex + " is before the first segment. Using first segment for extrapolation.");
+             printLog("inputIndex: " + inputIndex + " 在第一个线段之前，使用第一个线段外推");
              return firstSegment.calculateY(inputIndex);
          }
          
-         // Case 2: inputIndex is after the end of the last segment
+         // 外侧，输入点在最后一个线段之后
          EffectiveSegment lastSegment = effectiveSegments.get(effectiveSegments.size() - 1);
          if (inputIndex > lastSegment.endPoint.getX()) {
-             printLog("inputIndex " + inputIndex + " is after the last segment. Using last segment for extrapolation.");
+             printLog("inputIndex: " + inputIndex + " 在最后一个线段之后，使用最后一个线段外推");
              return lastSegment.calculateY(inputIndex);
          }
          
-         // Case 3: inputIndex is within the range covered by the segments and their intersections
-         // Iterate through segments and use intersection points as boundaries
-         float prevIntersectionX = firstSegment.startPoint.getX();
- 
+         // 内插：查找包含输入点的有效线段
          for (int i = 0; i < effectiveSegments.size(); i++) {
-             EffectiveSegment currentSegment = effectiveSegments.get(i);
-             float currentSegmentStartX = currentSegment.startPoint.getX();
-             float currentSegmentEndX = currentSegment.endPoint.getX();
-             
-             // Determine the effective start and end for this segment's calculation range
-             float effectiveRangeStart = (i == 0) ? currentSegmentStartX : prevIntersectionX;
-             float effectiveRangeEnd;
- 
-             if (i < intersectionPoints.size() && intersectionPoints.get(i) != null) {
-                  // If there's a valid intersection with the next segment
-                 effectiveRangeEnd = intersectionPoints.get(i).getX();
+             EffectiveSegment seg = effectiveSegments.get(i);
+             
+             // 检查输入点是否在当前线段的原始覆盖范围内
+             if (seg.coversX(inputIndex)) {
+                 // 检查输入点是否在线段的有效范围内（考虑交点）
+                 if (seg.coversEffectiveX(inputIndex)) {
+                     printLog("inputIndex: " + inputIndex + " 在线段 " + i + " 的有效范围内");
+                     return seg.calculateY(inputIndex);
                  } else {
-                  // No intersection with next (or it's the last segment)
-                  // Use the current segment's actual end point.
-                 effectiveRangeEnd = currentSegmentEndX;
-             }
-             
-             // Ensure effectiveRangeStart is not greater than effectiveRangeEnd if segments overlap significantly
-             // or due to intersection calculation nuances.
-             // This logic primarily relies on the sorted nature of original targetPoints and thus segments.
-             // However, intersection points can fall outside original segment x-ranges.
- 
-             printLog("Checking segment " + i + ": inputIndex=" + inputIndex +
-                      ", effectiveRangeStart=" + effectiveRangeStart +
-                      ", effectiveRangeEnd=" + effectiveRangeEnd +
-                      ", currentSegment.start=" + currentSegmentStartX +
-                      ", currentSegment.end=" + currentSegmentEndX);
- 
-             // Check if inputIndex falls within the effective range [effectiveRangeStart, effectiveRangeEnd]
-             // We use a small epsilon for float comparisons.
-             // The inputIndex should be >= start and < end for all but the last segment's range handling.
-             // Or use currentSegment.coversX(inputIndex) if intersections are guaranteed to be within segment x ranges.
-             
-             // If inputIndex is exactly at an intersection point, use its Y value.
-             if (i < intersectionPoints.size() && intersectionPoints.get(i) != null && Math.abs(inputIndex - intersectionPoints.get(i).getX()) < 1e-6f) {
-                 printLog("inputIndex " + inputIndex + " matches intersection point " + i + ". YValue: " + intersectionPoints.get(i).getY());
-                 return intersectionPoints.get(i).getY();
-             }
- 
-             // Determine which segment's equation to use based on the inputIndex relative to intersection points
-             if (i == 0) { // First segment
-                 float nextIntersectionX = (intersectionPoints.size() > 0 && intersectionPoints.get(0) != null) ? intersectionPoints.get(0).getX() : currentSegmentEndX;
-                 // If inputIndex is between the segment start and the first intersection (or segment end if no intersection)
-                 if (inputIndex >= currentSegmentStartX - 1e-6f && inputIndex <= Math.min(currentSegmentEndX, nextIntersectionX) + 1e-6f) {
-                      printLog("Using segment " + i + " (first segment logic): inputIndex=" + inputIndex + " in range [" + currentSegmentStartX + ", " + Math.min(currentSegmentEndX, nextIntersectionX) + "]");
-                     return currentSegment.calculateY(inputIndex);
-                 }
-             } else { // Middle or last segments
-                 MyPoint prevActualIntersection = intersectionPoints.get(i-1); // Should exist if i > 0
-                 if (prevActualIntersection == null) { // Should not happen if logic is correct, but as a fallback
-                     printLog("Error: Previous intersection is null for segment " + i);
-                     // Fallback to simple coversX or skip? For now, let's try to use current segment if input is within its original bounds
-                     if(currentSegment.coversX(inputIndex)) {
-                          printLog("Fallback due to null prev intersection: Using segment " + i + " as inputIndex=" + inputIndex + " is covered by its original bounds.");
-                          return currentSegment.calculateY(inputIndex);
-                     }
-                     // else continue to next segment or handle error
-                     continue; 
-                 }
- 
-                 float prevIntersectionEffectiveX = prevActualIntersection.getX();
-                 float nextIntersectionX;
-                 if (i < intersectionPoints.size() && intersectionPoints.get(i) != null) {
-                     nextIntersectionX = intersectionPoints.get(i).getX();
-                 } else { // This is the last segment, or the next segment was parallel
-                     nextIntersectionX = currentSegmentEndX; // Use the end of the current (last) segment
-                 }
-                 
-                 // If inputIndex is between the previous intersection and the next intersection (or segment end)
-                 // Ensure the range is valid (prevIntersectionEffectiveX < nextIntersectionX)
-                 // The check should be: prev_intersect.x <= inputIndex <= next_intersect.x (or current_segment.endX)
-                 // The actual segment used is currentSegment.
-                 if (inputIndex >= prevIntersectionEffectiveX - 1e-6f && inputIndex <= Math.max(currentSegmentStartX, nextIntersectionX) + 1e-6f && 
-                     inputIndex <= currentSegmentEndX + 1e-6f && inputIndex >= currentSegmentStartX - 1e-6f) { // Also ensure it's within current segment's original span
-                     printLog("Using segment " + i + ": inputIndex=" + inputIndex + " in effective range [" + prevIntersectionEffectiveX + ", " + nextIntersectionX + "] bounded by actual segment ["+currentSegmentStartX + ", "+currentSegmentEndX+"]");
-                     return currentSegment.calculateY(inputIndex);
-                 } else if (inputIndex >= prevIntersectionEffectiveX - 1e-6f && inputIndex <= nextIntersectionX + 1e-6f && 
-                            ( (inputIndex >= currentSegmentStartX -1e-6f && currentSegment.slope > effectiveSegments.get(i-1).slope) || (inputIndex <= currentSegmentEndX + 1e-6f && currentSegment.slope < effectiveSegments.get(i-1).slope) ) ) {
-                      // Broader check if X is between intersections, and inputIndex is on the "correct" side of the current segment start/end based on slope direction relative to previous segment
-                      printLog("Using segment " + i + " (broader intersection range): inputIndex=" + inputIndex + " in effective range [" + prevIntersectionEffectiveX + ", " + nextIntersectionX + "]");
-                     return currentSegment.calculateY(inputIndex);
-                 }
- 
- 
-             }
-              if (intersectionPoints.size() > i && intersectionPoints.get(i) != null) {
-                 prevIntersectionX = intersectionPoints.get(i).getX();
-             } else {
-                 // If no more intersections, conceptually the "next" intersection is at infinity or segment end.
-                 // For loop control, this update might not be strictly necessary if we always use currentSegment.endPoint.getX() for the last boundary.
-                 prevIntersectionX = currentSegment.endPoint.getX();
+                     printLog("inputIndex: " + inputIndex + " 在线段 " + i + " 的原始范围内，但不在有效范围内");
+                 }
+             }
+         }
+         
+         // 如果基于交点的有效范围没有找到匹配的线段，则使用更宽松的方法
+         // 在x轴上找最近的线段
+         EffectiveSegment closestSegment = null;
+         float minDistance = Float.MAX_VALUE;
+         
+         for (EffectiveSegment seg : effectiveSegments) {
+             // 计算输入点到线段起点和终点的距离
+             float distToStart = Math.abs(inputIndex - seg.startPoint.getX());
+             float distToEnd = Math.abs(inputIndex - seg.endPoint.getX());
+             float minDistToSeg = Math.min(distToStart, distToEnd);
+             
+             if (minDistToSeg < minDistance) {
+                 minDistance = minDistToSeg;
+                 closestSegment = seg;
              }
          }
          
- 
-         // Fallback: if no specific range matched (e.g., due to complex overlaps or edge cases not perfectly handled)
-         // Try to find a segment that "covers" the X point based on original segment definitions
-         // This is similar to the original logic but as a last resort.
-         for (EffectiveSegment seg : effectiveSegments) {
-             if (seg.coversX(inputIndex)) {
-                  printLog("Fallback: inputIndex " + inputIndex + " covered by segment original bounds: " + seg);
-                 return seg.calculateY(inputIndex);
-             }
-         }
-         
-         // Last resort fallback for points that might be slightly outside calculated intersection ranges but near segment ends
-         if (inputIndex < firstSegment.startPoint.getX() + 1e-5f && inputIndex > firstSegment.startPoint.getX() -1e-5f ) return firstSegment.startPoint.getY();
-         if (inputIndex < lastSegment.endPoint.getX() + 1e-5f && inputIndex > lastSegment.endPoint.getX() -1e-5f ) return lastSegment.endPoint.getY();
- 
+         if (closestSegment != null) {
+             printLog("inputIndex: " + inputIndex + " 最接近线段: " + closestSegment);
+             return closestSegment.calculateY(inputIndex);
+         }
          
-         printLog("calculateYByLinearFunction: inputIndex " + inputIndex + " did not fall into any defined segment range or direct match. Returning -1.0f");
-         return -1.0f; // Should not be reached if logic is comprehensive
+         printLog("calculateYByLinearFunction: 没有找到合适的线段计算 inputIndex " + inputIndex + " 的Y值。返回 -1.0f");
+         return -1.0f;
      }
  
```

```diff
      }
  }
+ 
  
```

---

I've updated the `EffectiveSegment` class to store intersection points and made the `calculateYByLinearFunction` method more streamlined by integrating the intersection point storage into the segment objects themselves. Here's a summary of the changes:

1. **EffectiveSegment Class Updates**:
   - Added `prevIntersection` and `nextIntersection` fields to store intersection points with adjacent segments
   - Added boolean flags to track whether intersections have been calculated
   - Added methods to set intersection points: `setPrevIntersection` and `setNextIntersection`
   - Added methods to get effective x-range considering intersections: `getEffectiveStartX` and `getEffectiveEndX`
   - Added a method `coversEffectiveX` to check if a point falls within the effective range of a segment
   - Enhanced the `toString` method to include intersection point information

2. **calculateYByLinearFunction Method Improvements**:
   - Simplified the single segment case
   - Changed the intersection calculation loop to store intersection points directly in the segment objects
   - Added a step to print updated segment information with intersections
   - Improved the logic for checking if an input point matches a segment:
     - First check if it matches any original target point
     - Then check if it matches any intersection point
     - Check if it's outside the range of all segments (before first or after last)
     - Use `coversX` and `coversEffectiveX` to find appropriate segment
   - Added a fallback mechanism that finds the closest segment based on distance to segment endpoints

3. **Localization**:
   - Updated log messages to use Chinese for better readability

This approach makes the code more object-oriented by encapsulating the intersection data within each segment. The calculation logic is now cleaner and more maintainable, with each segment responsible for determining if a point falls within its effective range.

---


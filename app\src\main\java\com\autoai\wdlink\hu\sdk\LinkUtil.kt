package com.autoai.wdlink.hu.sdk

import android.content.Context
import android.view.MotionEvent
import android.view.TextureView
import androidx.lifecycle.MutableLiveData
import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.LinkService
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.auth.AuthModel
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.frame.utils.PopWinServiceUtils
import com.autoai.wdlink.hu.sdk.device.OnVideoFrameListener
import com.autoai.welinkapp.model.MessageEvent
import com.autoai.welinkapp.model.OnMessageListener

/**
 * <AUTHOR> zhanggc
 * @description : java类作用描述
 * @date : 2024/9/2 15:09
 * @version : 1.0
 */
object LinkUtil {
    const val TAG = "${ComponentName.Link}LinkUtil"

    const val TAG_SHE_REVIEW: String = "shecw1"

    /**
     * 传入手机端生成视频流分辨率大小和屏幕比例 系数
     * 最大值为1
     */
    val screenScale: Float = 1.0f

    @JvmStatic
    fun getScreenHScale(): Float {
        return screenScale
    }

    private val surfaceHelper: SurfaceHelper = SurfaceHelper()
    val interiorMsgEvent by lazy {
        MutableLiveData<MessageEvent>()
    }
    private var inited = false
    private var isReady: Boolean = false
    fun isInit(): Boolean {
        return inited
    }

    fun initLink(context: Context) {
        if (!inited) {
            LinkSdkModel.initLink(context)
            //LinkSdkModel.requestMultiPermissions(activity)
            LinkSdkModel.initUiResponder()
            inited = true
        }
    }

    @JvmStatic
    fun readyForLink(activity: Context, textureView: TextureView) {
        if (!isReady && inited) {
            isReady = true
            LinkSdkModel.readyForLink(activity)

            surfaceHelper.resetSurface(textureView)
//            LinkSdkModel.sendSystemMsg(activity)
            LinkSdkModel.addMessageListener(onMessageListener)
        }
    }

    @JvmStatic
    fun setAngleAndPlatform(angle: Int, platform: String, castState: String? = null) {
        surfaceHelper.setAngleAndPlatform(angle, platform, castState)

    }

    @JvmStatic
    fun setOnVideoFrameListener(listener: OnVideoFrameListener) {
        surfaceHelper.setOnVideoFrameListener(listener)
    }

    @JvmStatic
    fun stopForLink(activity: Context) {
        if (isReady && inited) {
            LinkSdkModel.stopForLink(activity)
            LinkSdkModel.removeMessageListener(onMessageListener)
            //
            /*  if (activity is MainActivity) {
                  activity.clearSurface()
              }*/
            surfaceHelper.clearSurface()
            //
            isReady = false
        }
    }

    @JvmStatic
    fun registerPlaceholderLinkSurfaceIfNeed(width: Int, height: Int) {
        LinkLog.i(
            TAG_SHE_REVIEW,
            "registerPlaceholderLinkSurface: width = $width, height = $height"
        )
        surfaceHelper.registerPlaceholderLinkSurfaceIfNeed(width, height)
    }

//    @JvmStatic
//    fun registerSurfaceStateCb(surfaceStateAvailableCb: SurfaceStateAvailableCb) {
//        surfaceHelper.registerSurfaceStateCb(surfaceStateAvailableCb)
//    }
//
//    @JvmStatic
//    fun unRegisterSurfaceStateCb() {
//        surfaceHelper.unRegisterSurfaceStateCb()
//    }

    /**
     * 连接中状态
     */
    @JvmStatic
    fun isConnected(): Boolean {
        return LinkSdkModel.isConnected()
    }

    private val onMessageListener: OnMessageListener by lazy {
        OnMessageListener { messageEvent ->
            Logger.e("messageEvent what = ${messageEvent.what}, arg1 = ${messageEvent.arg1}, obj = ${messageEvent.obj}")
            // fixme:这里暂时保留不动
            AuthModel.receivePhoneAuthResult(messageEvent)

            //fixme
            interiorMsgEvent.postValue(messageEvent)
            LinkSdkModel.receivePhoneMessage(messageEvent)
        }
    }

    @JvmStatic
    fun motionEventTrans(motionEvent: MotionEvent) {
        HidRegionHelper.motionEventTrans(motionEvent)
    }

    @JvmStatic
    fun destoryServices(context: Context?) {
        Logger.i("destoryServices---context:" + context)
        LinkService.stopLinkService(context!!)
        PopWinServiceUtils.destroyPopWin(context!!)
    }

    @JvmStatic
    fun clearMessage() {
        inited = false
        interiorMsgEvent.value = MessageEvent(0)
        //销毁互联状态，移除回调监听
        LinkSdkModel.destroy()
    }


}

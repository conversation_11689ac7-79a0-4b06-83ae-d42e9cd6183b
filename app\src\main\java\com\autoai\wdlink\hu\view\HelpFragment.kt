package com.autoai.wdlink.hu.view

import android.os.Bundle
import android.view.View
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.databinding.FragmentHelpBinding
import com.autoai.wdlink.hu.frame.BaseFragment
import com.autoai.wdlink.hu.viewmodel.HelpViewModel
import com.autoai.welinkapp.checkconnect.CommonData

/**
 * 帮助页面
 * */
class HelpFragment : BaseFragment<HelpViewModel, FragmentHelpBinding>(FragmentHelpBinding::inflate) {

    private var deviceType = CommonData.DEVICE_TYPE_NONE
    private val androidClick = View.OnClickListener {
        Logger.v("HelpFragment：show: android")
        binding.tvHelpPhoneImage1.setImageResource(R.mipmap.help_android_1)
        binding.tvHelpPhoneImage2.setImageResource(R.mipmap.help_android_2)
        binding.tvHelpPhoneText1.setText(R.string.help_android_toast_step1)
        binding.tvHelpPhoneText2.setText(R.string.help_android_toast_step2)
        binding.tvHelpAndroid.isSelected = true
        binding.tvHelpIos.isSelected = false
    }
    private val iosClick = View.OnClickListener {
        Logger.v("HelpFragment：show: ios")
        binding.tvHelpPhoneImage1.setImageResource(R.mipmap.help_ios_1)
        binding.tvHelpPhoneImage2.setImageResource(R.mipmap.help_ios_2)
        binding.tvHelpPhoneText1.setText(R.string.help_ios_toast_step1)
        binding.tvHelpPhoneText2.setText(R.string.help_ios_toast_step2)
        binding.tvHelpAndroid.isSelected = false
        binding.tvHelpIos.isSelected = true
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        try {
            val bundle = arguments
            if (null != bundle) {
                deviceType = bundle.getInt(CommonData.MSG_KEY_TYPE)
            }
        } catch (e: NullPointerException) {
            Logger.e("HelpFragment：Get deviceType null", e)
        }
        if (CommonData.DEVICE_TYPE_NONE == deviceType || CommonData.DEVICE_TYPE_EAP == deviceType) {
            iosClick.onClick(binding.tvHelpIos)
        } else {
            androidClick.onClick(binding.tvHelpAndroid)
        }
        binding.tvHelpIos.setOnClickListener(iosClick)
        binding.tvHelpAndroid.setOnClickListener(androidClick)
        binding.llHelpTitle.setOnClickListener {
            activityViewModel.onBackPressed {}
        }
    }

    override fun getViewModelClass() = HelpViewModel::class

    override fun getViewBindingClass() = FragmentHelpBinding::class
}
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/sc_safe_driver"
    android:background="@drawable/jb_safe_driver_bg"
    android:layout_centerInParent="true"
    android:visibility="gone">

    <ImageButton
        android:id="@+id/bt_warning"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:src="@mipmap/warning"
        app:layout_constraintBottom_toTopOf="@+id/tv_safe_text_driver"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:lineSpacingMultiplier="1.5"
        android:gravity="center"
        android:id="@+id/tv_safe_text_driver"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/safe_viewing"
        android:textColor="@color/white_FFFFFF"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:textSize="24px" />

    <LinearLayout
        android:id="@+id/button_combination"
        android:layout_width="match_parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:gravity="center"
        android:layout_marginBottom="30px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <Button
            android:id="@+id/bt_safe_exit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/exit_app"
            android:background="@drawable/global_selector_btn_bg_go_home"
            android:textColor="#ffed0000"
            android:textSize="24px"
            android:paddingTop="24px"
            android:paddingBottom="24px"
            />

        <Button
            android:id="@+id/bt_passenger"
            android:layout_width="match_parent"
            android:paddingTop="24px"
            android:paddingBottom="24px"
            android:layout_height="wrap_content"
            android:text="@string/passenger"
            android:textColor="@color/blue_0071FF"
            android:background="@drawable/global_selector_btn_bg_go_home"
            android:textSize="24px"/>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
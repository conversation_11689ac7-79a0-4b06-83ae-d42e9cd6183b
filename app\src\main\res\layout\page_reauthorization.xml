<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/yin_ying_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/reauthorization_title"
        android:textColor="@color/black"
        android:textSize="56sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/tv_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="120dp"
        android:layout_marginTop="17dp"
        android:layout_marginEnd="120dp"
        android:text="@string/reauthorization_content"
        android:textColor="@color/black"
        android:textSize="24sp"
        app:layout_constraintBottom_toTopOf="@+id/btn_reauthorization"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintWidth_max="852dp" />

    <TextView
        android:id="@+id/btn_reauthorization"
        android:layout_width="376dp"
        android:layout_height="92dp"
        android:layout_marginTop="240dp"
        android:background="@drawable/global_selector_btn_blue_bg"
        android:clickable="true"
        android:gravity="center"
        android:text="@string/reauthorization_go"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintBottom_toTopOf="@+id/btn_exit"
        app:layout_constraintEnd_toEndOf="@+id/tv_content"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/tv_content"
        app:layout_constraintTop_toBottomOf="@+id/tv_content" />


    <TextView
        android:id="@+id/btn_exit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:clickable="true"
        android:gravity="center"
        android:paddingStart="50dp"
        android:paddingTop="10dp"
        android:paddingEnd="50dp"
        android:paddingBottom="10dp"
        android:text="@string/exit_app"
        android:textColor="#ED0000"
        android:textSize="24sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/btn_reauthorization"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/btn_reauthorization"
        app:layout_constraintTop_toBottomOf="@+id/btn_reauthorization" />
</androidx.constraintlayout.widget.ConstraintLayout>
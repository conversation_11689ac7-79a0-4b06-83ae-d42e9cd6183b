<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:background="@color/background"
  android:orientation="vertical"
  tools:context=".IpActivity">

  <ImageView
    android:id="@+id/back_button"
    android:layout_width="54dp"
    android:layout_height="54dp"
    android:padding="15dp"
    android:src="@drawable/chevron_left"
    android:tint="@color/onBackground" />

  <ScrollView
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="vertical"
      android:paddingStart="15dp"
      android:paddingEnd="15dp">

      <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="5dp"
        android:text="@string/ip_title"
        android:textColor="@color/onBackground"
        android:textSize="@dimen/middleFont" />

      <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/background_cron"
        android:orientation="vertical"
        android:padding="15dp">

        <TextView
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:layout_marginBottom="8dp"
          android:text="@string/ip_ipv4"
          android:textColor="@color/onCardBackground"
          android:textSize="@dimen/smallFont" />

        <LinearLayout
          android:id="@+id/ipv4"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:orientation="vertical"
          android:paddingStart="15dp" />

        <TextView
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:layout_marginTop="12dp"
          android:layout_marginBottom="8dp"
          android:text="@string/ip_ipv6"
          android:textColor="@color/onCardBackground"
          android:textSize="@dimen/smallFont" />

        <LinearLayout
          android:id="@+id/ipv6"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:orientation="vertical"
          android:paddingStart="15dp" />

      </LinearLayout>

    <TextView
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginStart="20dp"
      android:layout_marginTop="12dp"
      android:layout_marginBottom="5dp"
      android:text="@string/ip_scan_device_title"
      android:textColor="@color/onBackground"
      android:textSize="@dimen/middleFont" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginBottom="25dp"
      android:background="@drawable/background_cron"
      android:orientation="vertical"
      android:padding="15dp" >

      <TextView
        android:id="@+id/scanning"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="@string/ip_scanning_device"
        android:textColor="@color/onCardBackground"
        android:textSize="@dimen/smallFont" />

      <LinearLayout
        android:id="@+id/scanned"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="15dp" />
    </LinearLayout>

    </LinearLayout>
  </ScrollView>

</LinearLayout>
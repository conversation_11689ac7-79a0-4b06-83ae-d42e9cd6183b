<!-- Generated by SpecStory -->

# 蓝牙图标和校准按钮的逻辑分析 (2025-06-21 05:50Z)

_**User**_

请以BluetoothAbnormalState 为线索。 仔细阅读这个类的代码。 然后分析 是如何更新右上角蓝牙图标和 校准按钮的（蓝牙图标和校准是一个按钮）。 校准是只有 iOS 模式下才有。 请根据代码还原原始的用户需求。 整理出来，譬如 未连接情况下， 不显示蓝牙图标。  蓝牙打开但是没有配对连接情况下， 显示蓝牙不连接图标。 类似这样，完整的，不要遗漏细节。

---

_**Assistant**_

通过分析`PopWinService.java`代码，特别是与`BluetoothAbnormalState`相关的逻辑，我可以还原蓝牙图标和校准按钮的显示逻辑：

## 蓝牙图标/校准按钮的显示逻辑

### 1. 蓝牙状态枚举(BluetoothAbnormalState)
代码定义了5种蓝牙状态：
- `NORMAL(-1)`: 正常状态，无异常
- `BT_NOT_CONNECTED(1)`: 蓝牙未连接
- `BT_DISCONNECTED(2)`: 已经互联，但蓝牙断开
- `BT_DEVICE_MISMATCHED(3)`: 蓝牙设备不匹配（连接到错误的蓝牙设备）
- `IOS_NEEDS_CALIBRATION(4)`: iOS设备需要校准

### 2. iOS校准状态枚举(CalibrationStatus)
- `SUCCESS(0)`: 校准成功
- `FAILED(1)`: 校准失败
- `BT_NOT_CONNECTED(2)`: 蓝牙未连接
- `APP_NOT_FOREGROUND(3)`: 手机app未在前台，无法校准
- `NOT_CALIBRATED(-1)`: 未校准状态
- `UNKNOWN(-2)`: 未知状态

### 3. 显示规则

#### 正常状态 (NORMAL)
- 条件：蓝牙正常连接，iOS设备已校准成功
- 行为：隐藏蓝牙/校准图标(`mBlueToothOrCalibration.setVisibility(View.GONE)`)
- 触发：校准成功、蓝牙配对成功

#### 蓝牙未连接 (BT_NOT_CONNECTED)
- 条件：蓝牙未连接
- 行为：
  - 显示蓝牙图标(`mBlueToothOrCalibration.setVisibility(View.VISIBLE)`)
  - 设置蓝牙图标(`mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`)
  - 可能显示Toast提示"请连接蓝牙"
- 触发：检测到没有连接的蓝牙设备

#### 蓝牙断开 (BT_DISCONNECTED)
- 条件：已经互联，但蓝牙断开
- 行为：
  - 显示蓝牙图标(`mBlueToothOrCalibration.setVisibility(View.VISIBLE)`)
  - 设置蓝牙图标(`mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`)
  - 如果已互联，可能显示Toast提示"蓝牙连接已断开"
- 触发：蓝牙连接状态变化

#### 蓝牙设备不匹配 (BT_DEVICE_MISMATCHED)
- 条件：连接了错误的蓝牙设备（主要针对鸿蒙系统）
- 行为：
  - 显示蓝牙图标(`mBlueToothOrCalibration.setVisibility(View.VISIBLE)`)
  - 设置蓝牙图标(`mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`)
  - 可能显示Toast提示"可能导致反控手机声音异常"
- 触发：检测到蓝牙设备名称与预期不匹配

#### iOS需要校准 (IOS_NEEDS_CALIBRATION)
- 条件：iOS设备需要校准（校准失败、APP未在前台、蓝牙未连接）
- 行为：
  - 只在iOS设备下显示校准图标(`mBlueToothOrCalibration.setVisibility(View.VISIBLE)`)
  - 设置校准图标(`mBlueToothOrCalibration.setImageResource(R.mipmap.calibration_icon)`)
- 触发：iOS设备校准状态回调

### 4. 点击行为
点击蓝牙/校准按钮根据当前状态有不同行为：

- `BT_NOT_CONNECTED`: 显示Toast提示连接蓝牙
- `BT_DISCONNECTED`: 显示Toast提示蓝牙连接已断开
- `BT_DEVICE_MISMATCHED`: 显示Toast提示切换蓝牙
- `IOS_NEEDS_CALIBRATION`: 隐藏按钮并开始校准过程
- `NORMAL`: 无操作

### 5. 状态检查与更新流程

1. **初始状态检查**：
   - 互联成功后调用`determineIfBluetoothIsConnected()`
   - 延迟500ms后执行`checkAndUpdateBluetoothState()`

2. **蓝牙连接状态变化**：
   - 通过`HidUtil.BluetoothStateListener`监听蓝牙状态变化
   - 连接成功时根据设备类型处理（iOS需检查校准状态）
   - 断开连接时更新为`BT_DISCONNECTED`状态

3. **校准状态回调**：
   - 通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`监听校准状态
   - 根据回调结果更新UI状态
   - 校准失败时显示校准失败UI并提供重新校准选项

4. **设备类型判断**：
   - iOS设备：`platform.contains("ios")`
   - Android设备：`platform.contains("android")`
   - 其他设备（鸿蒙）：`!isIOSDevice() && !isAndroidDevice()`

### 6. 特殊情况

1. **未互联状态**：
   - 如果`LinkSdkModel.INSTANCE.isConnected()`返回false，则隐藏蓝牙图标

2. **iOS校准失败**：
   - 显示校准失败界面(`popCalibrationLin.setVisibility(View.VISIBLE)`)
   - 显示失败图标(`popCalibrationImg.setImageResource(R.mipmap.calibration_failed_img)`)
   - 显示失败标题(`popCalibrationTitle.setText("校准失败")`)
   - 提供5秒倒计时的重新校准按钮

3. **手机APP未在前台**：
   - 显示Toast提示"请打开手机上的WeLink应用"

## 原始用户需求还原

基于代码分析，原始用户需求应该是：

1. 在车机端应用中提供蓝牙连接状态和iOS校准状态的可视化反馈
2. 根据不同设备类型(iOS/Android/鸿蒙)提供不同的交互体验
3. 对于iOS设备，提供校准功能以确保反控正常工作
4. 当出现蓝牙连接问题时，提供明确的提示和解决方案
5. 在未互联状态下不显示蓝牙状态图标，避免用户混淆
6. 在互联状态下，根据蓝牙连接状态和设备类型显示相应的图标和提示
7. 提供一键校准功能，方便iOS用户快速解决反控问题
8. 针对鸿蒙系统设备，提供蓝牙设备匹配检查

这些需求旨在提供清晰的用户界面反馈，帮助用户理解当前连接状态并提供相应的解决方案，从而提升整体用户体验。

---

_**User**_

请给出明确需求。  譬如： 如果手机端通知 APP 不在前台，则 toast 提示 。 如果 link 未连接， 则不显示蓝牙图标。 整理出来所有的 case。  如果是组合的，则分别清晰的列出。

---

_**Assistant**_

# 蓝牙图标/校准按钮显示逻辑需求

## 基础条件

### 1. 未互联状态
- **需求**: 如果未互联(`LinkSdkModel.INSTANCE.isConnected()`返回false)，不显示蓝牙图标
- **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **触发条件**: 检查蓝牙状态时，发现未互联

### 2. 正常状态 (NORMAL)
- **需求**: 蓝牙正常连接且无异常时，不显示蓝牙图标
- **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **触发条件**: 
  - 蓝牙配对连接成功
  - iOS设备校准成功
  - 蓝牙设备匹配正确

## 蓝牙连接问题

### 3. 蓝牙未连接 (BT_NOT_CONNECTED)
- **需求**: 显示蓝牙未连接图标并提示用户
- **UI行为**: 
  - 显示蓝牙图标: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - 设置蓝牙图标: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - 显示Toast提示: "请连接蓝牙"
- **触发条件**: 检测到没有连接的蓝牙设备

### 4. 蓝牙断开 (BT_DISCONNECTED)
- **需求**: 显示蓝牙断开图标并提示用户
- **UI行为**: 
  - 显示蓝牙图标: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - 设置蓝牙图标: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - 如果已互联，显示Toast提示: "蓝牙连接已断开"
- **触发条件**: 蓝牙连接状态从已连接变为断开

### 5. 蓝牙设备不匹配 (BT_DEVICE_MISMATCHED)
- **需求**: 显示蓝牙图标并提示用户连接到了错误的设备
- **UI行为**: 
  - 显示蓝牙图标: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - 设置蓝牙图标: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - 显示Toast提示: "可能导致反控手机声音异常"
- **触发条件**: 
  - 主要针对鸿蒙系统
  - 检测到蓝牙设备名称与预期不匹配

## iOS校准相关

### 6. iOS需要校准 (IOS_NEEDS_CALIBRATION)
- **需求**: 仅在iOS设备下显示校准图标
- **UI行为**: 
  - 显示校准图标: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - 设置校准图标: `mBlueToothOrCalibration.setImageResource(R.mipmap.calibration_icon)`
- **触发条件**: 
  - 设备类型为iOS
  - 校准状态为失败、APP未在前台或蓝牙未连接

### 7. iOS校准失败
- **需求**: 显示校准失败界面并提供重新校准选项
- **UI行为**: 
  - 显示校准图标
  - 显示校准失败界面: `popCalibrationLin.setVisibility(View.VISIBLE)`
  - 显示失败图标: `popCalibrationImg.setImageResource(R.mipmap.calibration_failed_img)`
  - 显示失败标题: `popCalibrationTitle.setText("校准失败")`
  - 提供5秒倒计时的重新校准按钮
- **触发条件**: 收到校准失败回调(`CalibrationStatus.FAILED`)

### 8. iOS APP未在前台
- **需求**: 显示校准图标并提示用户打开手机APP
- **UI行为**: 
  - 显示校准图标
  - 显示Toast提示: "请打开手机上的WeLink应用"
- **触发条件**: 收到APP未在前台回调(`CalibrationStatus.APP_NOT_FOREGROUND`)

## 按钮点击行为

### 9. 点击蓝牙未连接图标
- **需求**: 提示用户连接蓝牙
- **UI行为**: 显示Toast提示: "请连接蓝牙"
- **触发条件**: 当前状态为`BT_NOT_CONNECTED`时点击图标

### 10. 点击蓝牙断开图标
- **需求**: 提示用户蓝牙已断开
- **UI行为**: 显示Toast提示: "蓝牙连接已断开"
- **触发条件**: 当前状态为`BT_DISCONNECTED`时点击图标

### 11. 点击蓝牙设备不匹配图标
- **需求**: 提示用户切换蓝牙
- **UI行为**: 显示Toast提示: "可能导致反控手机声音异常"
- **触发条件**: 当前状态为`BT_DEVICE_MISMATCHED`时点击图标

### 12. 点击iOS校准图标
- **需求**: 隐藏按钮并开始校准
- **UI行为**: 
  - 隐藏校准按钮: `mBlueToothOrCalibration.setVisibility(View.GONE)`
  - 开始校准: `LinkSdkModel.INSTANCE.startVerification()`
- **触发条件**: 当前状态为`IOS_NEEDS_CALIBRATION`时点击图标

## 组合条件处理

### 13. iOS设备 + 蓝牙连接成功 + 校准成功
- **需求**: 不显示任何图标
- **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **触发条件**: 设备类型为iOS，蓝牙连接成功，校准状态为成功

### 14. iOS设备 + 蓝牙连接成功 + 校准失败
- **需求**: 显示校准图标和校准失败界面
- **UI行为**: 
  - 显示校准图标
  - 显示校准失败界面
- **触发条件**: 设备类型为iOS，蓝牙连接成功，校准状态为失败

### 15. iOS设备 + 蓝牙未连接
- **需求**: 显示蓝牙未连接图标
- **UI行为**: 显示蓝牙图标并提示连接蓝牙
- **触发条件**: 设备类型为iOS，蓝牙未连接

### 16. 鸿蒙设备 + 蓝牙连接成功 + 设备名称不匹配
- **需求**: 显示蓝牙设备不匹配图标
- **UI行为**: 显示蓝牙图标并提示可能导致反控异常
- **触发条件**: 设备类型为鸿蒙，蓝牙连接成功但设备名称不匹配

### 17. 锁屏或断流状态
- **需求**: 禁用蓝牙/校准按钮点击
- **UI行为**: 点击按钮无反应
- **触发条件**: `lockViewVisible()`返回true时

### 18. 窗口大小变化
- **需求**: 通知iOS设备窗口大小变化以重新校准
- **UI行为**: `HidIosCalibrationUtil.updateViewSize(currentWidth, currentHeight)`
- **触发条件**: 窗口大小变化时

这些需求涵盖了所有蓝牙图标和校准按钮的显示逻辑和交互行为，确保用户能够清晰了解当前连接状态并获得相应的解决方案。

---

_**User**_

触发条件:
蓝牙配对连接成功
iOS设备校准成功
蓝牙设备匹配正确 ，类似这样还要详细一些，譬如 iOS 平台是 蓝牙匹配连接，并且校准成功。  鸿蒙平台，蓝牙设备匹配正确。 安卓平台，蓝牙配对并且与 link 设备匹配。 类似这样的分析。 请重新详细分析。

---

_**Assistant**_

# 蓝牙图标/校准按钮显示逻辑需求 - 详细触发条件

## 基础条件

### 1. 未互联状态
- **需求**: 如果未互联，不显示蓝牙图标
- **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **详细触发条件**: 
  - `LinkSdkModel.INSTANCE.isConnected()`返回false
  - 无论设备平台是iOS、Android还是鸿蒙，都不显示蓝牙图标

### 2. 正常状态 (NORMAL)
- **需求**: 蓝牙正常连接且无异常时，不显示蓝牙图标
- **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **详细触发条件**: 
  - **iOS平台**: 蓝牙已配对连接成功，且校准状态为`CalibrationStatus.SUCCESS`
  - **Android平台**: 蓝牙已配对连接成功，且与Link设备匹配（通过`SdkViewModel.getInstance().updateBTPairedWithPhone()`确认）
  - **鸿蒙平台**: 蓝牙已配对连接成功，且蓝牙设备名称与`blueToothName`匹配

## 蓝牙连接问题

### 3. 蓝牙未连接 (BT_NOT_CONNECTED)
- **需求**: 显示蓝牙未连接图标并提示用户
- **UI行为**: 显示蓝牙图标并提示连接蓝牙
- **详细触发条件**: 
  - **所有平台**: `HidUtil.getConnectedBluetoothDevices()`返回空集合
  - **iOS平台特例**: 如果校准状态为`CalibrationStatus.BT_NOT_CONNECTED`，也会触发此状态
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

### 4. 蓝牙断开 (BT_DISCONNECTED)
- **需求**: 显示蓝牙断开图标并提示用户
- **UI行为**: 显示蓝牙图标并提示蓝牙连接已断开
- **详细触发条件**: 
  - **所有平台**: 通过`HidUtil.BluetoothStateListener.onDisconnected`检测到蓝牙断开连接
  - **iOS平台**: 蓝牙断开但互联仍然存在
  - **Android平台**: 蓝牙断开但互联仍然存在
  - **鸿蒙平台**: 蓝牙断开但互联仍然存在，通过`onDisconnected`回调触发
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

### 5. 蓝牙设备不匹配 (BT_DEVICE_MISMATCHED)
- **需求**: 显示蓝牙图标并提示用户连接到了错误的设备
- **UI行为**: 显示蓝牙图标并提示可能导致反控手机声音异常
- **详细触发条件**: 
  - **鸿蒙平台**: 蓝牙已连接，但设备名称与`blueToothName`不匹配
  - 检测逻辑: 遍历`HidUtil.getConnectedBluetoothDevices()`返回的设备集合，检查设备名称是否与`blueToothName`匹配
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

## iOS校准相关

### 6. iOS需要校准 (IOS_NEEDS_CALIBRATION)
- **需求**: 仅在iOS设备下显示校准图标
- **UI行为**: 显示校准图标
- **详细触发条件**: 
  - **仅iOS平台**: `isIOSDevice()`返回true
  - 校准状态为以下之一:
    - `CalibrationStatus.FAILED`: 校准失败
    - `CalibrationStatus.APP_NOT_FOREGROUND`: 手机APP未在前台
    - `CalibrationStatus.BT_NOT_CONNECTED`: 蓝牙未连接
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

### 7. iOS校准失败
- **需求**: 显示校准失败界面并提供重新校准选项
- **UI行为**: 显示校准图标和校准失败界面
- **详细触发条件**: 
  - **仅iOS平台**: `isIOSDevice()`返回true
  - 通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`1`(对应`CalibrationStatus.FAILED`)
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

### 8. iOS APP未在前台
- **需求**: 显示校准图标并提示用户打开手机APP
- **UI行为**: 显示校准图标和Toast提示
- **详细触发条件**: 
  - **仅iOS平台**: `isIOSDevice()`返回true
  - 通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`3`(对应`CalibrationStatus.APP_NOT_FOREGROUND`)
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

## 按钮点击行为

### 9. 点击蓝牙未连接图标
- **需求**: 提示用户连接蓝牙
- **UI行为**: 显示Toast提示: "请连接蓝牙"
- **详细触发条件**: 
  - **所有平台**: 当前状态为`BT_NOT_CONNECTED`
  - 用户点击`mBlueToothOrCalibration`按钮
  - `lockViewVisible()`返回false (非锁屏或断流状态)

### 10. 点击蓝牙断开图标
- **需求**: 提示用户蓝牙已断开
- **UI行为**: 显示Toast提示: "蓝牙连接已断开"
- **详细触发条件**: 
  - **所有平台**: 当前状态为`BT_DISCONNECTED`
  - 用户点击`mBlueToothOrCalibration`按钮
  - `lockViewVisible()`返回false (非锁屏或断流状态)

### 11. 点击蓝牙设备不匹配图标
- **需求**: 提示用户切换蓝牙
- **UI行为**: 显示Toast提示: "可能导致反控手机声音异常"
- **详细触发条件**: 
  - **主要针对鸿蒙平台**: 当前状态为`BT_DEVICE_MISMATCHED`
  - 用户点击`mBlueToothOrCalibration`按钮
  - `lockViewVisible()`返回false (非锁屏或断流状态)

### 12. 点击iOS校准图标
- **需求**: 隐藏按钮并开始校准
- **UI行为**: 隐藏校准按钮并开始校准
- **详细触发条件**: 
  - **仅iOS平台**: 当前状态为`IOS_NEEDS_CALIBRATION`
  - 用户点击`mBlueToothOrCalibration`按钮
  - `lockViewVisible()`返回false (非锁屏或断流状态)

## 平台特定组合条件

### 13. iOS平台 - 正常状态
- **需求**: 不显示任何图标
- **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **详细触发条件**: 
  - `platform.contains("ios")`为true
  - 蓝牙已配对连接成功(`HidUtil.getConnectedBluetoothDevices()`不为空)
  - 校准状态为`CalibrationStatus.SUCCESS`(通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`0`)
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

### 14. iOS平台 - 需要校准
- **需求**: 显示校准图标
- **UI行为**: 显示校准图标
- **详细触发条件**: 
  - `platform.contains("ios")`为true
  - 蓝牙已配对连接成功
  - 校准状态为以下之一:
    - `CalibrationStatus.FAILED`(通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`1`)
    - `CalibrationStatus.APP_NOT_FOREGROUND`(通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`3`)
    - `CalibrationStatus.BT_NOT_CONNECTED`(通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`2`)
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

### 15. iOS平台 - 蓝牙未连接
- **需求**: 显示蓝牙未连接图标
- **UI行为**: 显示蓝牙图标并提示连接蓝牙
- **详细触发条件**: 
  - `platform.contains("ios")`为true
  - `HidUtil.getConnectedBluetoothDevices()`返回空集合
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

### 16. Android平台 - 正常状态
- **需求**: 不显示任何图标
- **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **详细触发条件**: 
  - `platform.contains("android")`为true
  - 蓝牙已配对连接成功(`HidUtil.getConnectedBluetoothDevices()`不为空)
  - 通过`SdkViewModel.getInstance().updateBTPairedWithPhone()`确认蓝牙与Link设备匹配
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

### 17. Android平台 - 蓝牙未连接或断开
- **需求**: 显示蓝牙未连接/断开图标
- **UI行为**: 显示蓝牙图标并提示连接蓝牙
- **详细触发条件**: 
  - `platform.contains("android")`为true
  - `HidUtil.getConnectedBluetoothDevices()`返回空集合或通过`HidUtil.BluetoothStateListener.onDisconnected`检测到蓝牙断开
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

### 18. 鸿蒙平台 - 正常状态
- **需求**: 不显示任何图标
- **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **详细触发条件**: 
  - `!isIOSDevice() && !isAndroidDevice()`为true (鸿蒙系统)
  - 蓝牙已配对连接成功(`HidUtil.getConnectedBluetoothDevices()`不为空)
  - 遍历连接的蓝牙设备，存在设备名称与`blueToothName`匹配
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

### 19. 鸿蒙平台 - 蓝牙设备不匹配
- **需求**: 显示蓝牙设备不匹配图标
- **UI行为**: 显示蓝牙图标并提示可能导致反控异常
- **详细触发条件**: 
  - `!isIOSDevice() && !isAndroidDevice()`为true (鸿蒙系统)
  - 蓝牙已配对连接成功(`HidUtil.getConnectedBluetoothDevices()`不为空)
  - 遍历连接的蓝牙设备，没有设备名称与`blueToothName`匹配
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

### 20. 鸿蒙平台 - 蓝牙未连接或断开
- **需求**: 显示蓝牙未连接/断开图标
- **UI行为**: 显示蓝牙图标并提示连接蓝牙
- **详细触发条件**: 
  - `!isIOSDevice() && !isAndroidDevice()`为true (鸿蒙系统)
  - `HidUtil.getConnectedBluetoothDevices()`返回空集合或通过`HidUtil.BluetoothStateListener.onDisconnected`检测到蓝牙断开
  - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)

这些详细的触发条件涵盖了所有平台(iOS、Android、鸿蒙)下蓝牙图标和校准按钮的显示逻辑，确保用户界面能够准确反映当前设备的连接和校准状态。

---

_**User**_

请讲上述分析结果完整的写入到 docs 目录下，新建一个文件名字用这个功能名字。 

---

_**Assistant**_

我将创建一个文档文件，记录蓝牙图标和校准按钮的显示逻辑需求。

---

<details>
            <summary>Listed directory docs • **0** results</summary>
        
No results found
</details>

---

让我先创建docs目录，然后创建文档文件。

---

```bash
mkdir -p docs
```

---

_**User**_

项目根目录已经有 docs 目录了。

---

_**Assistant**_

我明白了，项目根目录已经有docs目录。我将直接在docs目录下创建文档文件。

---

```diff
- 
+ # 蓝牙图标/校准按钮显示逻辑需求
+ 
+ 本文档详细描述了车机端应用中蓝牙图标和校准按钮的显示逻辑，包括不同平台(iOS、Android、鸿蒙)下的各种状态和触发条件。
+ 
+ ## 基础条件
+ 
+ ### 1. 未互联状态
+ - **需求**: 如果未互联，不显示蓝牙图标
+ - **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
+ - **详细触发条件**: 
+   - `LinkSdkModel.INSTANCE.isConnected()`返回false
+   - 无论设备平台是iOS、Android还是鸿蒙，都不显示蓝牙图标
+ 
+ ### 2. 正常状态 (NORMAL)
+ - **需求**: 蓝牙正常连接且无异常时，不显示蓝牙图标
+ - **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
+ - **详细触发条件**: 
+   - **iOS平台**: 蓝牙已配对连接成功，且校准状态为`CalibrationStatus.SUCCESS`
+   - **Android平台**: 蓝牙已配对连接成功，且与Link设备匹配（通过`SdkViewModel.getInstance().updateBTPairedWithPhone()`确认）
+   - **鸿蒙平台**: 蓝牙已配对连接成功，且蓝牙设备名称与`blueToothName`匹配
+ 
+ ## 蓝牙连接问题
+ 
+ ### 3. 蓝牙未连接 (BT_NOT_CONNECTED)
+ - **需求**: 显示蓝牙未连接图标并提示用户
+ - **UI行为**: 
+   - 显示蓝牙图标: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
+   - 设置蓝牙图标: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
+   - 显示Toast提示: "请连接蓝牙"
+ - **详细触发条件**: 
+   - **所有平台**: `HidUtil.getConnectedBluetoothDevices()`返回空集合
+   - **iOS平台特例**: 如果校准状态为`CalibrationStatus.BT_NOT_CONNECTED`，也会触发此状态
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ### 4. 蓝牙断开 (BT_DISCONNECTED)
+ - **需求**: 显示蓝牙断开图标并提示用户
+ - **UI行为**: 
+   - 显示蓝牙图标: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
+   - 设置蓝牙图标: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
+   - 如果已互联，显示Toast提示: "蓝牙连接已断开"
+ - **详细触发条件**: 
+   - **所有平台**: 通过`HidUtil.BluetoothStateListener.onDisconnected`检测到蓝牙断开连接
+   - **iOS平台**: 蓝牙断开但互联仍然存在
+   - **Android平台**: 蓝牙断开但互联仍然存在
+   - **鸿蒙平台**: 蓝牙断开但互联仍然存在，通过`onDisconnected`回调触发
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ### 5. 蓝牙设备不匹配 (BT_DEVICE_MISMATCHED)
+ - **需求**: 显示蓝牙图标并提示用户连接到了错误的设备
+ - **UI行为**: 
+   - 显示蓝牙图标: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
+   - 设置蓝牙图标: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
+   - 显示Toast提示: "可能导致反控手机声音异常"
+ - **详细触发条件**: 
+   - **鸿蒙平台**: 蓝牙已连接，但设备名称与`blueToothName`不匹配
+   - 检测逻辑: 遍历`HidUtil.getConnectedBluetoothDevices()`返回的设备集合，检查设备名称是否与`blueToothName`匹配
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ## iOS校准相关
+ 
+ ### 6. iOS需要校准 (IOS_NEEDS_CALIBRATION)
+ - **需求**: 仅在iOS设备下显示校准图标
+ - **UI行为**: 
+   - 显示校准图标: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
+   - 设置校准图标: `mBlueToothOrCalibration.setImageResource(R.mipmap.calibration_icon)`
+ - **详细触发条件**: 
+   - **仅iOS平台**: `isIOSDevice()`返回true
+   - 校准状态为以下之一:
+     - `CalibrationStatus.FAILED`: 校准失败
+     - `CalibrationStatus.APP_NOT_FOREGROUND`: 手机APP未在前台
+     - `CalibrationStatus.BT_NOT_CONNECTED`: 蓝牙未连接
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ### 7. iOS校准失败
+ - **需求**: 显示校准失败界面并提供重新校准选项
+ - **UI行为**: 
+   - 显示校准图标
+   - 显示校准失败界面: `popCalibrationLin.setVisibility(View.VISIBLE)`
+   - 显示失败图标: `popCalibrationImg.setImageResource(R.mipmap.calibration_failed_img)`
+   - 显示失败标题: `popCalibrationTitle.setText("校准失败")`
+   - 提供5秒倒计时的重新校准按钮
+ - **详细触发条件**: 
+   - **仅iOS平台**: `isIOSDevice()`返回true
+   - 通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`1`(对应`CalibrationStatus.FAILED`)
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ### 8. iOS APP未在前台
+ - **需求**: 显示校准图标并提示用户打开手机APP
+ - **UI行为**: 
+   - 显示校准图标
+   - 显示Toast提示: "请打开手机上的WeLink应用"
+ - **详细触发条件**: 
+   - **仅iOS平台**: `isIOSDevice()`返回true
+   - 通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`3`(对应`CalibrationStatus.APP_NOT_FOREGROUND`)
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ## 按钮点击行为
+ 
+ ### 9. 点击蓝牙未连接图标
+ - **需求**: 提示用户连接蓝牙
+ - **UI行为**: 显示Toast提示: "请连接蓝牙"
+ - **详细触发条件**: 
+   - **所有平台**: 当前状态为`BT_NOT_CONNECTED`
+   - 用户点击`mBlueToothOrCalibration`按钮
+   - `lockViewVisible()`返回false (非锁屏或断流状态)
+ 
+ ### 10. 点击蓝牙断开图标
+ - **需求**: 提示用户蓝牙已断开
+ - **UI行为**: 显示Toast提示: "蓝牙连接已断开"
+ - **详细触发条件**: 
+   - **所有平台**: 当前状态为`BT_DISCONNECTED`
+   - 用户点击`mBlueToothOrCalibration`按钮
+   - `lockViewVisible()`返回false (非锁屏或断流状态)
+ 
+ ### 11. 点击蓝牙设备不匹配图标
+ - **需求**: 提示用户切换蓝牙
+ - **UI行为**: 显示Toast提示: "可能导致反控手机声音异常"
+ - **详细触发条件**: 
+   - **主要针对鸿蒙平台**: 当前状态为`BT_DEVICE_MISMATCHED`
+   - 用户点击`mBlueToothOrCalibration`按钮
+   - `lockViewVisible()`返回false (非锁屏或断流状态)
+ 
+ ### 12. 点击iOS校准图标
+ - **需求**: 隐藏按钮并开始校准
+ - **UI行为**: 
+   - 隐藏校准按钮: `mBlueToothOrCalibration.setVisibility(View.GONE)`
+   - 开始校准: `LinkSdkModel.INSTANCE.startVerification()`
+ - **详细触发条件**: 
+   - **仅iOS平台**: 当前状态为`IOS_NEEDS_CALIBRATION`
+   - 用户点击`mBlueToothOrCalibration`按钮
+   - `lockViewVisible()`返回false (非锁屏或断流状态)
+ 
+ ## 平台特定组合条件
+ 
+ ### 13. iOS平台 - 正常状态
+ - **需求**: 不显示任何图标
+ - **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
+ - **详细触发条件**: 
+   - `platform.contains("ios")`为true
+   - 蓝牙已配对连接成功(`HidUtil.getConnectedBluetoothDevices()`不为空)
+   - 校准状态为`CalibrationStatus.SUCCESS`(通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`0`)
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ### 14. iOS平台 - 需要校准
+ - **需求**: 显示校准图标
+ - **UI行为**: 显示校准图标
+ - **详细触发条件**: 
+   - `platform.contains("ios")`为true
+   - 蓝牙已配对连接成功
+   - 校准状态为以下之一:
+     - `CalibrationStatus.FAILED`(通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`1`)
+     - `CalibrationStatus.APP_NOT_FOREGROUND`(通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`3`)
+     - `CalibrationStatus.BT_NOT_CONNECTED`(通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback`收到校准结果为`2`)
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ### 15. iOS平台 - 蓝牙未连接
+ - **需求**: 显示蓝牙未连接图标
+ - **UI行为**: 显示蓝牙图标并提示连接蓝牙
+ - **详细触发条件**: 
+   - `platform.contains("ios")`为true
+   - `HidUtil.getConnectedBluetoothDevices()`返回空集合
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ### 16. Android平台 - 正常状态
+ - **需求**: 不显示任何图标
+ - **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
+ - **详细触发条件**: 
+   - `platform.contains("android")`为true
+   - 蓝牙已配对连接成功(`HidUtil.getConnectedBluetoothDevices()`不为空)
+   - 通过`SdkViewModel.getInstance().updateBTPairedWithPhone()`确认蓝牙与Link设备匹配
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ### 17. Android平台 - 蓝牙未连接或断开
+ - **需求**: 显示蓝牙未连接/断开图标
+ - **UI行为**: 显示蓝牙图标并提示连接蓝牙
+ - **详细触发条件**: 
+   - `platform.contains("android")`为true
+   - `HidUtil.getConnectedBluetoothDevices()`返回空集合或通过`HidUtil.BluetoothStateListener.onDisconnected`检测到蓝牙断开
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ### 18. 鸿蒙平台 - 正常状态
+ - **需求**: 不显示任何图标
+ - **UI行为**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
+ - **详细触发条件**: 
+   - `!isIOSDevice() && !isAndroidDevice()`为true (鸿蒙系统)
+   - 蓝牙已配对连接成功(`HidUtil.getConnectedBluetoothDevices()`不为空)
+   - 遍历连接的蓝牙设备，存在设备名称与`blueToothName`匹配
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ### 19. 鸿蒙平台 - 蓝牙设备不匹配
+ - **需求**: 显示蓝牙设备不匹配图标
+ - **UI行为**: 显示蓝牙图标并提示可能导致反控异常
+ - **详细触发条件**: 
+   - `!isIOSDevice() && !isAndroidDevice()`为true (鸿蒙系统)
+   - 蓝牙已配对连接成功(`HidUtil.getConnectedBluetoothDevices()`不为空)
+   - 遍历连接的蓝牙设备，没有设备名称与`blueToothName`匹配
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ### 20. 鸿蒙平台 - 蓝牙未连接或断开
+ - **需求**: 显示蓝牙未连接/断开图标
+ - **UI行为**: 显示蓝牙图标并提示连接蓝牙
+ - **详细触发条件**: 
+   - `!isIOSDevice() && !isAndroidDevice()`为true (鸿蒙系统)
+   - `HidUtil.getConnectedBluetoothDevices()`返回空集合或通过`HidUtil.BluetoothStateListener.onDisconnected`检测到蓝牙断开
+   - 前提条件: 已互联(`LinkSdkModel.INSTANCE.isConnected()`为true)
+ 
+ ## 其他特殊情况
+ 
+ ### 21. 锁屏或断流状态
+ - **需求**: 禁用蓝牙/校准按钮点击
+ - **UI行为**: 点击按钮无反应
+ - **详细触发条件**: `lockViewVisible()`返回true时
+ 
+ ### 22. 窗口大小变化
+ - **需求**: 通知iOS设备窗口大小变化以重新校准
+ - **UI行为**: `HidIosCalibrationUtil.updateViewSize(currentWidth, currentHeight)`
+ - **详细触发条件**: 窗口大小变化时
+ 
+ ## 实现类参考
+ 
+ 相关实现主要在`PopWinService.java`文件中，关键部分包括：
+ 
+ 1. `BluetoothAbnormalState`枚举：定义蓝牙异常状态
+ 2. `CalibrationStatus`枚举：定义iOS校准状态
+ 3. `BluetoothStateManager`内部类：管理蓝牙状态和UI更新
+ 4. `handleBluetoothButtonClick()`方法：处理蓝牙/校准按钮点击
+ 5. `checkAndUpdateBluetoothState()`方法：检查并更新蓝牙状态
+ 6. `onBluetoothConnectionChanged()`方法：处理蓝牙连接状态变化
+ 7. `onCalibrationResult()`方法：处理校准状态回调
+ 8. `CalibrationMonitoringCallback()`方法：设置校准监听回调 
```

---

我已经在docs目录下创建了一个新文件`bluetooth_calibration_icon_display_logic.md`，该文件详细记录了蓝牙图标和校准按钮的显示逻辑需求。

文档内容包括：
1. 基础条件（未互联状态和正常状态）
2. 蓝牙连接问题（未连接、断开、设备不匹配）
3. iOS校准相关（需要校准、校准失败、APP未在前台）
4. 按钮点击行为
5. 平台特定组合条件（iOS、Android、鸿蒙平台各自的状态处理）
6. 其他特殊情况（锁屏/断流状态、窗口大小变化）
7. 实现类参考

每个条件都详细说明了需求、UI行为和详细触发条件，特别区分了不同平台的处理逻辑。这份文档可以作为开发和测试的参考，确保蓝牙图标和校准按钮的显示逻辑符合预期。

---


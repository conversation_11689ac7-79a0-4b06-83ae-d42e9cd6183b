int color alert 0x0
int color background 0x0
int color bar1 0x0
int color bar2 0x0
int color bar3 0x0
int color bar4 0x0
int color button 0x0
int color cardBackground 0x0
int color clientBar 0x0
int color clientBarSecond 0x0
int color ic_launcher_background 0x0
int color onAlert 0x0
int color onBackground 0x0
int color onBackgroundSecond 0x0
int color onButton 0x0
int color onCardBackground 0x0
int color onCardBackgroundSecond 0x0
int color onCardDivider 0x0
int color translucent 0x0
int dimen largeFont 0x0
int dimen middleFont 0x0
int dimen round 0x0
int dimen round_30 0x0
int dimen smallFont 0x0
int dimen smallSmallFont 0x0
int drawable background_cron 0x0
int drawable background_cron_stroke 0x0
int drawable bars 0x0
int drawable btn_click_f 0x0
int drawable btn_resize_move_bg 0x0
int drawable caret_left 0x0
int drawable chevron_left 0x0
int drawable close_button_selector 0x0
int drawable compress 0x0
int drawable divider_line 0x0
int drawable ellipsis_vertical 0x0
int drawable equals 0x0
int drawable expand 0x0
int drawable ic_launcher_foreground 0x0
int drawable lightbulb 0x0
int drawable lightbulb_off 0x0
int drawable link_can_connect 0x0
int drawable link_can_not_connect 0x0
int drawable link_checking_connection 0x0
int drawable lock 0x0
int drawable mini_button_selector_1 0x0
int drawable minus 0x0
int drawable move_button_selector 0x0
int drawable not_equal 0x0
int drawable o 0x0
int drawable pair 0x0
int drawable phone 0x0
int drawable phones 0x0
int drawable plus 0x0
int drawable power_off 0x0
int drawable refresh 0x0
int drawable reset_location 0x0
int drawable rotate 0x0
int drawable share_in 0x0
int drawable share_out 0x0
int drawable square 0x0
int drawable unlock 0x0
int drawable wifi_can_connect 0x0
int drawable wifi_can_not_connect 0x0
int drawable wifi_checking_connection 0x0
int drawable window_bg 0x0
int drawable x 0x0
int id adb_key_pri 0x0
int id adb_key_pub 0x0
int id address 0x0
int id address_title 0x0
int id back_button 0x0
int id bar 0x0
int id bar_view 0x0
int id button_add 0x0
int id button_always_full_mode 0x0
int id button_auto 0x0
int id button_back 0x0
int id button_cancel 0x0
int id button_change 0x0
int id button_change_to_mini 0x0
int id button_change_to_small 0x0
int id button_close 0x0
int id button_confirm 0x0
int id button_create_shortcut 0x0
int id button_custom 0x0
int id button_delete 0x0
int id button_full 0x0
int id button_full_exit 0x0
int id button_get_uuid 0x0
int id button_go_to_set 0x0
int id button_home 0x0
int id button_layout 0x0
int id button_light 0x0
int id button_light_off 0x0
int id button_lock 0x0
int id button_mini 0x0
int id button_more 0x0
int id button_nav_bar 0x0
int id button_night_mode 0x0
int id button_no 0x0
int id button_pair 0x0
int id button_power 0x0
int id button_refresh 0x0
int id button_rotate 0x0
int id button_set 0x0
int id button_start_wireless 0x0
int id button_switch 0x0
int id button_transfer 0x0
int id button_yes 0x0
int id capture_event 0x0
int id captured_events 0x0
int id cert_name 0x0
int id cert_regenerate 0x0
int id change_to_mini_events 0x0
int id change_to_small_events 0x0
int id container_enable 0x0
int id container_latency 0x0
int id create_display 0x0
int id debug_port 0x0
int id default_full 0x0
int id device_expand 0x0
int id device_icon 0x0
int id device_name 0x0
int id devices_list 0x0
int id display_mirroring 0x0
int id edit_text 0x0
int id ip 0x0
int id ipv4 0x0
int id ipv6 0x0
int id is_audio 0x0
int id is_options 0x0
int id item_detail 0x0
int id item_spinner 0x0
int id item_switch 0x0
int id item_text 0x0
int id iv_close 0x0
int id iv_mini 0x0
int id iv_move 0x0
int id ll_title 0x0
int id log_device 0x0
int id log_text 0x0
int id my_view 0x0
int id name 0x0
int id nav_bar 0x0
int id night_mode_detector 0x0
int id ok 0x0
int id opening_port 0x0
int id options 0x0
int id pairing 0x0
int id pairing_code 0x0
int id pairing_port 0x0
int id panel 0x0
int id panel_landscape 0x0
int id permission_checker 0x0
int id re_size 0x0
int id reset_location 0x0
int id rl_context 0x0
int id run_open_port 0x0
int id run_pair 0x0
int id scan_address 0x0
int id scan_remote_app_list 0x0
int id scanned 0x0
int id scanning 0x0
int id set_about 0x0
int id set_default 0x0
int id set_display 0x0
int id set_other 0x0
int id specified_app 0x0
int id specified_app_title 0x0
int id spinner_latency 0x0
int id switch_enable 0x0
int id text 0x0
int id text_about 0x0
int id text_enable 0x0
int id text_latency 0x0
int id text_latency_detail 0x0
int id texture_view_layout 0x0
int id title 0x0
int id webview 0x0
int layout activity_adb_key 0x0
int layout activity_full 0x0
int layout activity_ip 0x0
int layout activity_log 0x0
int layout activity_main 0x0
int layout activity_monitor 0x0
int layout activity_pair 0x0
int layout activity_set 0x0
int layout activity_web_view 0x0
int layout item_add_device 0x0
int layout item_add_event 0x0
int layout item_devices_item 0x0
int layout item_devices_item_detail 0x0
int layout item_loading 0x0
int layout item_night_mode_changer 0x0
int layout item_reconnect 0x0
int layout item_request_permission 0x0
int layout item_set_device 0x0
int layout item_spinner 0x0
int layout item_spinner_item 0x0
int layout item_switch 0x0
int layout item_text 0x0
int layout item_text_detail 0x0
int layout module_dialog 0x0
int layout module_mini_view 0x0
int layout module_small_view 0x0
int mipmap close_0 0x0
int mipmap close_1 0x0
int mipmap ic_launcher_round 0x0
int mipmap mini_image_left 0x0
int mipmap mini_image_right 0x0
int mipmap move_icon 0x0
int mipmap move_icon1 0x0
int mipmap scale_icon 0x0
int mipmap scale_icon1 0x0
int raw easycontrol_server 0x0
int string adb_key_button 0x0
int string adb_key_button_code 0x0
int string adb_key_pri 0x0
int string adb_key_pub 0x0
int string add_device_address 0x0
int string add_device_address_hint 0x0
int string add_device_button 0x0
int string add_device_name 0x0
int string add_device_name_hint 0x0
int string add_device_option 0x0
int string add_device_scan 0x0
int string add_device_scan_address_finish_none 0x0
int string add_device_scan_finish 0x0
int string add_device_scan_specify_app_finish_error 0x0
int string add_device_scanning 0x0
int string add_device_specify_app 0x0
int string add_device_specify_app_hint 0x0
int string app_name 0x0
int string cancel 0x0
int string car_version_message 0x0
int string change_night_mode_failed 0x0
int string change_night_mode_success 0x0
int string confirm 0x0
int string error_address_error 0x0
int string error_app_not_found 0x0
int string error_connect_server 0x0
int string error_create_display 0x0
int string error_default_device_not_found 0x0
int string error_device_not_found 0x0
int string error_mode_not_support 0x0
int string error_no_browser 0x0
int string error_refused_back 0x0
int string error_stream_closed 0x0
int string error_transfer_app_failed 0x0
int string ip_copy 0x0
int string ip_ipv4 0x0
int string ip_ipv6 0x0
int string ip_scan_device_title 0x0
int string ip_scan_finish_copy 0x0
int string ip_scan_finish_none 0x0
int string ip_scanning_device 0x0
int string ip_title 0x0
int string loading_text 0x0
int string log_notify 0x0
int string log_other_devices 0x0
int string log_title 0x0
int string main_float_permission 0x0
int string main_float_permission_button 0x0
int string main_no_float_mode_button 0x0
int string main_poem 0x0
int string monitor_add_change_to_mini_event 0x0
int string monitor_add_change_to_small_event 0x0
int string monitor_add_event 0x0
int string monitor_android_version_not_support 0x0
int string monitor_capture_event 0x0
int string monitor_capture_event_start 0x0
int string monitor_capture_event_stop 0x0
int string monitor_change_to_mini_event 0x0
int string monitor_change_to_small_event 0x0
int string monitor_enable 0x0
int string monitor_latency 0x0
int string monitor_latency_detail 0x0
int string monitor_no_event 0x0
int string monitor_no_permission 0x0
int string monitor_permission_request 0x0
int string monitor_show_event 0x0
int string monitor_title 0x0
int string night_mode_auto 0x0
int string night_mode_current 0x0
int string night_mode_custom 0x0
int string night_mode_no 0x0
int string night_mode_yes 0x0
int string option_clipboard_sync 0x0
int string option_clipboard_sync_detail 0x0
int string option_default_full 0x0
int string option_default_full_detail 0x0
int string option_default_usb_device 0x0
int string option_default_usb_device_detail 0x0
int string option_is_audio 0x0
int string option_is_audio_detail 0x0
int string option_max_fps 0x0
int string option_max_fps_detail 0x0
int string option_max_size 0x0
int string option_max_size_detail 0x0
int string option_max_size_original 0x0
int string option_max_video_bit 0x0
int string option_max_video_bit_detail 0x0
int string option_night_mode_sync 0x0
int string option_night_mode_sync_detail 0x0
int string option_set_resolution 0x0
int string option_set_resolution_detail 0x0
int string option_startup_device 0x0
int string option_startup_device_detail 0x0
int string option_use_h265 0x0
int string option_use_h265_detail 0x0
int string option_use_opus 0x0
int string option_use_opus_detail 0x0
int string pair_cert 0x0
int string pair_cert_regenerate 0x0
int string pair_debug_port_hint 0x0
int string pair_failed 0x0
int string pair_generating_cert 0x0
int string pair_ip 0x0
int string pair_ip_hint 0x0
int string pair_no_cert 0x0
int string pair_open_port 0x0
int string pair_pair 0x0
int string pair_pairing_code_hint 0x0
int string pair_pairing_port_hint 0x0
int string pair_run 0x0
int string pair_success 0x0
int string set_about 0x0
int string set_about_how_to_use 0x0
int string set_about_ip 0x0
int string set_about_privacy 0x0
int string set_about_version 0x0
int string set_about_website 0x0
int string set_always_full_mode 0x0
int string set_always_full_mode_detail 0x0
int string set_app_monitor 0x0
int string set_app_monitor_detail 0x0
int string set_audio_channel 0x0
int string set_audio_channel_detail 0x0
int string set_auto_countdown 0x0
int string set_auto_countdown_detail 0x0
int string set_connect_usb 0x0
int string set_connect_usb_detail 0x0
int string set_create_startup_shortcut 0x0
int string set_default 0x0
int string set_device_button_change 0x0
int string set_device_button_create_shortcut 0x0
int string set_device_button_delete 0x0
int string set_device_button_get_uuid 0x0
int string set_device_button_get_uuid_success 0x0
int string set_device_button_night_mode 0x0
int string set_device_button_recover_error 0x0
int string set_device_button_start_wireless 0x0
int string set_device_button_start_wireless_success 0x0
int string set_device_title 0x0
int string set_display 0x0
int string set_display_auto_back_on_start_default 0x0
int string set_display_auto_back_on_start_default_detail 0x0
int string set_display_default_mini_on_outside 0x0
int string set_display_default_mini_on_outside_detail 0x0
int string set_display_default_show_nav_bar 0x0
int string set_display_default_show_nav_bar_detail 0x0
int string set_display_full_fill 0x0
int string set_display_full_fill_detail 0x0
int string set_display_full_to_mini_on_exit 0x0
int string set_display_full_to_mini_on_exit_detail 0x0
int string set_display_keep_screen_awake 0x0
int string set_display_keep_screen_awake_detail 0x0
int string set_display_mini_recover_on_timeout 0x0
int string set_display_mini_recover_on_timeout_detail 0x0
int string set_enable_usb 0x0
int string set_enable_usb_detail 0x0
int string set_force_desktop_mode 0x0
int string set_force_desktop_mode_detail 0x0
int string set_license 0x0
int string set_light_off_on_connect 0x0
int string set_light_off_on_connect_detail 0x0
int string set_light_off_on_connect_error 0x0
int string set_light_on_on_close 0x0
int string set_light_on_on_close_detail 0x0
int string set_light_on_on_close_error 0x0
int string set_lock_screen_on_close 0x0
int string set_lock_screen_on_close_detail 0x0
int string set_mirror_mode 0x0
int string set_mirror_mode_detail 0x0
int string set_no_auto_countdown 0x0
int string set_other 0x0
int string set_other_clear_key 0x0
int string set_other_clear_key_code 0x0
int string set_other_custom_key 0x0
int string set_other_locale 0x0
int string set_other_locale_code 0x0
int string set_other_log 0x0
int string set_reconnect 0x0
int string set_reconnect_detail 0x0
int string set_set_full_screen 0x0
int string set_set_full_screen_detail 0x0
int string set_try_start_default_in_app_transfer 0x0
int string set_try_start_default_in_app_transfer_detail 0x0
int string set_wake_up_screen_on_connect 0x0
int string set_wake_up_screen_on_connect_detail 0x0
int string start_application_transfer 0x0
int string start_display_mirroring 0x0
int string tip_application_transfer 0x0
int string tip_default_device 0x0
int string tip_default_usb 0x0
int string tip_reconnect 0x0
int style Transparent 0x0

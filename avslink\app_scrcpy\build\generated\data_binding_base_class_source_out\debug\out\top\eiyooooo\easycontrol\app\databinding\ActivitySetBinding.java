// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ActivitySetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView backButton;

  @NonNull
  public final LinearLayout setAbout;

  @NonNull
  public final LinearLayout setDefault;

  @NonNull
  public final LinearLayout setDisplay;

  @NonNull
  public final LinearLayout setOther;

  @NonNull
  public final TextView textAbout;

  private ActivitySetBinding(@NonNull LinearLayout rootView, @NonNull ImageView backButton,
      @NonNull LinearLayout setAbout, @NonNull LinearLayout setDefault,
      @NonNull LinearLayout setDisplay, @NonNull LinearLayout setOther,
      @NonNull TextView textAbout) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.setAbout = setAbout;
    this.setDefault = setDefault;
    this.setDisplay = setDisplay;
    this.setOther = setOther;
    this.textAbout = textAbout;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_set, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.back_button;
      ImageView backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.set_about;
      LinearLayout setAbout = ViewBindings.findChildViewById(rootView, id);
      if (setAbout == null) {
        break missingId;
      }

      id = R.id.set_default;
      LinearLayout setDefault = ViewBindings.findChildViewById(rootView, id);
      if (setDefault == null) {
        break missingId;
      }

      id = R.id.set_display;
      LinearLayout setDisplay = ViewBindings.findChildViewById(rootView, id);
      if (setDisplay == null) {
        break missingId;
      }

      id = R.id.set_other;
      LinearLayout setOther = ViewBindings.findChildViewById(rootView, id);
      if (setOther == null) {
        break missingId;
      }

      id = R.id.text_about;
      TextView textAbout = ViewBindings.findChildViewById(rootView, id);
      if (textAbout == null) {
        break missingId;
      }

      return new ActivitySetBinding((LinearLayout) rootView, backButton, setAbout, setDefault,
          setDisplay, setOther, textAbout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

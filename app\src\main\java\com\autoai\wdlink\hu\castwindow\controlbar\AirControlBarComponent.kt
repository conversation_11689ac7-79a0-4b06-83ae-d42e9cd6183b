package com.autoai.wdlink.hu.castwindow.controlbar

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout.LayoutParams
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LifecycleOwner
import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.ktx.dpToPx
import com.autoai.wdlink.hu.castwindow.lifecycle.FullLifecycleObserverAdapter
import com.autoai.wdlink.hu.castwindow.lifecycle.LifecycleOwnerFactory
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState
import com.autoai.wdlink.hu.castwindow.view.CastWindowStateManager
import com.autoai.wdlink.hu.databinding.FragmentAirControlDialogBinding
import com.autoai.wdlink.hu.sdk.LinkSdkModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * control bar View
 * <AUTHOR>
 * @date 2024/09/9
 */
class AirControlBarComponent(val viewBinding: FragmentAirControlDialogBinding) {
    companion object {
        private var platform = ""
        private const val TAG = "${ComponentName.WindowSwitcher}AirControlBarComponent"
        @JvmStatic
        fun create(context: Context,platform : String): AirControlBarComponent {
            this.platform = platform
            LinkLog.i(TAG, "create: context = $context")
            return AirControlBarComponent(FragmentAirControlDialogBinding.inflate(LayoutInflater.from(context), null, false))
        }
    }

    private val context = viewBinding.root.context

    private var showJob: Job? = null
    private var hideCallback: (()->Unit)? = null

    init {
        LinkLog.i(TAG, "init: this = $this")
        if(context !is LifecycleOwner){
            bindLifecycle(LifecycleOwnerFactory.generateLifecycleOwnerByView(viewBinding.root, true))
        }
        viewBinding.buttonMini.setOnClickListener {
            Toast.makeText(context, "最小化", Toast.LENGTH_SHORT).show()
            CastWindowStateManager.switchState(CastWindowState.MINI)
        }
        if (platform == "ios"){
            viewBinding.buttonFull.visibility = ViewGroup.VISIBLE
            viewBinding.buttonFull.setOnClickListener {
                /*  Toast.makeText(context, "全屏", Toast.LENGTH_SHORT).show()
                  CastWindowStateManager.switchFullState()*/
                //todo 手动发起开始校准
                LinkSdkModel.startVerification()
            }
        }
    }

    private fun bindLifecycle(lifecycleOwner: LifecycleOwner) {
        LinkLog.i(TAG, "bindLifecycle: lifecycleOwner = $lifecycleOwner")
        lifecycleOwner.lifecycle.addObserver(object: FullLifecycleObserverAdapter(){
            override fun onPause(owner: LifecycleOwner) {
                hide(false)
            }
        })
    }

    fun showIn(viewParent: ConstraintLayout, hideCallback: (()->Unit)? = null){
        LinkLog.i(TAG, "showIn: invoke")
        this.hideCallback = hideCallback
        if(isShow()){
            LinkLog.i(TAG, "showIn: isShow is true")
            resetShowSchedule()
            return
        }
        // 确保 viewBinding.root 没有父视图
        (viewBinding.root.parent as? ViewGroup)?.removeView(viewBinding.root)

        val layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
            .apply { gravity = Gravity.TOP or Gravity.START }

        viewParent.addView(viewBinding.root, layoutParams)
        viewBinding.root.run {
            alpha = 0f
            val edge = 45.dpToPx()
            translationX = 0f
            translationY = -edge
            animate().translationX(0f).translationY(edge).alpha(1f)
        }
        resetShowSchedule()
    }

    private fun hide(fadeout: Boolean = true){
        LinkLog.i(TAG, "hide: fadeout = $fadeout")
        showJob?.cancel()
        val remove = {
            kotlin.runCatching {
                (viewBinding.root.parent as? ViewGroup)?.removeView(viewBinding.root)
            }.onSuccess {
                hideCallback?.invoke()
            }
        }
        if(fadeout){
            val edge = 0.dpToPx()
            viewBinding.root.animate().translationX(-edge.toFloat()).translationY(-edge.toFloat()).alpha(0f).withEndAction { remove() }.start()
        } else {
            remove()
        }
    }



    private fun resetShowSchedule(){
        showJob?.cancel()
        showJob = CoroutineScope(Dispatchers.Main).launch {
            delay(3000)
            hide()
        }
    }

    private fun isShow(): Boolean{
        return showJob?.isActive ?: false
    }
}
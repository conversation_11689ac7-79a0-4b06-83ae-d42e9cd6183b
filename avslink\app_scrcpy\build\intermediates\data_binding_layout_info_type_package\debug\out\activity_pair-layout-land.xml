<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_pair" modulePackage="top.eiyooooo.easycontrol.app" filePath="avslink\app_scrcpy\src\main\res\layout-land\activity_pair.xml" directory="layout-land" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout-land/activity_pair_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="220" endOffset="14"/></Target><Target id="@+id/back_button" view="ImageView"><Expressions/><location startLine="9" startOffset="2" endLine="15" endOffset="39"/></Target><Target id="@+id/night_mode_detector" view="TextView"><Expressions/><location startLine="17" startOffset="2" endLine="25" endOffset="33"/></Target><Target id="@+id/webview" view="WebView"><Expressions/><location startLine="31" startOffset="4" endLine="43" endOffset="13"/></Target><Target id="@+id/panel_landscape" view="ScrollView"><Expressions/><location startLine="45" startOffset="4" endLine="216" endOffset="16"/></Target><Target id="@+id/ip" view="EditText"><Expressions/><location startLine="66" startOffset="8" endLine="76" endOffset="46"/></Target><Target id="@+id/pairing" view="TextView"><Expressions/><location startLine="78" startOffset="8" endLine="84" endOffset="46"/></Target><Target id="@+id/pairing_port" view="EditText"><Expressions/><location startLine="92" startOffset="10" endLine="103" endOffset="48"/></Target><Target id="@+id/pairing_code" view="EditText"><Expressions/><location startLine="105" startOffset="10" endLine="116" endOffset="48"/></Target><Target id="@+id/run_pair" view="Button"><Expressions/><location startLine="118" startOffset="10" endLine="129" endOffset="48"/></Target><Target id="@+id/opening_port" view="TextView"><Expressions/><location startLine="133" startOffset="8" endLine="139" endOffset="46"/></Target><Target id="@+id/debug_port" view="EditText"><Expressions/><location startLine="147" startOffset="10" endLine="158" endOffset="48"/></Target><Target id="@+id/run_open_port" view="Button"><Expressions/><location startLine="160" startOffset="10" endLine="171" endOffset="48"/></Target><Target id="@+id/cert_name" view="TextView"><Expressions/><location startLine="187" startOffset="10" endLine="196" endOffset="48"/></Target><Target id="@+id/cert_regenerate" view="Button"><Expressions/><location startLine="198" startOffset="10" endLine="210" endOffset="48"/></Target></Targets></Layout>
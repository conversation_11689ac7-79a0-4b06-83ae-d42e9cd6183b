<!-- Generated by SpecStory -->

# Notify PopWinService of CAR_BT_CONNECTED (2025-06-20 01:39Z)

_**User**_

c参考onScreenRotationChange， 将CAR_BT_CONNECTED 事件回调通知到 PopWinService 层。 

---

_**Assistant**_

I'll implement a callback mechanism to notify the PopWinService layer when CAR_BT_CONNECTED events occur, similar to how onScreenRotationChange is handled.

Let me examine the current code structure first.

---

<details>
            <summary>Searched codebase "CAR_BT_CONNECTED constant definition" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkSdk/src/main/java/com/autoai/welink/macro/WL_API_SDK_MSG_TYPE.java` | L32-36 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/Enum/WL_SDK_MSG_STATUS.java` | L1-36 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/macro/WL_API_APP_MSG_TYPE.java` | L1-28 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/Enum/WL_SDK_LAUNCHER_MSG.java` | L1-44 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/RequestBTMusicFocusUtil.kt` | L1-52 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/frame/utils/RequestBTMusicFocusUtil.kt` | L1-52 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L1-43 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/macro/WL_API_SDK_MSG_TYPE.java` | L1-32 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/param/HidConstants.java` | L1-79 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/jni/Enum/WL_JNI_CMD_TYPE.java` | L1-23 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/BtMusicFocusManager.kt` | L1-59 |
| `avslink/wlhardware/wlhardware/src/main/java/com/autoai/welink/wireless/ble/AccessPoint.java` | L236-264 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/PlatformAdaptor.java` | L143-188 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L580-612 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2272-2304 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L162-190 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/PlatformAdaptor.java` | L58-113 |
| `app/src/main/java/com/autoai/car/os_adapter/interfaces/CarInfoInterface.kt` | L1-83 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L77-128 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L612-639 |
| `avslink/app_scrcpy/build/intermediates/packaged_res/debug/values-en/values-en.xml` | L1-29 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/jni/Enum/WL_JNI_STATUS.java` | L1-11 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L618-649 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidBtStateReceiver.kt` | L33-53 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidBtStateReceiver.kt` | L226-265 |

</details>

---

Now, let's look at how the hidCallback interface is defined and how it's used for screen rotation changes:

---

<details>
            <summary>Searched codebase "HidCallback interface definition" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `app/src/main/java/com/autoai/wdlink/hu/sdk/device/PopHidCheckCallback.java` | L1-5 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/HidCallback.java` | L1-17 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/HidCheckCallback.java` | L1-12 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidApi.java` | L1-61 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenOutsideTouchDelegate.kt` | L1-38 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidUtil.java` | L129-193 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java` | L192-219 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidAoa.java` | L1-67 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidIosCalibrationUtil.java` | L1-59 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java` | L37-107 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanAndroid.java` | L25-82 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java` | L107-148 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L618-649 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/CalibrationCallBack.java` | L1-30 |
| `app/src/main/java/com/autoai/wdlink/hu/module/hidscreen/HidScreenTouchDelegateHelper.kt` | L1-45 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanIos.java` | L1-48 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java` | L219-247 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanIos.java` | L48-93 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1109-1142 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L963-997 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidCommonInterface.java` | L1-14 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidUtil.java` | L30-82 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidUtil.java` | L82-129 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidUtil.java` | L1-30 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L77-148 |

</details>

---

Now let's look at the PopWinService class to understand how we need to implement the callback:

---

<details>
            <summary>Searched codebase "PopWinService class" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinServiceUtils.kt` | L1-28 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinServiceUtils.kt` | L28-83 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L53-86 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinServiceUtils.kt` | L1-40 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L1-53 |
| `app/src/main/java/com/autoai/wdlink/hu/view/InitFragment.kt` | L62-68 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/view/InitFragment.kt` | L59-65 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L50-93 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L93-147 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L147-187 |
| `app/src/main/java/com/autoai/wdlink/hu/LinkService.kt` | L156-195 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L621-661 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L696-741 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L1-50 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2042-2070 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L1620-1656 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L1667-1685 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L1395-1422 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L1860-1884 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L518-537 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L559-597 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L1685-1706 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L978-999 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L1884-1923 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinServiceUtils.kt` | L83-101 |

</details>

---

Let's look at how the PopWinService class is structured and how it implements the HidCallback interface:

---

Read file: app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java

---

Now I can see that the PopWinService class already implements the HidCallback interface, which has the onScreenRotationChange method. I need to add a method to handle CAR_BT_CONNECTED events.

Let's look at the SdkViewModel class to see how we need to implement this:

---

Read file: app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt

---

Now let's look at how the SdkViewModel class handles CAR_BT_CONNECTED events:

---

<details>
            <summary>Searched codebase "CAR_BT_CONNECTED in SdkViewModel" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkSdk/src/main/java/com/autoai/welink/macro/WL_API_SDK_MSG_TYPE.java` | L32-36 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/Enum/WL_SDK_MSG_STATUS.java` | L1-36 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L580-612 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/RequestBTMusicFocusUtil.kt` | L1-52 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/frame/utils/RequestBTMusicFocusUtil.kt` | L1-52 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/PlatformAdaptor.java` | L58-113 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2272-2304 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/BtMusicFocusManager.kt` | L1-59 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L612-639 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L618-649 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkMsg.java` | L32-74 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L162-190 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L132-156 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L77-148 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/Enum/WL_SDK_LAUNCHER_MSG.java` | L1-44 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2477-2495 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/macro/WL_API_APP_MSG_TYPE.java` | L1-28 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/macro/WL_API_SDK_MSG_TYPE.java` | L1-32 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L190-230 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L26-69 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2526-2539 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L173-208 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidBtStateReceiver.kt` | L226-265 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkJson.java` | L756-809 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java` | L284-318 |

</details>

---

Now I have a good understanding of how the system works. I need to:

1. Create a new interface method in the HidCallback interface to handle CAR_BT_CONNECTED events
2. Implement this method in the SdkViewModel class to notify listeners
3. Implement the method in the PopWinService class

Let's start by checking if the HidCallback interface already has a method for this:

---

Read file: avslink/linkHost/src/main/java/com/autoai/welinkapp/model/HidCallback.java

---

Now I'll look at how the SdkViewModel handles the CAR_BT_CONNECTED event:

---

Read file: avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java

---

Now I need to check if the SdkViewModel already handles the CAR_BT_CONNECTED event:

---

<details>
            <summary>Grep search for "CAR_BT_CONNECTED" • **0** files</summary>
        
No results found
</details>

---

Based on my analysis, I need to:

1. Add a new method to the HidCallback interface to handle CAR_BT_CONNECTED events
2. Modify the SdkViewModel class to call this method when it receives CAR_BT_CONNECTED events
3. Implement the method in PopWinService class

Let's make these changes:

1. First, let's update the HidCallback interface:

---

```diff
       */
      void onScreenRotationChange(int width, int height, int angle, int mode,String platform);
+     
+     /**
+      * 当车机蓝牙连接状态变化时调用
+      *
+      * @param connected 是否已连接
+      */
+     void onCarBtConnected(boolean connected);
  }
  
```

---

2. Now, let's modify the SdkViewModel class to handle CAR_BT_CONNECTED events and call the new callback method:

---

```diff
                  dispatchHidMessage(obj);
                  break;
+             case WL_API_SDK_MSG_TYPE.CAR_BT_CONNECTED:
+                 boolean connected = (int) object == 1;
+                 LogUtil.i(TAG, "onRecvMsg: CAR_BT_CONNECTED = " + connected);
+                 if (hidCallback != null) {
+                     hidCallback.onCarBtConnected(connected);
+                 }
+                 break;
              default:
                  break;
```

---

3. Finally, let's implement the onCarBtConnected method in the PopWinService class:

---

```diff
          }
      };
+ 
+     /**
+      * 处理车机蓝牙连接状态变化
+      * @param connected 是否已连接
+      */
+     @MainThread
+     @Override
+     public void onCarBtConnected(boolean connected) {
+         LogUtil.d(TAG, "onCarBtConnected: connected = " + connected);
+         if (connected) {
+             // 车机蓝牙已连接，隐藏蓝牙警告图标
+             if (mBlueToothOrCalibration != null) {
+                 mBlueToothOrCalibration.setVisibility(View.GONE);
+             }
+             // 重置蓝牙异常状态
+             bluetoothAbnormalState = BluetoothAbnormalState.NORMAL;
+         } else {
+             // 车机蓝牙未连接，显示蓝牙警告图标
+             if (mBlueToothOrCalibration != null && LinkSdkModel.INSTANCE.isConnected()) {
+                 mBlueToothOrCalibration.setVisibility(View.VISIBLE);
+                 mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2);
+                 bluetoothAbnormalState = BluetoothAbnormalState.BT_NOT_CONNECTED;
+                 ToastUtil.showToast(Utils.getApp().getString(R.string.bt_not_connected_tips), 
+                     Utils.getApp().getString(R.string.please_connect_bluetooth), 
+                     Utils.getApp().getString(R.string.connect_bluetooth), "", true, false, 1);
+             }
+         }
+     }
  }
  
```

---

The implementation looks good, but I need to make sure it's placed in the right location in the file. Let's fix the placement:

---



---

Let's check the current state of the PopWinService.java file around the onScreenRotationChange method:

---

Read file: app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java

---

I see that the onCarBtConnected method is already in the file, but it's at the end. Let's move it to be after onScreenRotationChange:

---

```diff
          updateFirstView();
      }
+ 
+     /**
+      * 处理车机蓝牙连接状态变化
+      * @param connected 是否已连接
+      */
+     @MainThread
+     @Override
+     public void onCarBtConnected(boolean connected) {
+         LogUtil.d(TAG, "onCarBtConnected: connected = " + connected);
+         if (connected) {
+             // 车机蓝牙已连接，隐藏蓝牙警告图标
+             if (mBlueToothOrCalibration != null) {
+                 mBlueToothOrCalibration.setVisibility(View.GONE);
+             }
+             // 重置蓝牙异常状态
+             bluetoothAbnormalState = BluetoothAbnormalState.NORMAL;
+         } else {
+             // 车机蓝牙未连接，显示蓝牙警告图标
+             if (mBlueToothOrCalibration != null && LinkSdkModel.INSTANCE.isConnected()) {
+                 mBlueToothOrCalibration.setVisibility(View.VISIBLE);
+                 mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2);
+                 bluetoothAbnormalState = BluetoothAbnormalState.BT_NOT_CONNECTED;
+                 ToastUtil.showToast(Utils.getApp().getString(R.string.bt_not_connected_tips), 
+                     Utils.getApp().getString(R.string.please_connect_bluetooth), 
+                     Utils.getApp().getString(R.string.connect_bluetooth), "", true, false, 1);
+             }
+         }
+     }
  
      private float mScale;
```

```diff
          }
      };
- 
-     /**
-      * 处理车机蓝牙连接状态变化
-      * @param connected 是否已连接
-      */
-     @MainThread
-     @Override
-     public void onCarBtConnected(boolean connected) {
-         LogUtil.d(TAG, "onCarBtConnected: connected = " + connected);
-         if (connected) {
-             // 车机蓝牙已连接，隐藏蓝牙警告图标
-             if (mBlueToothOrCalibration != null) {
-                 mBlueToothOrCalibration.setVisibility(View.GONE);
-             }
-             // 重置蓝牙异常状态
-             bluetoothAbnormalState = BluetoothAbnormalState.NORMAL;
-         } else {
-             // 车机蓝牙未连接，显示蓝牙警告图标
-             if (mBlueToothOrCalibration != null && LinkSdkModel.INSTANCE.isConnected()) {
-                 mBlueToothOrCalibration.setVisibility(View.VISIBLE);
-                 mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2);
-                 bluetoothAbnormalState = BluetoothAbnormalState.BT_NOT_CONNECTED;
-                 ToastUtil.showToast(Utils.getApp().getString(R.string.bt_not_connected_tips), 
-                     Utils.getApp().getString(R.string.please_connect_bluetooth), 
-                     Utils.getApp().getString(R.string.connect_bluetooth), "", true, false, 1);
-             }
-         }
-     }
  }
  
```

---

Now let's update the notifyScreenState method in PopWinService to use the new callback method instead of directly handling the car_bt_connected message:

---

```diff
                  break;
              default:
-                 // 处理car_bt_connected状态更新
+                 // 处理车机蓝牙连接状态消息
                  if (action.startsWith("car_bt_connected:")) {
-                     notifyScreenState(action);
+                     String connectedState = action.substring("car_bt_connected:".length());
+                     LogUtil.d(TAG, "notifyScreenState: car_bt_connected state = " + connectedState);
+                     onCarBtConnected("1".equals(connectedState));
                  }
                  break;
```

```diff
                          String connectedState = action.substring("car_bt_connected:".length());
                          LogUtil.d(TAG, "notifyScreenState: car_bt_connected state = " + connectedState);
-                         if ("1".equals(connectedState)) {
-                             // 车机蓝牙已连接，隐藏蓝牙警告图标
-                             if (mBlueToothOrCalibration != null) {
-                                 mBlueToothOrCalibration.setVisibility(View.GONE);
-                             }
-                             // 重置蓝牙异常状态
-                             bluetoothAbnormalState = BluetoothAbnormalState.NORMAL;
-                         } else {
-                             // 车机蓝牙未连接，显示蓝牙警告图标
-                             if (mBlueToothOrCalibration != null && LinkSdkModel.INSTANCE.isConnected()) {
-                                 mBlueToothOrCalibration.setVisibility(View.VISIBLE);
-                                 mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2);
-                                 bluetoothAbnormalState = BluetoothAbnormalState.BT_NOT_CONNECTED;
-                                 ToastUtil.showToast(Utils.getApp().getString(R.string.bt_not_connected_tips), 
-                                     Utils.getApp().getString(R.string.please_connect_bluetooth), 
-                                     Utils.getApp().getString(R.string.connect_bluetooth), "", true, false, 1);
-                             }
-                         }
+                         onCarBtConnected("1".equals(connectedState));
                      }
                      break;
```

---

Great! Now let's make sure the SdkViewModel is properly registering the HidCallback for CAR_BT_CONNECTED events. Let's check how it's currently registering the callback for screen rotation changes:

---

<details>
            <summary>Grep search for "setHidRegionCallback" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L996 | `public void setHidRegionCallback(HidCallback hidCallback) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L996 | `public void setHidRegionCallback(HidCallback hidCallback) {` |

</details>

---

Let's check how the callback is registered in LinkSdkModel:

---

<details>
            <summary>Grep search for "setHidRegionCallback" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L197 | `fun setHidRegionCallback(hidCallback: HidCallback) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L198 | `LinkHost.setHidRegionCallback(hidCallback)` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L480 | `fun setHidRegionCallback(hidCallback: HidCallback) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L481 | `LinkHost.setHidRegionCallback(hidCallback)` |

</details>

---

Now let's check where this method is called in PopWinService:

---

<details>
            <summary>Grep search for "setHidRegionCallback" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L1423 | `LinkSdkModel.INSTANCE.setHidRegionCallback((width, height, angle, mode, platform) -> {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/testapp/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L379 | `TouchUtil.getInstance().setHidRegionCallback((width, height, angle, mode) -> {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L1425 | `LinkSdkModel.INSTANCE.setHidRegionCallback((width, height, angle, mode, platform) -> {` |

</details>

---

Let's check the implementation of setHidRegionCallback in the PopWinService class:

---

Read file: app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java

---

I can see that the PopWinService class is already registering itself as a HidCallback through the LinkSdkModel.setHidRegionCallback method. This will ensure that both onScreenRotationChange and onCarBtConnected methods will be called when the respective events occur.

Let's summarize what we've done:

1. Added a new method `onCarBtConnected(boolean connected)` to the HidCallback interface to handle car Bluetooth connection status changes.

2. Modified the SdkViewModel class to call this method when it receives a CAR_BT_CONNECTED event from the SDK.

3. Implemented the onCarBtConnected method in the PopWinService class to update the UI based on Bluetooth connection status.

4. Updated the notifyScreenState method in PopWinService to use the new onCarBtConnected method directly instead of duplicating the logic.

These changes ensure that when a CAR_BT_CONNECTED event is received, the PopWinService will be notified through the HidCallback interface, similar to how screen rotation changes are handled.

---


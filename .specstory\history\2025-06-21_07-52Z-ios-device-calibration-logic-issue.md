<!-- Generated by SpecStory -->

# iOS device calibration logic issue (2025-06-21 07:52Z)

_**User**_

@MainActivity.kt @bluetooth_calibration_icon_display_logic.md  现在这个项目存在一个问题，就是 iOS 设备连接成功后，这个应用就自动开始进入校准状态。需求是 互联成功后检查蓝牙是否连接，如果没有连接，则直接进入投屏。 如果连接匹配了，则进入校准流程。  请检查一下代码，然后再分析一下问题产生原因，给出解决方案，等待确认。

---

_**Assistant**_

我会分析iOS设备连接后自动进入校准状态的问题，并提供解决方案。

## 问题分析

检查MainActivity.kt和蓝牙校准逻辑文档后，我发现以下问题：

1. 在MainActivity.kt中，当设备类型为iOS WiFi时，代码总是检查蓝牙设备是否连接，但没有检查蓝牙是否与当前连接的手机匹配：

```kotlin
CommonData.DEVICE_TYPE_WIFI_IOS-> {
    type = "ios"
    androidwifi = "ios_wifi"
    val deviceSet = HidUtil.getConnectedBluetoothDevices();
    Logger.d("MiandeviceSet  = ${deviceSet.size}")
    if (deviceSet.isEmpty()){
        iosWifiLin2("ios")
    }else{
        iosCalibrationLin()
    }
}
```

2. 根据需求文档，正确的逻辑应该是：
   - 互联成功后检查蓝牙是否连接
   - 如果没有连接，直接进入投屏
   - 如果连接了且匹配当前互联设备，才进入校准流程

3. 但当前代码只检查了蓝牙是否连接，没有检查蓝牙是否与当前互联设备匹配，导致任何蓝牙连接都会触发校准流程。

## 解决方案

需要修改MainActivity.kt中的代码，增加蓝牙匹配检查逻辑：

```kotlin
CommonData.DEVICE_TYPE_WIFI_IOS-> {
    type = "ios"
    androidwifi = "ios_wifi"
    val deviceSet = HidUtil.getConnectedBluetoothDevices()
    Logger.d("MiandeviceSet  = ${deviceSet.size}")
    
    // 检查是否有蓝牙连接
    if (deviceSet.isEmpty()) {
        // 没有蓝牙连接，直接进入投屏
        iosWifiLin2("ios")
    } else {
        // 有蓝牙连接，检查是否与当前互联设备匹配
        val isBluetoothMatched = SdkViewModel.getInstance().isBTPairedWithPhone()
        if (isBluetoothMatched) {
            // 蓝牙匹配当前互联设备，进入校准流程
            iosCalibrationLin()
        } else {
            // 蓝牙不匹配当前互联设备，直接进入投屏
            iosWifiLin2("ios")
        }
    }
}
```

这个解决方案需要使用`SdkViewModel.getInstance().isBTPairedWithPhone()`方法来检查蓝牙是否与当前互联设备匹配。如果该方法不存在，可能需要创建一个类似于`updateBTPairedWithPhone()`的方法来获取蓝牙匹配状态。

根据文档中的描述：
> updateBTPairedWithPhone 是在互联完成后，手机侧发送过来回调的函数， 如果是 true，则iOS 和安卓设备蓝牙就是与互联设备匹配的，并且以这个通知为准。

这表明应该有一个回调或状态可以检查蓝牙是否与当前互联设备匹配。

## 实现建议

1. 首先检查`SdkViewModel`类中是否有获取蓝牙匹配状态的方法，如果没有，需要添加一个。

2. 修改`MainActivity.kt`中的iOS设备连接逻辑，增加蓝牙匹配检查。

3. 确保在蓝牙状态变化时重新评估是否需要校准。

请确认这个解决方案是否符合需求，以及`SdkViewModel`中是否有获取蓝牙匹配状态的方法。如果没有，我可以提供更详细的实现方案。

---


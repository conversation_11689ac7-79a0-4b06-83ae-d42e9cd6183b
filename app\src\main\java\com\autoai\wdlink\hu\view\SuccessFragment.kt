package com.autoai.wdlink.hu.view

import com.autoai.wdlink.hu.databinding.FragmentSuccessBinding
import com.autoai.wdlink.hu.frame.BaseFragment
import com.autoai.wdlink.hu.viewmodel.SuccessViewModel

/**
 * 连接成功
 * */
class SuccessFragment :
    BaseFragment<SuccessViewModel, FragmentSuccessBinding>(FragmentSuccessBinding::inflate) {

    override fun getViewModelClass() = SuccessViewModel::class

    override fun getViewBindingClass() = FragmentSuccessBinding::class
}
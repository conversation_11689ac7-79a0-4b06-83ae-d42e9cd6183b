<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_request_permission" modulePackage="top.eiyooooo.easycontrol.app" filePath="avslink\app_scrcpy\src\main\res\layout\item_request_permission.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_request_permission_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="64" endOffset="14"/></Target><Target id="@+id/text" view="TextView"><Expressions/><location startLine="15" startOffset="2" endLine="24" endOffset="30"/></Target><Target id="@+id/button_layout" view="GridLayout"><Expressions/><location startLine="26" startOffset="2" endLine="62" endOffset="14"/></Target><Target id="@+id/button_go_to_set" view="Button"><Expressions/><location startLine="36" startOffset="4" endLine="46" endOffset="43"/></Target><Target id="@+id/button_always_full_mode" view="Button"><Expressions/><location startLine="50" startOffset="4" endLine="60" endOffset="43"/></Target></Targets></Layout>
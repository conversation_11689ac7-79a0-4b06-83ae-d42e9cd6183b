// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.GridLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ItemRequestPermissionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button buttonAlwaysFullMode;

  @NonNull
  public final Button buttonGoToSet;

  @NonNull
  public final GridLayout buttonLayout;

  @NonNull
  public final TextView text;

  private ItemRequestPermissionBinding(@NonNull LinearLayout rootView,
      @NonNull Button buttonAlwaysFullMode, @NonNull Button buttonGoToSet,
      @NonNull GridLayout buttonLayout, @NonNull TextView text) {
    this.rootView = rootView;
    this.buttonAlwaysFullMode = buttonAlwaysFullMode;
    this.buttonGoToSet = buttonGoToSet;
    this.buttonLayout = buttonLayout;
    this.text = text;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRequestPermissionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRequestPermissionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_request_permission, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRequestPermissionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_always_full_mode;
      Button buttonAlwaysFullMode = ViewBindings.findChildViewById(rootView, id);
      if (buttonAlwaysFullMode == null) {
        break missingId;
      }

      id = R.id.button_go_to_set;
      Button buttonGoToSet = ViewBindings.findChildViewById(rootView, id);
      if (buttonGoToSet == null) {
        break missingId;
      }

      id = R.id.button_layout;
      GridLayout buttonLayout = ViewBindings.findChildViewById(rootView, id);
      if (buttonLayout == null) {
        break missingId;
      }

      id = R.id.text;
      TextView text = ViewBindings.findChildViewById(rootView, id);
      if (text == null) {
        break missingId;
      }

      return new ItemRequestPermissionBinding((LinearLayout) rootView, buttonAlwaysFullMode,
          buttonGoToSet, buttonLayout, text);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

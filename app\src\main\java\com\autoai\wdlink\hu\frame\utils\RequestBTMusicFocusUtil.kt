package com.autoai.wdlink.hu.frame.utils

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothProfile
import android.content.Context
import android.content.Intent
import com.autoai.wdlink.hu.Logger

/**
 * <AUTHOR> zhanggc
 * @description : java类作用描述  德赛蓝牙
 * @date : 2024/4/12 15:00
 * @version : 1.0
 */
object RequestBTMusicFocusUtil {
    private const val TAG = "RequestBTMusicFocusUtil"
    const val DESAYSV_ACTION_START_SOURCE = "com.desaysv.mediaapp.action.startSource"
    const val KEY_START_SOURCE = "key_start_source"
    const val KEY_IS_FOREGROUND = "key_is_foreground"
    const val KEY_IS_REQUEST_FOCUS = "key_is_request_focus"
    const val KEY_OPEN_REASON = "key_open_reason"
    const val MUSIC_APP_PACKAGE = "com.desaysv.audioapp";
    const val BT_MUSIC_SOURCE = "bt_music"
    const val REASON_VALUE = "we_link"

    //车机与手机是否连接成功
    var isConnectPhone: Boolean = false
    //蓝牙状态变化广播
    var ACTION_CONNECTION_STATE_CHANGED = "android.bluetooth.a2dp-sink.profile.action.CONNECTION_STATE_CHANGED"

    /**
     * 德赛请求蓝牙焦点广播
     */
    fun requestBTMusicFocus(context: Context?) {
        Logger.d("requestBTMusicFocus--------context:$context,isConnectPhone:$isConnectPhone")
        if (context == null || !isConnectPhone) return
        val intent = Intent(DESAYSV_ACTION_START_SOURCE)
        intent.putExtra(KEY_START_SOURCE, BT_MUSIC_SOURCE)
        intent.putExtra(KEY_IS_FOREGROUND, false)
        intent.putExtra(KEY_IS_REQUEST_FOCUS, true)
        intent.putExtra(KEY_OPEN_REASON, REASON_VALUE)
        intent.setPackage(MUSIC_APP_PACKAGE)
        context.startService(intent)
    }

    /**
     * 判断蓝牙是否连接
     */
    @SuppressLint("MissingPermission")
    fun isBluetoothConnecte(context: Context?): Boolean {
        // 获取蓝牙适配器
        var bluetoothAdapter: BluetoothAdapter? = null
        val bluetoothManager =
            context!!.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        if (bluetoothManager != null) {
            bluetoothAdapter = bluetoothManager.adapter
        }
        if (bluetoothAdapter != null && bluetoothAdapter!!.isEnabled) {
//            var headset = bluetoothAdapter?.getProfileConnectionState( getProperty("HEADSET_CLIENT",16))
            var a2dp = bluetoothAdapter?.getProfileConnectionState(getProperty("A2DP_SINK", 11))
            Logger.d("bluetoothAdapter a2dp:$a2dp")
            return a2dp == BluetoothProfile.STATE_CONNECTED
        } else {
            Logger.d("bluetoothAdapter:$bluetoothAdapter,或 isEnabled 为false")
        }
        return false
    }

    fun getProperty(key: String, defaultValue: Int): Int {
        //获取属性
        var value = defaultValue
        try {
            //获取对应的属性类 SystemProperties
            val clazz = Class.forName("android.bluetooth.BluetoothProfile");
            //通过属性名获取Field对象
            var field = clazz.getDeclaredField(key)
            // 如果属性是私有的，则需要调用此方法
            field.isAccessible = true
            value = field.get(null) as Int
            Logger.d("bluetoothState    value:$value")
        } catch (e: Exception) {
            e.printStackTrace();
        } finally {
            return value;
        }
    }
    fun getReceiveProperty(key: String): String {
        //获取属性
        try {
            //获取对应的属性类 SystemProperties
            val clazz = Class.forName("android.bluetooth.BluetoothA2dpSink");
            //通过属性名获取Field对象
            var field = clazz.getDeclaredField(key)
            // 如果属性是私有的，则需要调用此方法
            field.isAccessible = true
            ACTION_CONNECTION_STATE_CHANGED = field.get(null) as String
            Logger.d("bluetoothState    value:$ACTION_CONNECTION_STATE_CHANGED")
        } catch (e: Exception) {
            e.printStackTrace();
        } finally {
            return ACTION_CONNECTION_STATE_CHANGED;
        }
    }
}
<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:background="@drawable/background_cron"
  android:gravity="center"
  android:orientation="horizontal"
  android:paddingStart="12dp"
  android:paddingTop="10dp"
  android:paddingEnd="12dp"
  android:paddingBottom="10dp">

  <ImageView
    android:id="@+id/device_icon"
    android:layout_width="26dp"
    android:layout_height="26dp"
    android:src="@drawable/wifi_checking_connection"
    android:tint="@color/onCardBackground" />

  <TextView
    android:id="@+id/device_name"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:layout_marginStart="12dp"
    android:layout_marginEnd="10dp"
    android:layout_weight="1"
    android:includeFontPadding="false"
    android:maxLines="1"
    android:text="设备1"
    android:textColor="@color/onCardBackground"
    android:textSize="@dimen/largeFont"
    tools:ignore="HardcodedText" />

  <ImageView
    android:id="@+id/device_expand"
    android:layout_width="24dp"
    android:layout_height="24dp"
    android:rotation="180"
    android:src="@drawable/chevron_left"
    android:tint="@color/onCardBackground" />

</LinearLayout>

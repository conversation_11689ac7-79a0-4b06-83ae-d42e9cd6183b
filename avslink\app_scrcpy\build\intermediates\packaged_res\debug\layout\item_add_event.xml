<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:layout_gravity="center_horizontal" >

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="15dp"
    android:background="@drawable/background_cron"
    android:elevation="8dp"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingStart="15dp"
    android:paddingEnd="15dp"
    android:paddingTop="10dp"
    android:paddingBottom="10dp">

    <TextView
      android:id="@+id/title"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="center"
      android:gravity="center"
      android:text="@string/monitor_add_event"
      android:textColor="@color/onCardBackground"
      android:textSize="@dimen/middleFont"
      android:textStyle="bold" />

    <GridLayout
      android:id="@+id/button_layout"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginBottom="12dp"
      android:columnCount="3"
      android:paddingStart="12dp"
      android:paddingEnd="12dp"
      android:paddingTop="12dp">

      <Button
        android:id="@+id/button_change_to_mini"
        android:layout_columnWeight="9"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_cron"
        android:backgroundTint="@color/button"
        android:gravity="center"
        android:text="@string/monitor_add_change_to_mini_event"
        android:textColor="@color/onButton"
        android:textSize="@dimen/smallFont" />

      <Space android:layout_columnWeight="1" />

      <Button
        android:id="@+id/button_change_to_small"
        android:layout_columnWeight="9"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_cron"
        android:backgroundTint="@color/button"
        android:gravity="center"
        android:text="@string/monitor_add_change_to_small_event"
        android:textColor="@color/onButton"
        android:textSize="@dimen/smallFont" />

    </GridLayout>

  </LinearLayout>

</FrameLayout>
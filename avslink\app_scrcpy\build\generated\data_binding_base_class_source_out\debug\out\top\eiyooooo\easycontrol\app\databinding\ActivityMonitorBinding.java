// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ActivityMonitorBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView backButton;

  @NonNull
  public final TextView captureEvent;

  @NonNull
  public final LinearLayout capturedEvents;

  @NonNull
  public final LinearLayout changeToMiniEvents;

  @NonNull
  public final LinearLayout changeToSmallEvents;

  @NonNull
  public final LinearLayout containerEnable;

  @NonNull
  public final LinearLayout containerLatency;

  @NonNull
  public final TextView permissionChecker;

  @NonNull
  public final Spinner spinnerLatency;

  @NonNull
  public final Switch switchEnable;

  @NonNull
  public final TextView textEnable;

  @NonNull
  public final TextView textLatency;

  @NonNull
  public final TextView textLatencyDetail;

  private ActivityMonitorBinding(@NonNull LinearLayout rootView, @NonNull ImageView backButton,
      @NonNull TextView captureEvent, @NonNull LinearLayout capturedEvents,
      @NonNull LinearLayout changeToMiniEvents, @NonNull LinearLayout changeToSmallEvents,
      @NonNull LinearLayout containerEnable, @NonNull LinearLayout containerLatency,
      @NonNull TextView permissionChecker, @NonNull Spinner spinnerLatency,
      @NonNull Switch switchEnable, @NonNull TextView textEnable, @NonNull TextView textLatency,
      @NonNull TextView textLatencyDetail) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.captureEvent = captureEvent;
    this.capturedEvents = capturedEvents;
    this.changeToMiniEvents = changeToMiniEvents;
    this.changeToSmallEvents = changeToSmallEvents;
    this.containerEnable = containerEnable;
    this.containerLatency = containerLatency;
    this.permissionChecker = permissionChecker;
    this.spinnerLatency = spinnerLatency;
    this.switchEnable = switchEnable;
    this.textEnable = textEnable;
    this.textLatency = textLatency;
    this.textLatencyDetail = textLatencyDetail;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMonitorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMonitorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_monitor, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMonitorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.back_button;
      ImageView backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.capture_event;
      TextView captureEvent = ViewBindings.findChildViewById(rootView, id);
      if (captureEvent == null) {
        break missingId;
      }

      id = R.id.captured_events;
      LinearLayout capturedEvents = ViewBindings.findChildViewById(rootView, id);
      if (capturedEvents == null) {
        break missingId;
      }

      id = R.id.change_to_mini_events;
      LinearLayout changeToMiniEvents = ViewBindings.findChildViewById(rootView, id);
      if (changeToMiniEvents == null) {
        break missingId;
      }

      id = R.id.change_to_small_events;
      LinearLayout changeToSmallEvents = ViewBindings.findChildViewById(rootView, id);
      if (changeToSmallEvents == null) {
        break missingId;
      }

      id = R.id.container_enable;
      LinearLayout containerEnable = ViewBindings.findChildViewById(rootView, id);
      if (containerEnable == null) {
        break missingId;
      }

      id = R.id.container_latency;
      LinearLayout containerLatency = ViewBindings.findChildViewById(rootView, id);
      if (containerLatency == null) {
        break missingId;
      }

      id = R.id.permission_checker;
      TextView permissionChecker = ViewBindings.findChildViewById(rootView, id);
      if (permissionChecker == null) {
        break missingId;
      }

      id = R.id.spinner_latency;
      Spinner spinnerLatency = ViewBindings.findChildViewById(rootView, id);
      if (spinnerLatency == null) {
        break missingId;
      }

      id = R.id.switch_enable;
      Switch switchEnable = ViewBindings.findChildViewById(rootView, id);
      if (switchEnable == null) {
        break missingId;
      }

      id = R.id.text_enable;
      TextView textEnable = ViewBindings.findChildViewById(rootView, id);
      if (textEnable == null) {
        break missingId;
      }

      id = R.id.text_latency;
      TextView textLatency = ViewBindings.findChildViewById(rootView, id);
      if (textLatency == null) {
        break missingId;
      }

      id = R.id.text_latency_detail;
      TextView textLatencyDetail = ViewBindings.findChildViewById(rootView, id);
      if (textLatencyDetail == null) {
        break missingId;
      }

      return new ActivityMonitorBinding((LinearLayout) rootView, backButton, captureEvent,
          capturedEvents, changeToMiniEvents, changeToSmallEvents, containerEnable,
          containerLatency, permissionChecker, spinnerLatency, switchEnable, textEnable,
          textLatency, textLatencyDetail);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

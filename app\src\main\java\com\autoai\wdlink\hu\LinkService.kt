package com.autoai.wdlink.hu

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ServiceInfo

import android.graphics.Color
import android.net.LocalSocket
import android.os.Build
import android.os.IBinder
import android.os.Process
import androidx.annotation.RequiresApi
import androidx.lifecycle.Observer
import com.autoai.car.os_adapter.proxy.CarInfoProxy
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState
import com.autoai.wdlink.hu.castwindow.utils.ThreadInfoUtils
import com.autoai.wdlink.hu.castwindow.view.CastWindowStateManager
import com.autoai.wdlink.hu.dialog.BluetoothWifiUtil
import com.autoai.wdlink.hu.frame.utils.BluetoothReceiver
import com.autoai.wdlink.hu.frame.utils.PopWinServiceUtils
import com.autoai.wdlink.hu.frame.utils.RequestBTMusicFocusUtil
import com.autoai.wdlink.hu.lifecycle.ActivityStateGetter
import com.autoai.wdlink.hu.lifecycle.NetworkMonitor
import com.autoai.wdlink.hu.model.PageModel
import com.autoai.wdlink.hu.sdk.LinkSdkModel
import com.autoai.wdlink.hu.sdk.LinkUtil
import com.autoai.wdlink.hu.utils.isOverlayPermissionGranted
import com.autoai.welinkapp.model.UIResponder
import com.autoai.welinkapp.util.WindowUtil


class LinkService : Service() {
    companion object{
        private val TAG = "${ComponentName.Link}${LinkService::class.java.name}"
        fun startLinkService(context: Context) {

            LinkLog.i(TAG, "startLinkService: stack: ${ThreadInfoUtils.getStackTraces()}")
            val intent = Intent()
            intent.setAction("com.gn2023oversea.android.launcher.LinkService")
            intent.setClass(context, LinkService::class.java)
            context.startForegroundService(intent)
        }
        fun stopLinkService(context: Context) {
            LinkLog.i(TAG, "stopLinkService")
            val intent = Intent()
            intent.setAction("com.gn2023oversea.android.launcher.LinkService")
            intent.setClass(context, LinkService::class.java)
            context.stopService(intent)
        }
    }

    private val NOTIFICATION_ID: Int = 123
    private val mBluetoothReceiver by lazy{
        BluetoothReceiver()
    }
    private var networkMonitor : NetworkMonitor? = null
    /**
     * service 工作线程
     */
//    private lateinit var handlerThread: HandlerThread

    /**
     * service 工作线程handler
     */
//    private lateinit var serviceHandler: Handler

    // 定义需要检查的权限
    private val REQUIRED_PERMISSIONS = arrayOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION,
        Manifest.permission.BLUETOOTH_ADMIN,
        Manifest.permission.BLUETOOTH_ADVERTISE,
        Manifest.permission.BLUETOOTH_CONNECT,

        )

    override fun onCreate()   {
        LinkLog.d(TAG, "onCreate")
        startForeground()
        //---> 注册页面迁移相关的event回到到sdk传输层
        LinkSdkModel.exteriorPageEvent.observeForever(pageEventObserver)
        //---> service 工作线程
/*        handlerThread = HandlerThread("LinkServiceHT")
        handlerThread.start()

        //---> service 工作线程handler
        serviceHandler = Handler(handlerThread.looper)*/

        //---> 初始化和窗体相关参数
        var windowUtil: WindowUtil = WindowUtil.getInstance(applicationContext)
        windowUtil.setScreenWH(
            (windowUtil.screenWidth * LinkUtil.screenScale).toInt(),
            (windowUtil.screenHeight * LinkUtil.screenScale).toInt()
        )
        //---> 监听系统蓝牙原生状态action
        registerBlueTooth()
        //---> 初始化档位相关逻辑
        CarInfoProxy.init(this)
        networkMonitor = NetworkMonitor(this)
        BluetoothWifiUtil.checkAndOpenBluetooth()
        BluetoothWifiUtil.checkAndOpenWifi(this)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LinkLog.d(TAG,"onStartCommand")
        if (isOverlayPermissionGranted(applicationContext)) {
            // 权限已被授予
            init()
            startPopWinService()
        } else {
            // 权限未被授予
            LinkLog.d(TAG,"permission not granted !!!")
        }
        return START_STICKY
    }

    private fun init() {
        if (!LinkUtil.isConnected()) {
            LinkUtil.initLink(this)
            LinkLog.d(TAG,"initLink ...")
        }
    }

    fun registerBlueTooth() {
        //监听蓝牙连接状态
        LinkLog.d(TAG, "registerBlueTooth")
        var filter = IntentFilter()
        filter.addAction(RequestBTMusicFocusUtil.getReceiveProperty("ACTION_CONNECTION_STATE_CHANGED"))
        registerReceiver(mBluetoothReceiver, filter,Context.RECEIVER_NOT_EXPORTED);
    }

    override fun onBind(p0: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        mBluetoothReceiver?.let {
            LinkLog.d(TAG, "unregisterReceiver")
            unregisterReceiver(it)
        }
        networkMonitor?.let {
            it.unregisterNetworkCallback()
        }
        // 移除前台服务的通知
        stopForeground(STOP_FOREGROUND_REMOVE)
        // 停止服务自身
        stopSelf()
        super.onDestroy()
        LinkLog.d(TAG,"LinkService onDestroy")
        LinkSdkModel.exteriorPageEvent.removeObserver(pageEventObserver)
//        handlerThread.quit()
    }


    private val pageEventObserver by lazy {
        Observer<PageModel> {
            //如果在前台由 MainActivity 处理显示浮窗逻辑，这个地方直接 return
            if (ActivityStateGetter.isActivityInForeground && CastWindowStateManager.getCurrentState() == CastWindowState.NONE) return@Observer
            LinkLog.d(TAG, "PageEventObserver: ${it.page}")
            when (it.page) {
                UIResponder.SCREEN_PAGE_MIRRORING -> {
                    PopWinServiceUtils.showPopWin(this)
                    //连接过程中退出引导后台service居中显示浮窗
                   /* Handler().postDelayed({
                        PopWinServiceUtils.showPopWinLayout(this)
                    }, 100)*/
                }
            }

        }
    }

    fun startPopWinService() {
        PopWinServiceUtils.startPopWinService(this)
    }


    private fun startForeground() {
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE){
                start34Foreground()
        }
        else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startOForeground()
        } else {
            val builder = Notification.Builder(this).setContentTitle(getString(R.string.app_name))
                .setContentText("正在运行")
            val notification = builder.build()
            startForeground(NOTIFICATION_ID, notification)
        }
    }

    private fun startOForeground() {
        val notificationChannelId = "com.autoai.car.welink3"
        val channelName = "WeLink Service"
        val chan = NotificationChannel(
            notificationChannelId, channelName, NotificationManager.IMPORTANCE_NONE
        )
        chan.lightColor = Color.BLUE
        chan.lockscreenVisibility = Notification.VISIBILITY_PRIVATE
        val manager = (getSystemService(NOTIFICATION_SERVICE) as NotificationManager)
        manager.createNotificationChannel(chan)
        val notificationBuilder = Notification.Builder(this, notificationChannelId)
        val notification = notificationBuilder.setContentTitle(getString(R.string.app_name))
            .setContentText("正在运行").build()
        startForeground(NOTIFICATION_ID, notification)
    }


    @RequiresApi(api = Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun start34Foreground() {
        val notificationChannelId = "com.autoai.car.welink3"
        val channelName = "WeLink Service"
        val chan = NotificationChannel(
            notificationChannelId, channelName, NotificationManager.IMPORTANCE_NONE
        )
        chan.lightColor = Color.BLUE
        chan.lockscreenVisibility = Notification.VISIBILITY_PRIVATE
        val manager = (getSystemService(NOTIFICATION_SERVICE) as NotificationManager)
        manager.createNotificationChannel(chan)
        val notificationBuilder = Notification.Builder(this, notificationChannelId)
        val notification = notificationBuilder.setContentTitle(getString(R.string.app_name))
            .setContentText("正在运行").build()
        startForeground(NOTIFICATION_ID, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE)
    }
}
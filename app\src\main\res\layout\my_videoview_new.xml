<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00009929">

    <com.autoai.wdlink.hu.module.hidscreen.MultiTouchView
        android:id="@+id/root_view_parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <!-- 左边扩展区域 -->
        <View
            android:id="@+id/view_1"
            android:layout_width="@dimen/extend_size2"
            android:layout_height="match_parent"
            android:background="#0033bb33" />

        <!-- 右边扩展区域 -->
        <View
            android:id="@+id/view_2"
            android:layout_width="@dimen/extend_size2"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="#0033bb33" />

        <!-- 上边扩展区域 -->
        <View
            android:id="@+id/view_3"
            android:layout_height="@dimen/extend_top_size"
            android:layout_width="match_parent"
            android:background="#0033bb33" />

        <!-- 下边扩展区域 -->
        <View
            android:id="@+id/view_4"
            android:layout_height="@dimen/extend_bottom_size"
            android:layout_width="match_parent"
            android:layout_alignParentBottom="true"
            android:background="#0033bb33" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/root_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/view_4"
            android:layout_below="@+id/view_3"
            android:layout_toStartOf="@+id/view_2"
            android:layout_toEndOf="@+id/view_1"
            android:background="#00000444">

            <!-- 顶部拓展功能条 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/console"
                android:layout_width="0dp"
                android:layout_height="@dimen/dimen_45"
                android:background="@drawable/btn_click_f"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageButton
                    android:id="@+id/mini"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:paddingLeft="20dp"
                    android:paddingRight="40dp"
                    android:src="@drawable/mini_button_selector"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />


                <ImageButton
                    android:id="@+id/move"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:background="@null"
                    android:src="@drawable/move_button_selector"
                    app:layout_constraintLeft_toRightOf="@+id/mini"
                    app:layout_constraintRight_toLeftOf="@+id/calibrate_bluetooth"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>
                <ImageButton
                    android:id="@+id/calibrate_bluetooth"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    app:layout_constraintRight_toLeftOf="@+id/fullscreen"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />
                <ImageButton
                    android:id="@+id/fullscreen"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginRight="0px"
                    android:background="@null"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp"
                    android:src="@drawable/fullscreen_button_selector"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 视频操作区域 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/sv_framelayout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@drawable/window_bg"
                android:padding="@dimen/dimen_10"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/console"
                app:layout_constraintBottom_toBottomOf="parent">

                <com.autoai.wdlink.hu.module.hidscreen.InterceptFrameLayout
                    android:id="@+id/sv_framelayout_inner"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <TextureView
                        android:id="@+id/phoneData_sv"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center" />
                    <!-- 封面图 -->
                    <RelativeLayout
                        android:id="@+id/view_shot_cover_parent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:alpha="0"
                        android:background="#303030">

                        <ImageView
                            android:id="@+id/view_shot_cover"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:visibility="visible" />
                    </RelativeLayout>
                    <TextView
                        android:id="@+id/tv_fps"
                        android:layout_width="60dp"
                        android:layout_height="30dp"
                        android:layout_marginLeft="15dp"
                        android:layout_marginTop="20dp"
                        android:textSize="18sp"
                        android:textColor="#ff33bb33"
                        />
                </com.autoai.wdlink.hu.module.hidscreen.InterceptFrameLayout>

                <!-- 锁屏提示UI -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/sc_on_off_tip"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <View
                        android:id="@+id/screen_bgve"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/screen_off_phone_bg"
                        />

                    <ImageView
                        android:layout_width="268px"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        android:layout_marginTop="56px"
                        android:layout_height="268px"
                        android:id="@+id/floating_window_image" />

                    <TextView
                        android:id="@+id/screen_off_title"
                        android:layout_width="match_parent"
                        android:gravity="center"
                        android:layout_height="wrap_content"
                        android:text="@string/pop_window_phone_screen_off"
                        android:textColor="#17417C"
                        android:textSize="28px"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        android:layout_marginTop="36px"
                        android:layout_marginLeft="28px"
                        android:layout_marginRight="28px"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/floating_window_image"
                        app:layout_constraintVertical_bias="0.3"
                        app:layout_constraintVertical_chainStyle="packed" />

                    <TextView
                        android:id="@+id/screen_off_reason"
                        android:layout_width="305px"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="96px"
                        android:gravity="center"
                        android:text="@string/pop_window_unlock_continue_cast"
                        android:textColor="#17417C"
                        android:textSize="18px"
                        android:layout_marginLeft="28px"
                        android:layout_marginRight="28px"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/floating_window_image" />

                    <TextView
                        android:id="@+id/recovery_cast"
                        android:layout_width="match_parent"
                        android:layout_height="72px"
                        android:layout_marginLeft="38px"
                        android:layout_marginRight="38px"
                        android:background="@drawable/global_selector_btn_bg_normal"
                        android:clickable="true"
                        android:gravity="center"
                        android:text="@string/pop_window_recovery_cast"
                        android:layout_marginBottom="12px"
                        android:textColor="#fff"
                        android:textSize="24px"
                        app:layout_constraintBottom_toTopOf="@+id/btn_exit"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <TextView
                        android:id="@+id/btn_exit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="38dp"
                        android:clickable="true"
                        android:gravity="center"
                        android:paddingStart="50dp"
                        android:paddingTop="10dp"
                        android:paddingEnd="50dp"
                        android:paddingBottom="10dp"
                        android:text="@string/exit_app"
                        android:textColor="#ffed0000"
                        android:textSize="20px"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <include layout="@layout/safe_driver_old" />
                <include layout="@layout/safe_driver" />

                <!--校准提示UI-->
                <RelativeLayout
                    android:id="@+id/pop_calibration_lin"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    tools:visibility="gone">

                    <ImageView
                        android:id="@+id/pop_bg_img"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/jb_calibration_phone_bg"
                        android:gravity="center"/>

                    <ImageView
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="105px"
                        android:id="@+id/calibration_failed_img"
                        android:layout_width="268px"
                        android:layout_height="268px"
                        android:gravity="center"/>

                    <TextView
                        android:id="@+id/pop_calibration_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:textColor="#17417C"
                        android:layout_below="@+id/calibration_failed_img"
                        android:layout_marginLeft="38px"
                        android:layout_marginRight="38px"
                        android:layout_marginTop="36px"
                        android:gravity="center"
                        android:text="校准失败"
                        android:textSize="28px" />

                    <TextView
                        android:id="@+id/pop_calibration_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="60px"
                        android:layout_marginTop="96px"
                        android:layout_marginRight="60px"
                        android:layout_below="@+id/calibration_failed_img"
                        android:gravity="center"
                        android:textColor="#17417C"
                        android:text="校准过程中请勿触碰手机 校准失败可能会导"
                        android:textSize="18px"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/pop_recalibrate"
                        android:layout_width="300px"
                        android:layout_height="72dp"
                        android:layout_centerHorizontal="true"
                        android:layout_gravity="center"
                        android:layout_marginTop="195px"
                        android:layout_below="@+id/calibration_failed_img"
                        android:background="@drawable/global_selector_btn_bg_normal"
                        android:clickable="true"
                        android:gravity="center"
                        android:text="重新校准"
                        android:textColor="#fff"
                        android:textSize="24px"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </RelativeLayout>
                <FrameLayout
                    android:id="@+id/dialog_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:visibility="gone" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 右上角关闭投影，隐藏的测试功能 -->
            <ImageView
                android:id="@+id/close"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:src="@mipmap/a7_btn_close_p"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintLeft_toRightOf="@+id/sv_framelayout"
                app:layout_constraintTop_toTopOf="@+id/sv_framelayout" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- 左下角缩放功能 -->
        <Button
            android:id="@+id/resize_left"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_alignStart="@+id/root_view"
            android:layout_alignBottom="@+id/root_view"
            android:layout_marginLeft="-30dp"
            android:layout_marginBottom="-30dp"
            android:background="@drawable/btn_resize_move_bg"
            android:visibility="visible"
            tools:visibility="visible" />

        <!-- 右下角缩放功能 -->
        <Button
            android:id="@+id/resize"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_alignEnd="@+id/root_view"
            android:layout_alignBottom="@+id/root_view"
            android:layout_marginRight="-30dp"
            android:layout_marginBottom="-30dp"
            android:background="@drawable/btn_resize_move_bg"
            android:visibility="visible"
            tools:visibility="visible" />

        <!-- 时间 隐藏的测试功能-->
        <TextView
            android:id="@+id/tv_time"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:background="#f00"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />
    </com.autoai.wdlink.hu.module.hidscreen.MultiTouchView>

</RelativeLayout>
package com.autoai.wdlink.hu.sdk

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.MotionEvent
import android.view.Surface
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.autoai.avslinkhid.api.HidIosCalibrationUtil
import com.autoai.avslinkhid.api.HidUtil
import com.autoai.car.os_adapter.proxy.CarInfoProxy
import com.autoai.common.util.LogUtil
import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.MainActivity
import com.autoai.wdlink.hu.MyApplication
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState
import com.autoai.wdlink.hu.castwindow.utils.ThreadInfoUtils
import com.autoai.wdlink.hu.castwindow.view.CastWindowStateManager
import com.autoai.wdlink.hu.frame.utils.BtMusicFocusManager
import com.autoai.wdlink.hu.frame.utils.PopWinServiceUtils
import com.autoai.wdlink.hu.frame.utils.RequestBTMusicFocusUtil
import com.autoai.wdlink.hu.frame.utils.UnPeekLiveData
import com.autoai.wdlink.hu.lifecycle.ActivityStateGetter
import com.autoai.wdlink.hu.model.DevelopModel
import com.autoai.wdlink.hu.model.DevicesTypeModel
import com.autoai.wdlink.hu.model.IosHidModel
import com.autoai.wdlink.hu.model.PageModel
import com.autoai.wdlink.hu.model.PageModel.Companion.toPageStr
import com.autoai.wdlink.hu.network.helper.DeviceInfoHelper
import com.autoai.wdlink.hu.sdk.device.AoaDeviceImpl
import com.autoai.wdlink.hu.sdk.device.IusbDevice
import com.autoai.wdlink.hu.sdk.device.PopHidCheckCallback
import com.autoai.wdlink.hu.utils.ToastUtil
import com.autoai.welinkapp.LinkHost
import com.autoai.welinkapp.audio.AudioPlayer
import com.autoai.welinkapp.checkconnect.CommonData
import com.autoai.welinkapp.checkconnect.CommonData.LinkStatus
import com.autoai.welinkapp.model.AutoAgreeCallback
import com.autoai.welinkapp.model.ButtonType
import com.autoai.welinkapp.model.HidCallback
import com.autoai.welinkapp.model.HidVerificationType
import com.autoai.welinkapp.model.MessageEvent
import com.autoai.welinkapp.model.OnLinkStatusChangeListener
import com.autoai.welinkapp.model.OnMessageListener
import com.autoai.welinkapp.model.SdkConfig
import com.autoai.welinkapp.model.SdkViewModel
import com.autoai.welinkapp.model.UIResponder
import com.autoai.welinkapp.platform.LinkPlatform
import com.autoai.welinkapp.util.PermissionUtils
import com.blankj.utilcode.util.Utils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

object LinkSdkModel {
    private val TAG = "LinkSdkModel"
    private var weakReference: WeakReference<Context>? = null
    private var pageValue: AtomicInteger = AtomicInteger(0)

    /**
     * 目前实际上是蓝牙 bt device ：name
     */
    var address: String? = ""

    /**
     * 当前连接类型
     */
    var devicesEventstatus: Int? = null
    var popHidCheckCallback: PopHidCheckCallback? = null


    /**
     * 用于判断 超时后  前后台切换是否 调用startPopwin 准备互联
     * true 调用  false不调用
     */
    var mStratPop: AtomicBoolean = AtomicBoolean(false)
    var connectStatus: Int = 0

    /**
     * 连接状态
     **/
    @LinkStatus
    private var linkStatus: Int = CommonData.LINK_STATUS_NONE

    /**
     * 页面
     * */
    private val interiorPageEvent by lazy {
        MutableLiveData<PageModel>()
    }
    val exteriorPageEvent: LiveData<PageModel>
        get() = interiorPageEvent

    private val devicesEvent by lazy {
        UnPeekLiveData<DevicesTypeModel>()
    }
    val devicesTypeEvent: UnPeekLiveData<DevicesTypeModel>
        get() = devicesEvent

    private val iosEvent by lazy {
        UnPeekLiveData<IosHidModel>()
    }
    val iosHidTypeEvent: UnPeekLiveData<IosHidModel>
        get() = iosEvent
    /**
     * 连接状态
     * */
    private val statusChangeListener =
        OnLinkStatusChangeListener { status, devicesType ->
            LinkLog.i(
                TAG, "statusChangeListener: onChange: " +
                        "status = ${LinkStatus.Print.toString(status)}, " +
                        "linkStatus = ${LinkStatus.Print.toString(linkStatus)}, " +
                        "devicesType = ${CommonData.DeviceType.Print.toString(devicesType)}, " +
                        "stack: ${ThreadInfoUtils.getStackTraces()}"
            )
            RequestBTMusicFocusUtil.isConnectPhone = false
            //connectStatus = status
            linkStatus = status
            when (status) {
                CommonData.LINK_STATUS_NONE -> {
                    LinkLog.printStackTraceString(
                        TAG,
                        "LinkSdkModel::OnLinkStatusChangeListener::CommonData.LINK_STATUS_NONE "
                    )
                    connectStatus = status
                    devicesEvent.postValue(DevicesTypeModel(CommonData.DEVICE_TYPE_NONE))
                    Logger.d("USB连接状态：未连接 status = $status, devicesType = $devicesType")
                    BtMusicFocusManager.abandonBtMusicFocus(null)
                    BtMusicFocusManager.isConnectPhone = false
                }

                CommonData.LINK_STATUS_FAIL -> {//TODO: bruce 链接失败
                    LinkLog.printStackTraceString(
                        TAG,
                        "LinkSdkModel::OnLinkStatusChangeListener::CommonData.LINK_STATUS_FAIL "
                    )

                    connectStatus = status
                    Logger.d("USB连接状态：连接失败 status = $status, devicesType = $devicesType")
                    devicesEvent.postValue(DevicesTypeModel(CommonData.DEVICE_TYPE_NONE))
                    // 不需要主动断开，如果切换设备类别才断开 TAG_HID
                    //HidUtil.unregisterHID();
                }

                CommonData.LINK_STATUS_CONNECTING -> {
                    LinkLog.printStackTraceString(
                        TAG,
                        "LinkSdkModel::OnLinkStatusChangeListener::CommonData.LINK_STATUS_CONNECTING "
                    )
                    Logger.d("USB连接状态：连接中 status = $status, devicesType = $devicesType")
                }

                CommonData.LINK_STATUS_CONNECTED -> {
                    LinkLog.printStackTraceString(
                        TAG,
                        "LinkSdkModel::OnLinkStatusChangeListener::CommonData.LINK_STATUS_CONNECTED "
                    )
                    connectStatus = status
                    //todo 连接的如果是IOS手机回调校准State
                    if (devicesType == CommonData.DEVICE_TYPE_WIFI_IOS) {
                        registerIosHidCheckCallback()
                    }
                    devicesEventstatus = devicesType
                    PopWinServiceUtils.notifyScreenState(MyApplication.application, "connected")
                    devicesEvent.postValue(DevicesTypeModel(devicesType))
                    Logger.d("USB连接状态：已连接 status = $status, devicesType = $devicesType")

                    //--> 申请音乐焦点
                    RequestBTMusicFocusUtil.isConnectPhone = true
                    BtMusicFocusManager.isConnectPhone = true
                    BtMusicFocusManager.requestBtMusicFocus(null)
                }

                CommonData.LINK_STATUS_SCREEN_MIRRORING -> {
                    LinkLog.printStackTraceString(
                        TAG,
                        "LinkSdkModel::OnLinkStatusChangeListener::CommonData.LINK_STATUS_SCREEN_MIRRORING "
                    )
                    if (devicesType != CommonData.DEVICE_TYPE_WIFI_IOS ||
                        HidIosCalibrationUtil.getHidVerificationState() == HidVerificationType.HID_VERIFY_TYPE_VERIFICATION_SUCCESS) {
                        HidUtil.backHome()
                    }
                    else {
                        Logger.d("LINK_STATUS_SCREEN_MIRRORING getHidVerificationState: HID_VERIFY_TYPE_VERIFICATION_ING ")
                    }

                    connectStatus = status
                    //devicesEvent.postValue(DevicesTypeModel(devicesEvent))
                    Logger.d("USB连接状态：开始录屏 status = $status, devicesType = $devicesType")
                }

                else -> {
                    Logger.d("USB连接状态：未知 status = $status, devicesType = $devicesType")
                }
            }
        }

    /**
     * 是否处于连接中
     */
    fun isConnected(): Boolean {
        return connectStatus == CommonData.LINK_STATUS_CONNECTED || connectStatus == CommonData.LINK_STATUS_SCREEN_MIRRORING
    }

    fun logAble(logDebug: Boolean, fileDebug: Boolean) {
        Logger.i("logAble: logDebug = $logDebug, fileDebug = $fileDebug")
        LinkHost.applyDebug(logDebug, fileDebug)
    }

    fun logFullLog(logDebug: Boolean) {
        Logger.i("logAble: IsFullLog = $logDebug")
        LogUtil.setIsFullLog(logDebug)
    }

    fun initLink(
        context: Context,
        linkPlatform: LinkPlatform? = null,
        audioPlayer: AudioPlayer? = null
    ) {
        Logger.i("initLink")
        val sdkConfig = SdkConfig.Builder()
            .setAoaDevice(AoaDeviceImpl())
            .setEapDevice(IusbDevice())
            .setPlatform(linkPlatform)
            .setAudioPlayer(audioPlayer)
            .setFps(30)
            .setRtt(DevelopModel.mDevelopModel.data.rtt)
            .setSerialNum(DeviceInfoHelper.getCidFromVinOrAndroidIdOrMacAdd())
            .build()
        weakReference = WeakReference(context)
        LinkHost.init(context, sdkConfig)
        LinkHost.addLinkStateChangeListener(statusChangeListener)
        Logger.i("authWindowStatus = ${DevelopModel.getInstance().authWindowStatus}")
        if (DevelopModel.getInstance().authWindowStatus) {
            autoAgree()
        }

        DevelopModel.getInstance().setAuthWindowListener {
            Logger.i("autoAgree: authWindowStatus = $it")
            if (it) {
                autoAgree()
            } else {
                LinkHost.unregisterAutoAgreeCallback()
            }
        }
    }

    fun autoAgree() {
        LinkHost.registerAutoAgreeCallback(AutoAgreeCallback {
            //todo 目前阶段只处理true情况，后续量产需要考虑false情况
            if (it) {
                HidUtil.autoAgree(1500)
            }
        })
    }

    fun initUiResponder() {
        LinkLog.i(TAG, "initUiResponder: invoke ")
        //--> UI狀態回调
        LinkHost.registerUiResponder { page, param ->
            val pageModel = PageModel(page, param)
            LinkLog.printStackTraceString(TAG,"LinkSdkModel::UiResponder page event -> ${page.toPageStr()}" )
            Logger.i("initUiResponder 异常系日志: onUpdatePage: pageModel = $pageModel, pageValue = ${pageValue.get()},State= ${CastWindowStateManager.getCurrentState()}")
            interiorPageEvent.postValue(pageModel)
            if (page != pageValue.get()) {
                when (page) {
                    UIResponder.INITIAL_PAGE -> { //初始状态（首次启动 或 拔线等异常情况恢复初始状态）
                        //当处于后台时  初始化状态不启动互联引导页
                        if (!mStratPop.get() && CastWindowStateManager.getCurrentState() == CastWindowState.NONE && pageValue.get() == UIResponder.ERR_PAGE_CONNECT) {
                            LinkLog.i(TAG, "initUiResponder: pageValue is ERR_PAGE_CONNECT");
                            //保证超时后 从后台切换引导页会前台 能重新拉起互联
                            mStratPop.set(true)
                        } else {
                            retryLink(page.toPageStr())
                            if (isConnected(linkStatus)) { //从连接中到断开状态提示 toast
                                ToastUtil.showToast(
                                    Utils.getApp().getString(R.string.connect_abort),
                                    "",
                                    "",
                                    "",
                                    false,
                                    false,
                                    -1
                                )
                            }
                        }
                    }
                    //TODO:bruce 异常断开（体现：关浮窗）
                    UIResponder.ERR_PAGE_HEART,
                    UIResponder.ERR_PAGE_SDK -> {
                        PopWinServiceUtils.stopPopWinService(MyApplication.application)
                        LinkHost.resetConnectStatus()

                        ToastUtil.showToast(
                            Utils.getApp().getString(R.string.connect_abort),
                            "",
                            "",
                            "",
                            false,
                            false,
                            -1
                        )
                    }

                    UIResponder.ERR_PAGE_CONNECT -> {
                        PopWinServiceUtils.stopPopWinService(MyApplication.application)
                        LinkHost.resetConnectStatus()
                    }

                    UIResponder.CONNECTING_PAGE -> {
                        // 如果为后台则提示
                        if (!ActivityStateGetter.isActivityInForeground) {
                            ToastUtil.showToast(
                                Utils.getApp().getString(R.string.welink_is_currently_connecting),
                                "",
                                "",
                                "",
                                false,
                                false,
                                -1
                            )
                        }
                    }

                    UIResponder.SHOW_USB_INTERFACE_DIALOG -> {
                        Logger.v("uiResponder: navigate - > SHOW_USB_INTERFACE_DIALOG")
                        PopWinServiceUtils.showUSBPopWin(MyApplication.application)
                    }

                    UIResponder.DISMISS_USB_INTERFACE_DIALOG -> {
                        Logger.v("uiResponder: navigate - > DISMISS_USB_INTERFACE_DIALOG")
                        PopWinServiceUtils.dismissUSBPopWin(MyApplication.application)
                    }
                }
            }
            pageValue.set(page)
        }
    }

    /**
     * 重连
     */
    private fun retryLink(refer: String) {
        LinkLog.i(TAG, "retryLink: refer = $refer")
        CoroutineScope(Dispatchers.Main).launch {
            LinkLog.i(TAG, "retryLink: stopPopWinService")
            PopWinServiceUtils.stopPopWinService(MyApplication.application)
            delay(1000)

            LinkLog.i(TAG, "retryLink: startPopWinService")
            PopWinServiceUtils.startPopWinService(MyApplication.application)
        }
    }

    fun readyForLink(activity: Context) {
        Logger.i("readyForLink")
        LinkHost.readyForLink(activity)
    }

    fun addMessageListener(listener: OnMessageListener) {
        Logger.i("addMessageListener")
        LinkHost.addMessageListener(listener)
    }

    fun registerLinkSurface(surface: Surface, width: Int, height: Int) {
        LinkHost.registerLinkSurface(surface, width, height)
    }

    fun unRegisterLinkSurface(surface: Surface) {
        LinkHost.unRegisterLinkSurface(surface)
    }

    fun stopForLink(context: Context) {
        Logger.i("stopForLink")
        LinkHost.stopForLink(context, false)
    }

    fun removeMessageListener(listener: OnMessageListener) {
        Logger.i("removeMessageListener")
        LinkHost.removeMessageListener(listener)
    }

    fun sendButton(type: Int, param: Int) {
        Logger.i("sendButton type = $type, param = $param")
        LinkHost.sendButton(type, param)
    }

    fun help() {
        Logger.i("sendButton CLICK_FOR_HELP")
        sendButton(ButtonType.CLICK_FOR_HELP, 1)
    }

    fun tisp() {
        Logger.i("sendButton CLICK_FOR_HELP")
        sendButton(ButtonType.CLICK_FOR_HELP, 1)
    }

    fun iKnow() {
        Logger.i("sendButton GOT_IT")
        sendButton(ButtonType.GOT_IT, 1)
    }

    fun screenTouch(motionEvent: MotionEvent) {
        LinkHost.screenTouch(motionEvent)
    }

    fun setHidRegionCallback(hidCallback: HidCallback) {
        LinkHost.setHidRegionCallback(hidCallback)
    }

    fun registerPopHidCheckCallback(popHidCheckCallback: PopHidCheckCallback) {
        this.popHidCheckCallback = popHidCheckCallback
    }

    /**
     * 开始校准，车机端发起， 发送消息给手机端， 手机端收到消息后，开始校准
     */
    fun startVerification() {
        LinkHost.startVerification()
    }

    private fun registerIosHidCheckCallback() {
        LinkHost.registerHidCheckCallback {
            Logger.i("registerHidCheckCallback === $it")
            iosHidTypeEvent.postValue(IosHidModel(it))
            popHidCheckCallback?.onCheck(it)
        }
    }

    /**
     * 画面从后台切换到前台， 开始显示（一般互联成功 / 从最小化到画面显示）
     */
    fun onResume() {
        LinkHost.onResume()
    }

    /**
     * 暂停画面， 画面从前台切换到后台， 暂停画面（一般断开连接 / 从画面显示到最小化）
     */
    fun onPause() {
        LinkHost.onPause()
    }

    fun destroy() {
        connectStatus = 0
        linkStatus = CommonData.LINK_STATUS_NONE
        weakReference?.clear()
        Logger.i("destroy")
        LinkHost.removeLinkStateChangeListener(statusChangeListener)
        LinkHost.destroy()
    }

    fun receivePhoneMessage(messageEvent: MessageEvent?) {
        if (messageEvent != null) {
            when (messageEvent.what) {
                MessageEvent.RECEIVE_MESSAGE -> {
                    LinkLog.i(TAG, "MsgCallback Type = MessageEvent.RECEIVE_MESSAGE")
                    var key = ""
                    try {
                        val obj = messageEvent.obj.toString()
                        LinkLog.i(TAG, "MsgCallback obj = $obj")
                        val json = JSONObject(obj)
                        key = json.get("key").toString()
                        val extra = json.optString("extra")
                        LinkLog.i(
                            TAG,
                            "MsgCallback key = ${key == "address"}" + " === key =   ${key},extra:$extra"
                        )
                        when (key) {
                            "cancel_cast" -> {
                                val pageModel =
                                    PageModel(UIResponder.SCREEN_REQ_RECORD_SCREEN, null)
                                interiorPageEvent.postValue(pageModel)
                                PopWinServiceUtils.notifyScreenState(MyApplication.application, key)

                            }

                            "reauthorize_record_screen" -> {
                                if ("start_record_screen" == extra) {
                                    //android 端才会发送改消息
                                    ToastUtil.showToast(
                                        Utils.getApp()
                                            .getString(R.string.welink_mobile_confirms_screen_mirroring),
                                        "",
                                        "",
                                        "",
                                        false,
                                        false,
                                        -1
                                    )
                                } else
                                    PopWinServiceUtils.notifyScreenState(
                                        MyApplication.application,
                                        extra
                                    )
                            }

                            "screen_off",
                            "screen_on",
                            "stream_abort",
                            "stream_recovery" -> {
                                HidIosCalibrationUtil.cancelHeartbeat()
                                PopWinServiceUtils.notifyScreenState(MyApplication.application, key)
                            }

                            "notify_phone_play_video_state" -> {
                                //将收到结果回调给应用层
                                val isPlay = json.getInt("value")
                                CarInfoProxy.onCheckIsPlayVideoResult(isPlay);
                            }

                            "phone_device_info" -> {
                                CoroutineScope(Dispatchers.Main).launch {
                                    val model = json.get("model").toString()
                                    address = json.get("address").toString()
                                    val deviceName = json.get("deviceName").toString()
                                    val deviceBrand = json.get("deviceBrand").toString()
                                    LinkLog.d(
                                        TAG,
                                        "phone_device_info = $model" + "_" + "$deviceName" + "_" + "$deviceBrand"
                                    )
                                    LinkLog.d(TAG, "address_address = $address")
                                    //将手机设备蓝牙名称传入
                                    HidUtil.setName(
                                        model + "_" + deviceName + "_" + deviceBrand,
                                        address
                                    )
                                }
                            }
                        }
                    } catch (e: JSONException) {
                        Logger.e("消息解析错误", e)
                    }
                }

                else -> {
                    Logger.v("MsgCallback Type = others --> ${messageEvent.what}")
                }
            }
        }
    }
}
<!-- Generated by SpecStory -->

# iOS 校准流程时序分析 (2025-06-21 07:36Z)

_**User**_

请分析整个工程根据关键词 CalibrationCallBack,  校准流程文档,  梳理整个 iOS 校准流程。 从互联成功开始。 到校准成功，或者校准失败各个步骤的时序。 请详细输出，等我确认。 需要补充说明的是，这个项目是 车机端，会与 iOS 端互联，所以是两个设备相互通讯和交互的一个过程。 请详细输出，等我确认。 请详细输出，等我确认。 需要输出相互交互的时序。 第一步，第二步骤，这样。

---

_**Assistant**_

我将分析iOS校准流程的时序，从互联成功开始到校准成功或失败的整个过程。

# iOS校准流程时序分析

## 概述
这是一个车机端与iOS设备互联后的校准流程，用于确保iOS设备的反控功能正常工作。校准过程涉及车机端和iOS端的交互，通过蓝牙HID连接实现。

## 详细时序流程

### 第一步：互联成功
1. 车机端和iOS设备成功建立连接
   - 通过`LinkSdkModel.INSTANCE.isConnected()`可以检测连接状态
   - 连接成功后，车机端会收到"show"动作，触发`onStartCommand`方法

### 第二步：检查蓝牙连接状态
1. 车机端检查蓝牙连接状态
   - 在`onStartCommand`的"show"分支中调用`determineIfBluetoothIsConnected()`
   - 该方法延迟500ms后调用`bluetoothStateManager.checkAndUpdateBluetoothState()`
   - 检查是否有连接的蓝牙设备，以及是否与互联设备配对

### 第三步：判断是否需要校准
1. 如果是iOS设备，检查校准状态
   - 通过`isIOSDevice()`方法判断是否为iOS设备
   - 如果校准状态为`FAILED`、`APP_NOT_FOREGROUND`或`BT_NOT_CONNECTED`，则需要校准
   - 显示校准图标`mBlueToothOrCalibration.setImageResource(R.mipmap.calibration_icon)`

### 第四步：用户触发校准
1. 用户点击校准图标
   - 触发`handleBluetoothButtonClick()`方法
   - 当`bluetoothAbnormalState`为`IOS_NEEDS_CALIBRATION`时:
     - 隐藏校准按钮`mBlueToothOrCalibration.setVisibility(View.GONE)`
     - 调用`LinkSdkModel.INSTANCE.startVerification()`开始校准

### 第五步：开始校准过程
1. 车机端发送校准请求到iOS设备
   - `startVerification()`方法通过SDK发送校准请求
   - iOS设备收到请求后，需要在前台显示校准界面

### 第六步：监听校准结果
1. 车机端注册校准回调
   - 在`CalibrationMonitoringCallback()`方法中注册回调
   - 通过`LinkSdkModel.INSTANCE.registerPopHidCheckCallback()`监听校准状态

### 第七步：处理校准结果
1. 收到校准回调
   - iOS设备完成校准后，通过SDK回传结果
   - 车机端在回调中接收校准结果值`isCheck`
   - 将结果转换为`CalibrationStatus`枚举值

2. 根据校准结果更新UI和状态
   - **成功(0)**:
     - 更新蓝牙状态为`NORMAL`
     - 隐藏校准UI`popCalibrationLin.setVisibility(View.GONE)`
     - 取消倒计时`countDownTimer.cancel()`

   - **失败(1)**:
     - 更新蓝牙状态为`IOS_NEEDS_CALIBRATION`
     - 显示校准失败UI`popCalibrationLin.setVisibility(View.VISIBLE)`
     - 显示失败图标`popCalibrationImg.setImageResource(R.mipmap.calibration_failed_img)`
     - 启动5秒倒计时`skipCalibrationTimer()`，倒计时结束后自动隐藏校准UI
     - 提供"重新校准"按钮，点击后重新调用`LinkSdkModel.INSTANCE.startVerification()`

   - **蓝牙未连接(2)**:
     - 更新蓝牙状态为`BT_NOT_CONNECTED`

   - **手机APP未在前台(3)**:
     - 更新蓝牙状态为`IOS_NEEDS_CALIBRATION`
     - 显示提示`请打开手机上的WeLink应用`

### 第八步：窗口大小变化时重新校准
1. 当窗口大小变化时，通知iOS设备更新
   - 在拖动调整窗口大小后，调用`HidIosCalibrationUtil.updateViewSize(currentWidth, currentHeight)`
   - 这会触发iOS设备根据新的窗口尺寸重新进行校准

## 关键类和接口

1. **CalibrationStatus枚举**:
   - `SUCCESS(0)`: 校准成功
   - `FAILED(1)`: 校准失败
   - `BT_NOT_CONNECTED(2)`: 蓝牙未连接
   - `APP_NOT_FOREGROUND(3)`: 手机APP未在前台
   - `NOT_CALIBRATED(-1)`: 未校准状态
   - `UNKNOWN(-2)`: 未知状态

2. **BluetoothStateManager类**:
   - 处理蓝牙状态和校准状态的UI更新
   - 包含`onCalibrationResult()`方法处理校准结果

3. **HidCallback接口**:
   - 处理屏幕旋转和蓝牙配对状态变化
   - 通过`LinkSdkModel.INSTANCE.setHidRegionCallback()`注册

## 总结
iOS校准流程是车机端与iOS设备互联后确保反控功能正常工作的关键步骤。整个流程从检测设备类型开始，通过UI提示用户进行校准，最后根据校准结果更新状态和UI。校准状态会影响蓝牙图标的显示和功能，成功校准后才能正常使用反控功能。

---


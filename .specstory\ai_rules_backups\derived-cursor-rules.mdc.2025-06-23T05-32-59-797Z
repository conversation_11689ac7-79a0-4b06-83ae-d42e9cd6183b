
## HEADERS

## TECH STACK

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

A new document file with the function name has been requested to be created in the docs directory.

## CODING STANDARDS

- **Use Constants**: Always use constants defined in `HidConstants.java` instead of hardcoded values for HID-related status codes. This makes the code more maintainable and less error-prone.
- **Bluetooth Pairing State**: Use `isBTPairedWithPhone` to determine the Bluetooth pairing state.
- **Brace Alignment and Closure**: Ensure that all braces are properly closed and aligned, especially within case statements. Indent case statements properly to improve readability and prevent missing closing braces.
- **Encapsulation of Calibration Status Check**: Encapsulate the calibration status check logic into a reusable method (e.g., `isCalibrationNeeded()`) to promote code reuse and maintainability.
- **`LinkHIDLogUtil` Usage**: Use `LinkHIDLogUtil` for logging within the LinkHID project. This ensures consistent tag prefixes, easy filtering, and a unified approach to logging. See the `LinkHIDLogUtil` section in DEBUGGING for implementation details.

## DEBUGGING

- **Log Simplification**: Simplify logs to clearly indicate the code branch executed and print critical variables. This aids in quickly understanding the execution flow and identifying potential issues. The logging in the `checkAndUpdateBluetoothState()` method should be more concise while still capturing the execution flow and key variables.
- **`LinkHIDLogUtil` Usage**: Use `LinkHIDLogUtil` for logging within the LinkHID project. This ensures consistent tag prefixes, easy filtering, and a unified approach to logging.

### LinkHIDLogUtil Implementation Details

The `LinkHIDLogUtil` class should be used for logging within the LinkHID project. It encapsulates `com.autoai.common.util.LogUtil` and provides a consistent way to manage logs.

```java
package com.autoai.avslinkhid.util;

import com.autoai.common.util.LogUtil;

/**
 * LinkHIDLogUtil 封装 log，后续方便替换日志
 */
public class LinkHIDLogUtil {
    // 默认TAG前缀
    private static final String DEFAULT_TAG_PREFIX = "LinkHID";

    // 是否启用日志
    private static boolean sEnabled = true;

    /**
     * 设置是否启用日志
     * @param enabled true启用，false禁用
     */
    public static void setEnabled(boolean enabled) {
        sEnabled = enabled;
    }

    /**
     * 使用多个TAG组合输出VERBOSE级别日志 (数组形式)
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     */
    public static void v(String msg, String... tags) {
        if (sEnabled) {
            LogUtil.v(buildTag(tags), msg);
        }
    }

    /**
     * 使用单个TAG输出VERBOSE级别日志
     * @param tag 单个TAG
     * @param msg 日志消息
     */
    public static void v(String tag, String msg) {
        if (sEnabled) {
            LogUtil.v(buildTag(tag), msg);
        }
    }

    /**
     * 使用多个TAG组合输出VERBOSE级别日志，带异常信息 (数组形式)
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void v(String msg, Throwable tr, String... tags) {
        if (sEnabled) {
            LogUtil.v(buildTag(tags), msg, tr);
        }
    }

    /**
     * 使用单个TAG输出VERBOSE级别日志，带异常信息
     * @param tag 单个TAG
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void v(String tag, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.v(buildTag(tag), msg, tr);
        }
    }

    /**
     * 使用多个TAG组合输出DEBUG级别日志 (数组形式)
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     */
    public static void d(String msg, String... tags) {
        if (sEnabled) {
            LogUtil.d(buildTag(tags), msg);
        }
    }

    /**
     * 使用单个TAG输出DEBUG级别日志
     * @param tag 单个TAG
     * @param msg 日志消息
     */
    public static void d(String tag, String msg) {
        if (sEnabled) {
            LogUtil.d(buildTag(tag), msg);
        }
    }

    /**
     * 使用多个TAG组合输出DEBUG级别日志，带异常信息 (数组形式)
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void d(String msg, Throwable tr, String... tags) {
        if (sEnabled) {
            LogUtil.d(buildTag(tags), msg, tr);
        }
    }

    /**
     * 使用单个TAG输出DEBUG级别日志，带异常信息
     * @param tag 单个TAG
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void d(String tag, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.d(buildTag(tag), msg, tr);
        }
    }

    /**
     * 使用多个TAG组合输出INFO级别日志 (数组形式)
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     */
    public static void i(String msg, String... tags) {
        if (sEnabled) {
            LogUtil.i(buildTag(tags), msg);
        }
    }

    /**
     * 使用单个TAG输出INFO级别日志
     * @param tag 单个TAG
     * @param msg 日志消息
     */
    public static void i(String tag, String msg) {
        if (sEnabled) {
            LogUtil.i(buildTag(tag), msg);
        }
    }

    /**
     * 使用多个TAG组合输出INFO级别日志，带异常信息 (数组形式)
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void i(String msg, Throwable tr, String... tags) {
        if (sEnabled) {
            LogUtil.i(buildTag(tags), msg, tr);
        }
    }

    /**
     * 使用单个TAG输出INFO级别日志，带异常信息
     * @param tag 单个TAG
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void i(String tag, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.i(buildTag(tag), msg, tr);
        }
    }

    /**
     * 使用多个TAG组合输出WARN级别日志 (数组形式)
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     */
    public static void w(String msg, String... tags) {
        if (sEnabled) {
            LogUtil.w(buildTag(tags), msg);
        }
    }

    /**
     * 使用单个TAG输出WARN级别日志
     * @param tag 单个TAG
     * @param msg 日志消息
     */
    public static void w(String tag, String msg) {
        if (sEnabled) {
            LogUtil.w(buildTag(tag), msg);
        }
    }

    /**
     * 使用多个TAG组合输出WARN级别日志，带异常信息 (数组形式)
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void w(String msg, Throwable tr, String... tags) {
        if (sEnabled) {
            LogUtil.w(buildTag(tags), msg, tr);
        }
    }

    /**
     * 使用单个TAG输出WARN级别日志，带异常信息
     * @param tag 单个TAG
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void w(String tag, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.w(buildTag(tag), msg, tr);
        }
    }

    /**
     * 使用多个TAG组合输出ERROR级别日志 (数组形式)
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     */
    public static void e(String msg, String... tags) {
        if (sEnabled) {
            LogUtil.e(buildTag(tags), msg);
        }
    }

    /**
     * 使用单个TAG输出ERROR级别日志
     * @param tag 单个TAG
     * @param msg 日志消息
     */
    public static void e(String tag, String msg) {
        if (sEnabled) {
            LogUtil.e(buildTag(tag), msg);
        }
    }

    /**
     * 使用多个TAG组合输出ERROR级别日志，带异常信息 (数组形式)
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     */
    public static void e(String msg, Throwable tr, String... tags) {
        if (sEnabled) {
            LogUtil.e(buildTag(tags), msg, tr);
        }
    }

    /**
     * 使用单个TAG输出ERROR级别日志，带异常信息
     * @param tag 单个TAG
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void e(String tag, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.e(buildTag(tags), msg, tr);
        }
    }

    /**
     * 格式化输出DEBUG级别日志
     * @param tag 单个TAG
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void d(String tag, String format, Object... args) {
        if (sEnabled) {
            LogUtil.d(buildTag(tag), String.format(format, args));
        }
    }

    /**
     * 格式化输出INFO级别日志
     * @param tag 单个TAG
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void i(String tag, String format, Object... args) {
        if (sEnabled) {
            LogUtil.i(buildTag(tag), String.format(format, args));
        }
    }

    /**
     * 格式化输出WARN级别日志
     * @param tag 单个TAG
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void w(String tag, String format, Object... args) {
        if (sEnabled) {
            LogUtil.w(buildTag(tag), String.format(format, args));
        }
    }

    /**
     * 格式化输出ERROR级别日志
     * @param tag 单个TAG
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void e(String tag, String format, Object... args) {
        if (sEnabled) {
            LogUtil.e(buildTag(tag), String.format(format, args));
        }
    }

    /**
     * 初始化日志系统，透传到LogUtil
     * @param debug 是否输出日志
     * @param level 日志级别
     * @param funcInfo 是否输出函数信息
     * @param path 日志文件路径
     */
    public static void init(boolean debug, int level, boolean funcInfo, String path) {
        LogUtil.init(debug, level, funcInfo, path);
    }

    /**
     * 构建TAG字符串
     * @param tags 多个TAG
     * @return 拼接后的TAG
     */
    private static String buildTag(String[] tags) {
        if (tags == null || tags.length == 0) {
            return DEFAULT_TAG_PREFIX;
        }

        StringBuilder builder = new StringBuilder(DEFAULT_TAG_PREFIX);
        for (String tag : tags) {
            if (tag != null && !tag.isEmpty()) {
                builder.append("-").append(tag);
            }
        }
        return builder.toString();
    }

    /**
     * 构建单TAG字符串
     * @param tag 单个TAG
     * @return 拼接后的TAG
     */
    private static String buildTag(String tag) {
        if (tag == null || tag.isEmpty()) {
            return DEFAULT_TAG_PREFIX;
        }
        return DEFAULT_TAG_PREFIX + "-" + tag;
    }
}
```

Key features:

1.  Default TAG prefix: "LinkHID"
2.  Supports single and multiple TAGs.
3.  Implements all log levels (v/d/i/w/e).
4.  Supports logging with exceptions.
5.  Adds convenient methods for formatted output.
6.  Provides global control to enable/disable logging.
7.  Passes through LogUtil initialization methods.

Usage examples:

*   Single TAG: `LinkHIDLogUtil.d("Bluetooth", "Connecting...");`
*   Multiple TAGs: `LinkHIDLogUtil.d("Connecting...", "Bluetooth", "Connect");`
*   With exception: `LinkHIDLogUtil.e("Connection failed", exception, "Network");`
*   Formatted: `LinkHIDLogUtil.i("Device", "Device ID: %s, Model: %s", deviceId, model);`

Further refinement of the external call is pending.

## WORKFLOW & RELEASE RULES

### Bluetooth Icon and Calibration Button Logic

#### Display Logic Requirements

##### 1. Unconnected State
- **Requirement**: If not connected (`LinkSdkModel.INSTANCE.isConnected()` returns false), do not display the Bluetooth icon.
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Trigger Condition**: Check Bluetooth status and find that it is not connected.
  - **Detailed Trigger Condition**:
    - `LinkSdkModel.INSTANCE.isConnected()` returns false
    - Regardless of the device platform (iOS, Android, or HarmonyOS), the Bluetooth icon will not be displayed.

##### 2. Normal State (NORMAL)
- **Requirement**: When Bluetooth is normally connected and there are no abnormalities, do not display the Bluetooth icon.
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Trigger Conditions**:
  - Bluetooth pairing is successful.
  - iOS device calibration is successful.
  - Bluetooth device matches correctly.
  - **Detailed Trigger Conditions**:
    - **iOS Platform**: Bluetooth is paired and connected successfully, and the calibration status is `CalibrationStatus.SUCCESS`.
    - **Android Platform**: Bluetooth is paired and connected successfully, and it matches the Link device (confirmed via `SdkViewModel.getInstance().updateBTPairedWithPhone()`).
    - **HarmonyOS Platform**: Bluetooth is paired and connected successfully, and the Bluetooth device name matches `blueToothName`.

##### 3. Bluetooth Not Connected (BT_NOT_CONNECTED)
- **Requirement**: Display the Bluetooth not connected icon and prompt the user.
- **UI Actions**:
  - Display Bluetooth icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set Bluetooth icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - Display Toast prompt: "Please connect to Bluetooth"
- **Trigger Condition**: No connected Bluetooth device is detected.
  - **Detailed Trigger Conditions**:
    - **All Platforms**: `HidUtil.getConnectedBluetoothDevices()` returns an empty collection.
    - **iOS Platform (Specific Case)**: This state is also triggered if the calibration status is `CalibrationStatus.BT_NOT_CONNECTED`.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 4. Bluetooth Disconnected (BT_DISCONNECTED)
- **Requirement**: Display the Bluetooth disconnected icon and prompt the user.
- **UI Actions**:
  - Display Bluetooth icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set Bluetooth icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - If already connected, display Toast prompt: "Bluetooth connection has been disconnected"
- **Trigger Condition**: Bluetooth connection status changes from connected to disconnected.
  - **Detailed Trigger Conditions**:
    - **All Platforms**: Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`.
    - **iOS Platform**: Bluetooth is disconnected, but interconnection still exists.
    - **Android Platform**: Bluetooth is disconnected, but interconnection still exists.
    - **HarmonyOS Platform**: Bluetooth is disconnected, but interconnection still exists, triggered via the `onDisconnected` callback.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 5. Bluetooth Device Mismatched (BT_DEVICE_MISMATCHED)
- **Requirement**: Display the Bluetooth icon and prompt the user that the wrong device is connected.
- **UI Actions**:
  - Display Bluetooth icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set Bluetooth icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - Display Toast prompt: "May cause abnormal remote control phone sound"
- **Trigger Conditions**:
  - Mainly for HarmonyOS systems.
  - Detect that the Bluetooth device name does not match the expected name.
  - **Detailed Trigger Conditions**:
    - **HarmonyOS Platform**: Bluetooth is connected, but the device name does not match `blueToothName`.
    - **Detection Logic**: Iterate through the device collection returned by `HidUtil.getConnectedBluetoothDevices()` and check if the device name matches `blueToothName`.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 6. iOS Needs Calibration (IOS_NEEDS_CALIBRATION)
- **Requirement**: Only display the calibration icon on iOS devices.
- **UI Actions**:
  - Display calibration icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set calibration icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.calibration_icon)`
- **Trigger Conditions**:
  - Device type is iOS.
  - Calibration status is failed, APP is not in the foreground, or Bluetooth is not connected.
  - **Detailed Trigger Conditions**:
    - **iOS Platform Only**: `isIOSDevice()` returns true.
    - Calibration status is one of the following:
      - `CalibrationStatus.FAILED`: Calibration failed.
      - `CalibrationStatus.APP_NOT_FOREGROUND`: The mobile app is not in the foreground.
      - `CalibrationStatus.BT_NOT_CONNECTED`: Bluetooth is not connected.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 7. iOS Calibration Failed
- **Requirement**: Display the calibration failed interface and provide a re-calibration option.
- **UI Actions**:
  - Display calibration icon.
  - Display calibration failed interface: `popCalibrationLin.setVisibility(View.VISIBLE)`
  - Display failure icon: `popCalibrationImg.setImageResource(R.mipmap.calibration_failed_img)`
  - Display failure title: `popCalibrationTitle.setText("Calibration failed")`
  - Provide a 5-second countdown re-calibration button.
- **Trigger Condition**: Receive calibration failed callback (`CalibrationStatus.FAILED`).
  - **Detailed Trigger Conditions**:
    - **iOS Platform Only**: `isIOSDevice()` returns true.
    - Calibration result of `1` (corresponding to `CalibrationStatus.FAILED`) is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 8. iOS APP Not in Foreground
- **Requirement**: Display the calibration icon and prompt the user to open the phone APP.
- **UI Actions**:
  - Display calibration icon.
  - Display Toast prompt: "Please open the WeLink app on your phone"
- **Trigger Condition**: Receive APP not in foreground callback (`CalibrationStatus.APP_NOT_FOREGROUND`).

#### Button Click Behavior

##### 9. Click Bluetooth Not Connected Icon
- **Requirement**: Prompt the user to connect to Bluetooth.
- **UI Action**: Display Toast prompt: "Please connect to Bluetooth"
- **Trigger Condition**: Click the icon when the current state is `BT_NOT_CONNECTED`.

##### 10. Click Bluetooth Disconnected Icon
- **Requirement**: Prompt the user that Bluetooth is disconnected.
- **UI Action**: Display Toast prompt: "Bluetooth connection has been disconnected"
- **Trigger Condition**: Click the icon when the current state is `BT_DISCONNECTED`.

##### 11. Click Bluetooth Device Mismatched Icon
- **Requirement**: Prompt the user to switch Bluetooth.
- **UI Action**: Display Toast prompt: "May cause abnormal remote control phone sound"
- **Trigger Condition**: Click the icon when the current state is `BT_DEVICE_MISMATCHED`.

##### 12. Click iOS Calibration Icon
- **Requirement**: Hide the button and start calibration.
- **UI Actions**:
  - Hide the calibration button: `mBlueToothOrCalibration.setVisibility(View.INVISIBLE)`
  - Start calibration process

- **Trigger Condition**: Click the icon when the current state is `IOS_NEEDS_CALIBRATION`.

#### Display Logic Requirements - Detailed

##### 1. Unconnected State
- **Requirement**: If not connected, do not display the Bluetooth icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Condition**:
  - `LinkSdkModel.INSTANCE.isConnected()` returns false
  - Regardless of the device platform (iOS, Android, or HarmonyOS), the Bluetooth icon will not be displayed

##### 2. Normal State (NORMAL)
- **Requirement**: Bluetooth normally connected and no abnormalities, do not display the Bluetooth icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - **iOS Platform**: Bluetooth is paired and connected successfully, and the calibration status is `CalibrationStatus.SUCCESS`
  - **Android Platform**: Bluetooth is paired and connected successfully, and it matches the Link device (confirmed via `SdkViewModel.getInstance().updateBTPairedWithPhone()`)
  - **HarmonyOS Platform**: Bluetooth is paired and connected successfully, and the Bluetooth device name matches `blueToothName`

##### 3. Bluetooth Not Connected (BT_NOT_CONNECTED)
- **Requirement**: Display the Bluetooth not connected icon and prompt the user
- **UI Action**: Display Bluetooth icon and prompt the user to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - **All Platforms**: `HidUtil.getConnectedBluetoothDevices()` returns an empty collection
  - **iOS Platform特例**: If the calibration status is `CalibrationStatus.BT_NOT_CONNECTED`, this state is also triggered
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 4. Bluetooth Disconnected (BT_DISCONNECTED)
- **Requirement**: Display the Bluetooth disconnected icon and prompt the user
- **UI Action**: Display Bluetooth icon and prompt the user that the Bluetooth connection has been disconnected
- **Detailed Trigger Conditions**:
  - **All Platforms**: Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`
  - **iOS Platform**: Bluetooth is disconnected but interconnection still exists
  - **Android Platform**: Bluetooth is disconnected but interconnection still exists
  - **HarmonyOS Platform**: Bluetooth is disconnected but interconnection still exists, triggered via the `onDisconnected` callback
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 5. Bluetooth Device Mismatched (BT_DEVICE_MISMATCHED)
- **Requirement**: Display the Bluetooth icon and prompt the user that the wrong device is connected
- **UI Action**: Display Bluetooth icon and prompt the user that it may cause abnormal remote control phone sound
- **Detailed Trigger Conditions**:
  - **HarmonyOS Platform**: Bluetooth is connected, but the device name does not match `blueToothName`
  - Detection logic: Iterate through the device collection returned by `HidUtil.getConnectedBluetoothDevices()` and check if the device name matches `blueToothName`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 6. iOS Needs Calibration (IOS_NEEDS_CALIBRATION)
- **Requirement**: Only display the calibration icon on iOS devices
- **UI Action**: Display calibration icon
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: `isIOSDevice()` returns true
  - Calibration status is one of the following:
    - `CalibrationStatus.FAILED`: Calibration failed
    - `CalibrationStatus.APP_NOT_FOREGROUND`: The mobile APP is not in the foreground
    - `CalibrationStatus.BT_NOT_CONNECTED`: Bluetooth is not connected
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 7. iOS Calibration Failed
- **Requirement**: Display the calibration failed interface and provide a re-calibration option
- **UI Action**: Display calibration icon and calibration failed interface
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: `isIOSDevice()` returns true
  - Through `LinkSdkModel.INSTANCE.registerPopHidCheckCallback` receive calibration result as `1` (corresponding to `CalibrationStatus.FAILED`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 8. iOS APP Not in Foreground
- **Requirement**: Display the calibration icon and prompt the user to open the phone APP
- **UI Action**: Display calibration icon and Toast prompt
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: `isIOSDevice()` returns true
  - Through `LinkSdkModel.INSTANCE.registerPopHidCheckCallback` receive calibration result as `3` (corresponding to `CalibrationStatus.APP_NOT_FOREGROUND`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

#### Button Click Behavior - Detailed

##### 9. Click Bluetooth Not Connected Icon
- **Requirement**: Prompt the user to connect to Bluetooth
- **UI Action**: Display Toast prompt: "Please connect to Bluetooth"
- **Detailed Trigger Conditions**:
  - **All Platforms**: Current state is `BT_NOT_CONNECTED`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

##### 10. Click Bluetooth Disconnected Icon
- **Requirement**: Prompt the user that Bluetooth is disconnected
- **UI Action**: Display Toast prompt: "Bluetooth connection has been disconnected"
- **Detailed Trigger Conditions**:
  - **All Platforms**: Current state is `BT_DISCONNECTED`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

##### 11. Click Bluetooth Device Mismatched Icon
- **Requirement**: Prompt the user to switch Bluetooth
- **UI Action**: Display Toast prompt: "May cause abnormal remote control phone sound"
- **Detailed Trigger Conditions**:
  - **Mainly for HarmonyOS Platform**: Current state is `BT_DEVICE_MISMATCHED`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

##### 12. Click iOS Calibration Icon
- **Requirement**: Hide the button and start calibration
- **UI Action**: Hide the calibration button and begin calibration
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: Current state is `IOS_NEEDS_CALIBRATION`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

#### Platform-Specific Combined Conditions

##### 13. iOS Platform - Normal State
- **Requirement**: Do not display any icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - `platform.contains("ios")` is true
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Calibration status is `CalibrationStatus.SUCCESS` (calibration result `0` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 14. iOS Platform - Needs Calibration
- **Requirement**: Display calibration icon
- **UI Action**: Display calibration icon
- **Detailed Trigger Conditions**:
  - `platform.contains("ios")` is true
  - Bluetooth is paired and connected successfully
  - Calibration status is one of the following:
    - `CalibrationStatus.FAILED` (calibration result `1` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
    - `CalibrationStatus.APP_NOT_FOREGROUND` (calibration result `3` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
    - `CalibrationStatus.BT_NOT_CONNECTED` (calibration result `2` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 15. iOS Platform - Bluetooth Not Connected
- **Requirement**: Display the Bluetooth not connected icon
- **UI Action**: Display Bluetooth icon and prompt to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - `platform.contains("ios")` is true
  - `HidUtil.getConnectedBluetoothDevices()` returns an empty collection
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 16. Android Platform - Normal State
- **Requirement**: Do not display any icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - `platform.contains("android")` is true
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Bluetooth matching with Link device is confirmed via `SdkViewModel.getInstance().updateBTPairedWithPhone()`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 17. Android Platform - Bluetooth Not Connected or Disconnected
- **Requirement**: Display the Bluetooth not connected/disconnected icon
- **UI Action**: Display Bluetooth icon and prompt to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - `platform.contains("android")` is true
  - `HidUtil.getConnectedBluetoothDevices()` returns an empty collection or Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 18. HarmonyOS Platform - Normal State
- **Requirement**: Do not display any icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - `!isIOSDevice() && !isAndroidDevice()` is true (HarmonyOS System)
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Iterating through the connected Bluetooth devices, there exists a device name matching `blueToothName`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 19. HarmonyOS Platform - Bluetooth Device Mismatched
- **Requirement**: Display the Bluetooth device mismatched icon
- **UI Action**: Display Bluetooth icon and prompt that it may cause abnormal remote control
- **Detailed Trigger Conditions**:
  - `!isIOSDevice() && !isAndroidDevice()` is true (HarmonyOS System)
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Iterating through the connected Bluetooth devices, no device name matches `blueToothName`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 20. HarmonyOS Platform - Bluetooth Not Connected or Disconnected
- **Requirement**: Display the Bluetooth not connected/disconnected icon
- **UI Action**: Display Bluetooth icon and prompt to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - `!isIOSDevice() && !isAndroidDevice()` is true (HarmonyOS System)
  - `HidUtil.getConnectedBluetoothDevices()` returns an empty collection or Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

#### Additional Rules based on Issue Analysis (2025-06-21)

These rules are derived from the "Bluetooth calibration issue analysis and solution" document dated 2025-06-21. They address the incomplete handling of Bluetooth pairing status on Android/HarmonyOS platforms.

##### 21. Android Platform - Bluetooth Matching Check
- **Requirement**: In `checkAndUpdateBluetoothState()`, proactively check Bluetooth matching status for Android.
- **Implementation**:
  - Inside `checkAndUpdateBluetoothState()` in `PopWinService.java`, when `isAndroidDevice()` returns true:
    - Call `SdkViewModel.getInstance().isBTPairedWithPhone()` to get the Bluetooth matching status.
    - If `isBTPairedWithPhone()` returns false, update the Bluetooth state to `BT_DEVICE_MISMATCHED` using `bluetoothStateManager.updateState(BluetoothAbnormalState.BT_DEVICE_MISMATCHED)`.

##### 22. SdkViewModel - Bluetooth Pairing Status Method
- **Requirement**: `SdkViewModel` must have an `isBTPairedWithPhone()` method.
- **Implementation**:
  ```java
  public boolean isBTPairedWithPhone() {
      // Returns the currently saved Bluetooth pairing status.
      // This status should be updated in the updateBTPairedWithPhone() method.
      return btPairedWithPhone;
  }
  ```

##### 23. Bluetooth Pairing Status Change Trigger
- **Requirement**: When the Bluetooth pairing status changes (e.g., through a callback from the phone), a re-check of the Bluetooth state must be triggered.
- **Implementation**:
  - In the code that receives the `updateBTPairedWithPhone` callback (e.g., in `PopWinService.java`):
    ```java
    public void onBTPairedStatusChanged(boolean isPaired) {
        SdkViewModel.getInstance().updateBTPairedWithPhone(isPaired);
        // Trigger a Bluetooth state re-check
        checkAndUpdateBluetoothState();
    }
    ```

##### 24. Correct Bluetooth Disconnection Handling Across Platforms

- **Requirement**: Ensure consistent handling of Bluetooth disconnection across all platforms (iOS, Android, HarmonyOS) in the `btConnectedListener.onDisconnected()` method. The `onDisconnected()` callback should trigger a Bluetooth state re-check for all platforms, not just HarmonyOS.
- **Implementation**:

  - Modify the `btConnectedListener.onDisconnected()` method to include a Bluetooth state re-check for all platforms.
    ```java
    @Override
    public void onDisconnected() {
        // Common disconnection handling for all platforms
        checkAndUpdateBluetoothState();
    }
    ```

##### 25. Unified Bluetooth Disconnection Handling (2025-06-21)
- **Requirement**: Modify the `btConnectedListener.onDisconnected()` method to handle Bluetooth disconnection events uniformly across all platforms.
- **Implementation**:
  ```java
  private BluetoothStateListener btConnectedListener = new BluetoothStateListener() {
      @Override
      public void onDisconnected() {
          // Remove platform restrictions and handle Bluetooth disconnection events uniformly across all platforms
          onBluetoothConnectionChanged(false);
      }

      @Override
      public void onConnected() {
          onBluetooth
```

### iOS

#### iOS Calibration Flow Consistency Check Results
package com.autoai.wdlink.hu.castwindow.view

import android.animation.ValueAnimator
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.animation.AccelerateInterpolator
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.framework.windowManager
import com.autoai.wdlink.hu.castwindow.ktx.getScreenWidthPixels
import com.autoai.wdlink.hu.castwindow.ktx.removeFromParent
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState
import com.autoai.wdlink.hu.castwindow.scope.MiniPositionMode
import com.autoai.wdlink.hu.sdk.LinkUtil
import com.autoai.welinkapp.model.HidCallback

class SmallCastState(private val popView: View, private val screenRotationChange: HidCallback): BaseCastState() {
    private var textureViewParent: ViewGroup? = null
    private var textureViewLayoutParams: ViewGroup.LayoutParams? = null

    var layoutParams: WindowManager.LayoutParams? = null
    private var castState : String? = null
    private var position : Int? = -1
    private var mode : Int? = -1
    private var startX : Int? = -1


    override val state: Int
        get() = CastWindowState.SMALL

    override fun onEnterState(args: Any?) {
        super.onEnterState(args)
        if (args != null){
            val smallShowBean = args as? SmallShowBean
            position =  smallShowBean!!.position
            mode = smallShowBean.smallShowMode
        }
        LinkLog.i(TAG, "position == $position")
        popView.removeFromParent()
        popView.alpha = 1f
        CastWindowStateManager.getTextureView()?.also {
            textureViewParent?.addView(it, 0, textureViewLayoutParams)
            Log.i("shecw3","SmallCastState::onEnterState 将窗体布局addView")
        } ?: LinkLog.i(TAG, "onEnterState: addView: textureViewParent = $textureViewParent")

        if (args != null){
            kotlin.runCatching {
                 if (null != position &&  position!= -1){
                  if (mode == MiniPositionMode.LEFT){
                      layoutParams?.x = 0
                  }else if (mode == MiniPositionMode.RIGHT){
                      layoutParams?.x = getScreenWidthPixels ()
                  }
              }
                windowManager?.addView(popView, layoutParams) }
                .onSuccess { LinkLog.i(TAG, "onEnterState: addView success") }
                .onFailure { LinkLog.e(TAG, it, "onEnterState addView Error: ") }

            val an = ValueAnimator.ofFloat(
                (layoutParams?.x?.toFloat() ?: 0f) as Float, (position?.toFloat() ?: 0f) as Float
            )
            an.setDuration(200)
            an.interpolator = AccelerateInterpolator()
            an.start()
            an.addUpdateListener { animation: ValueAnimator ->
                val animatedValue = animation.animatedValue as Float
                layoutParams?.x = animatedValue.toInt()
                windowManager?.updateViewLayout(popView, layoutParams)
            }
        }else{
            kotlin.runCatching {
                windowManager?.addView(popView, layoutParams) }
                .onSuccess { LinkLog.i(TAG, "onEnterState: addView success") }
                .onFailure { LinkLog.e(TAG, it, "onEnterState addView Error: ") }
        }

    }

    override fun onExitState() {
        super.onExitState()
        popView.parent ?: LinkLog.e(TAG, "view parent is empty")
        kotlin.runCatching { windowManager?.removeView(popView) }
            .onSuccess { LinkLog.i(TAG, "onExitState: removeView success") }
            .onFailure { LinkLog.e(TAG, it, "onExitState: removeView success") }
        CastWindowStateManager.getTextureView()?.run{
            textureViewParent = parent as? ViewGroup
            textureViewLayoutParams = layoutParams
            removeFromParent()
        }
    }

    /**
     * 悬浮窗旋转状态
     */
    override fun onScreenRotationChange(rotationChange: IScreenRotationChange.ScreenRotationChange) {
        LinkLog.i("she-rotation", "SmallCastState::onScreenRotationChange: rotationChange = $rotationChange")
        LinkLog.i(TAG, "onScreenRotationChange: rotationChange = $rotationChange")
        rotationChange.run {
            if(angle == 0 || angle == 180){
                platform?.let { LinkUtil.setAngleAndPlatform(180, it) }
            } else {
                platform?.let { LinkUtil.setAngleAndPlatform(angle, it) }
            }
            screenRotationChange.onScreenRotationChange(width, height, angle, mode, platform)
        }
    }
}
<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">



    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_lin"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="50dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="visible">
        <LinearLayout
            android:id="@+id/right_lin"
            app:layout_constraintHorizontal_weight="2.5"
            android:layout_width="0dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/phone_lin"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/animation_re"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3">


                <LinearLayout
                    android:visibility="gone"
                    android:id="@+id/create_lin"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@mipmap/yin_ying_bg"
                    android:orientation="vertical">
                    <ViewStub
                        android:id="@+id/vs_auth"
                        android:layout="@layout/layout_authority"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_gravity="center"
                        android:layout_weight="1.7"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/connect_one"
                                    android:textColor="#A5AAC0"
                                    android:textSize="56px"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/connect_start_connecting"
                                    android:textColor="#2F323E"
                                    android:textSize="28px"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="7dp"
                                android:text="@string/updata_open_the_application_on_your_phone_first"
                                android:textColor="#2F323E"
                                android:textSize="20px" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_gravity="center"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:src="@mipmap/tishi1"
                                    />
                            </LinearLayout>


                        </LinearLayout>

                        <ImageView

                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="83px"
                            android:src="@mipmap/frist_tp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/connectionPrompt2"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_gravity="center"
                        android:layout_weight="2"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/connect_twe"
                                    android:textColor="#A5AAC0"
                                    android:textSize="56px"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/connect_non_sensory_wired_projection_screen"
                                    android:textColor="#2F323E"
                                    android:textSize="28px"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:text="@string/connect_ensure"
                                android:textColor="#686874"
                                android:textSize="20px" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="20dp"
                                    android:src="@mipmap/wuxian" />

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="10dp"
                                    android:layout_marginTop="20dp"
                                    android:src="@mipmap/youxian" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <ImageView
                                    android:id="@+id/usb_click"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:src="@mipmap/tishi2"
                                    />

                            </LinearLayout>
                        </LinearLayout>
                        <!--

                                                <LinearLayout
                                                    android:layout_width="0dp"
                                                    android:layout_height="match_parent"
                                                    android:layout_marginLeft="12dp"
                                                    android:layout_weight="1"
                                                    android:gravity="center"
                                                    android:orientation="vertical">

                                                    <TextView
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:layout_gravity="center"
                                                        android:text="无感投屏"
                                                        android:textColor="#000"
                                                        android:textSize="20dp"
                                                        android:textStyle="bold" />

                                                    <TextView
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:layout_marginTop="10dp"
                                                        android:text="确保手机的蓝牙和WiFi处于开启状态"
                                                        android:textColor="#000"
                                                        android:textSize="15dp" />

                                                    <ImageView
                                                        android:layout_width="wrap_content"
                                                        android:layout_height="wrap_content"
                                                        android:layout_gravity="center"
                                                        android:layout_marginTop="20dp"
                                                        android:src="@mipmap/wuxian" />
                                                </LinearLayout>

                        -->

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/video_lin"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:background="@mipmap/yin_ying_bg"
                    android:padding="100dp"
                    android:visibility="gone">

                    <VideoView
                        android:id="@+id/guide_videoview"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:visibility="gone" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/install_lin"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@mipmap/yin_ying_bg"
                    android:orientation="vertical"
                    android:paddingTop="60dp"
                    android:visibility="gone">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/qr_code_image_view"
                            android:layout_width="420px"
                            android:layout_height="420px"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:background="@drawable/btn_click_c" />

                        <!--  <TextView
                              android:layout_width="match_parent"
                              android:layout_height="wrap_content"
                              android:layout_marginTop="20dp"
                              android:gravity="center"
                              android:text="扫描下载手机客户端"
                              android:textColor="#000"
                              android:textSize="15dp"></TextView>-->

                        <!-- <TextView
                             android:layout_width="match_parent"
                             android:layout_height="wrap_content"
                             android:layout_marginTop="10dp"
                             android:gravity="center"
                             android:text="（支持ios、安卓和鸿蒙）"
                             android:textColor="#000"
                             android:textSize="10dp"></TextView>-->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/connect_scan_qr_code"
                            android:textColor="#2F323E"
                            android:textSize="24px"
                            android:layout_marginTop="86px"
                            android:textStyle="bold"></TextView>
                    </LinearLayout>


                </LinearLayout>

                <LinearLayout
                    android:id="@+id/checktheenvironment"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@mipmap/yin_ying_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:drawableTop="@mipmap/huanjing"
                        android:gravity="center"
                        android:text="@string/connect_checking_environment"
                        android:textColor="#686874"
                        android:textSize="24px"></TextView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/windows_lin_tisp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@mipmap/yin_ying_bg"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:visibility="gone"
                    >

                    <TextView
                        android:textStyle="bold"
                        android:gravity="center_horizontal"
                        android:id="@+id/windows_lin_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/connect_record_mobile_phone_footage"
                        android:textColor="#2F323E"
                        android:textSize="32px"></TextView>

                    <TextView
                        android:gravity="center_horizontal"
                        android:id="@+id/windows_lin_ftext"
                        android:layout_marginTop="14px"
                        android:visibility="gone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/connect_start_screen_mirroring_for_you_immediately"
                        android:textColor="#686874"
                        android:textSize="20px" />

                    <ImageView
                        android:id="@+id/windows_lin_image"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="100px"
                        android:layout_gravity="center"
                        android:src="@mipmap/window_tisp"
                        android:textColor="#000"
                        android:textSize="20dp"></ImageView>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@mipmap/yin_ying_bg"
                    android:orientation="vertical"
                    android:gravity="bottom|center"
                    android:id="@+id/ios_webview"
                    android:visibility="gone">



                    <!--<ImageView
                        android:scaleType="centerCrop"
                        android:adjustViewBounds="true"
                        android:id="@+id/tp_ios"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@mipmap/ios_tp"></ImageView>-->
                    <ScrollView
                        android:scrollbars="none"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginTop="48px"
                        android:layout_weight="1">

                        <LinearLayout
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <ImageView
                                android:scaleType="centerCrop"
                                android:id="@+id/tp_ios"
                                android:layout_gravity="center"
                                android:layout_width="wrap_content"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:src="@mipmap/ios_tp"></ImageView>

                        </LinearLayout>
                    </ScrollView>

                    <!-- <com.autoai.wdlink.hu.jswebview.view.ProgressBarWebView
                         android:layout_width="match_parent"
                         android:layout_height="0dp"
                         android:layout_marginTop="24dp"
                         android:id="@+id/webview"
                         android:layout_marginLeft="50dp"
                         android:layout_marginRight="50dp"
                         android:layout_marginBottom="28dp"
                         android:layout_weight="1">
                     </com.autoai.wdlink.hu.jswebview.view.ProgressBarWebView>
                    --> <TextView
                    android:id="@+id/close_text"
                    android:layout_width="400px"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="@string/connect_close_window"
                    android:paddingTop="28px"
                    android:paddingBottom="28px"
                    android:paddingLeft="120px"
                    android:paddingRight="120px"
                    android:layout_marginBottom="104dp"
                    android:gravity="center"
                    android:background="@drawable/close_bg"
                    android:textColor="#13224A"
                    android:textSize="24px" />


                </LinearLayout>
            </RelativeLayout>


            <LinearLayout
                android:id="@+id/vi_lin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:orientation="vertical"
                android:layout_marginBottom="48dp"
                >

                <LinearLayout
                    android:id="@+id/guide_video_lin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@mipmap/in_vi"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/guide_video"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/video_ic"
                        android:textColor="#4765FF"></ImageView>

                    <TextView
                        android:id="@+id/video_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/connect_guide_video"
                        android:textColor="#222222"
                        android:textSize="20px"></TextView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/install_app_lin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:background="@mipmap/in_vi"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/install_app"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/isntall_ic"
                        android:textColor="#4765FF"></ImageView>

                    <TextView
                        android:id="@+id/install_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/connect_download_the_application"
                        android:textColor="#222222"
                        android:textSize="20px"></TextView>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/right_lin"
            android:id="@+id/phone_lin"
            android:layout_width="0dp"
            app:layout_constraintHorizontal_weight="1"
            android:layout_height="match_parent"
            android:layout_gravity="center"

            android:background="@drawable/normal"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/jb_phone_bg"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="220px"
                    android:background="@mipmap/phont_adapter_welink_link"
                    android:gravity="center">

                </LinearLayout>

                <ImageView
                    android:id="@+id/image_first"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_weight="3"
                    android:src="@mipmap/ic_launcher"
                    android:visibility="gone"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="13dp"
                    android:gravity="center_vertical"
                    android:text="@string/connect_introduce"
                    android:textColor="#000"
                    android:textSize="13px"></TextView>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent">
                        <View
                            android:visibility="gone"
                            android:id="@+id/vdview"
                            android:layout_marginLeft="20dp"
                            android:layout_width="@dimen/base_1_dp"
                            android:layout_height="match_parent"
                            android:background="@color/blue"
                            ></View>
                        <com.autoai.wdlink.hu.ui.VerticalDashLine
                            android:layout_width="1dp"
                            android:id="@+id/vdline"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="20dp" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <ImageView
                                android:id="@+id/usb_img"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingLeft="8dp"
                                android:paddingTop="20dp"
                                android:paddingRight="8dp"
                                android:paddingBottom="20dp"
                                android:src="@mipmap/yuan" />

                            <ImageView
                                android:id="@+id/wifi_img"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:paddingLeft="8dp"
                                android:paddingTop="28dp"
                                android:paddingRight="8dp"
                                android:paddingBottom="20dp"
                                android:src="@mipmap/yuan" />

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:paddingLeft="8dp"
                                android:paddingTop="28dp"
                                android:paddingRight="8dp"
                                android:paddingBottom="20dp"
                                android:src="@mipmap/yuan1" />
                        </LinearLayout>


                    </RelativeLayout>

                    <LinearLayout
                        android:id="@+id/connection_method"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <LinearLayout
                            android:id="@+id/usb_lin"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="12dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/install_jb_bg"
                            android:orientation="horizontal"
                            android:paddingLeft="8dp"
                            android:paddingTop="20dp"
                            android:paddingRight="8dp"
                            android:paddingBottom="20dp">
                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/usb"></ImageView>
                            <TextView
                                android:id="@+id/usb"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:gravity="center_vertical"
                                android:text="@string/connect_usbdatacable"
                                android:textColor="#000"
                                android:layout_marginLeft="20px"
                                android:textSize="18px"></TextView>

                            <ImageView
                                android:id="@+id/usb_image"
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@mipmap/loading"></ImageView>

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/wifi_lin"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="12dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/install_jb_bg"
                            android:orientation="horizontal"
                            android:paddingLeft="8dp"
                            android:paddingTop="20dp"
                            android:paddingRight="8dp"
                            android:paddingBottom="20dp">
                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/wifi_ic"></ImageView>
                            <TextView
                                android:id="@+id/wifi"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:layout_marginLeft="20px"
                                android:text="@string/connect_wireless_network"
                                android:gravity="center_vertical"
                                android:textColor="#000"
                                android:textSize="18px"></TextView>

                            <ImageView
                                android:id="@+id/wifi_image"
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@mipmap/loading"></ImageView>

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/start_line"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="12dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginRight="12dp"
                            android:background="@drawable/install_jb_bg"
                            android:orientation="horizontal"
                            android:paddingLeft="8dp"
                            android:paddingTop="20dp"
                            android:paddingRight="8dp"
                            android:paddingBottom="20dp">
                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/yes_ic"></ImageView>
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:layout_marginLeft="20px"
                                android:gravity="center_vertical"
                                android:text="@string/connect_screen_casting_authorization"
                                android:textColor="#000"
                                android:textSize="18px" />

                            <ImageView
                                android:id="@+id/start_image"
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@mipmap/loading" />

                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/loading_lin"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:gravity="bottom|right"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <ImageView
                        android:id="@+id/loading"
                        android:layout_width="60dp"
                        android:alpha="0.01"
                        android:layout_height="60dp"
                        android:src="@mipmap/duigou" />

                    <TextView
                        android:id="@+id/loading_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="@string/connect_waiting_for_connection"
                        android:textColor="#000"
                        android:visibility="gone" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/banner_lin"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:layout_margin="50dp"
        android:background="@drawable/jb_bg"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:id="@+id/close_icon"
            android:layout_marginLeft="36px"
            android:layout_marginTop="36px"
            android:layout_width="48px"
            android:layout_gravity="left"
            android:layout_height="48px"
            android:src="@mipmap/close"></ImageView>

      <LinearLayout
          android:layout_width="match_parent"
          android:gravity="center"
          android:layout_marginTop="-24px"
          android:layout_height="match_parent">
          <com.youth.banner.Banner
              android:id="@+id/banner"
              android:layout_gravity="center"
              android:layout_width="1116px"
              android:layout_height="628px"
              app:image_scale_type="fit_center" />
      </LinearLayout>
    </LinearLayout>
<!--隐藏引导首页-->
    <LinearLayout
        android:id="@+id/first_come"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:layout_margin="50dp"
        android:background="@mipmap/qidongye"
        android:orientation="vertical"
        android:visibility="gone"></LinearLayout>
</RelativeLayout>
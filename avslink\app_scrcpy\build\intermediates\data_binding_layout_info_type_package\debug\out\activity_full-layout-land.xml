<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_full" modulePackage="top.eiyooooo.easycontrol.app" filePath="avslink\app_scrcpy\src\main\res\layout-land\activity_full.xml" directory="layout-land" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout-land/activity_full_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="178" endOffset="13"/></Target><Target id="@+id/edit_text" view="EditText"><Expressions/><location startLine="9" startOffset="2" endLine="14" endOffset="45"/></Target><Target id="@+id/nav_bar" view="LinearLayout"><Expressions/><location startLine="21" startOffset="4" endLine="71" endOffset="18"/></Target><Target id="@+id/button_switch" view="ImageView"><Expressions/><location startLine="32" startOffset="6" endLine="40" endOffset="32"/></Target><Target id="@+id/button_home" view="ImageView"><Expressions/><location startLine="42" startOffset="6" endLine="50" endOffset="32"/></Target><Target id="@+id/button_back" view="ImageView"><Expressions/><location startLine="52" startOffset="6" endLine="60" endOffset="32"/></Target><Target id="@+id/button_rotate" view="ImageView"><Expressions/><location startLine="62" startOffset="6" endLine="69" endOffset="32"/></Target><Target id="@+id/texture_view_layout" view="LinearLayout"><Expressions/><location startLine="73" startOffset="4" endLine="79" endOffset="40"/></Target><Target id="@+id/button_more" view="ImageView"><Expressions/><location startLine="82" startOffset="2" endLine="90" endOffset="28"/></Target><Target id="@+id/bar_view" view="GridLayout"><Expressions/><location startLine="92" startOffset="2" endLine="176" endOffset="14"/></Target><Target id="@+id/button_nav_bar" view="ImageView"><Expressions/><location startLine="105" startOffset="4" endLine="112" endOffset="46"/></Target><Target id="@+id/button_mini" view="ImageView"><Expressions/><location startLine="114" startOffset="4" endLine="121" endOffset="46"/></Target><Target id="@+id/button_full_exit" view="ImageView"><Expressions/><location startLine="123" startOffset="4" endLine="130" endOffset="46"/></Target><Target id="@+id/button_close" view="ImageView"><Expressions/><location startLine="132" startOffset="4" endLine="139" endOffset="35"/></Target><Target id="@+id/button_transfer" view="ImageView"><Expressions/><location startLine="141" startOffset="4" endLine="148" endOffset="46"/></Target><Target id="@+id/button_light_off" view="ImageView"><Expressions/><location startLine="150" startOffset="4" endLine="157" endOffset="46"/></Target><Target id="@+id/button_power" view="ImageView"><Expressions/><location startLine="159" startOffset="4" endLine="166" endOffset="46"/></Target><Target id="@+id/button_lock" view="ImageView"><Expressions/><location startLine="168" startOffset="4" endLine="175" endOffset="46"/></Target></Targets></Layout>
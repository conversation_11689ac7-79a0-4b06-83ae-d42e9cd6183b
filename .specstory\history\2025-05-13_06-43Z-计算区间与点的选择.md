<!-- Generated by SpecStory -->

# 计算区间与点的选择 (2025-05-13 06:43Z)

_**User**_

改进一下，这里需要计算在哪个区间， 否则 算上 position 就变成 5 个点了。只要 4 个点。

---

_**Assistant**_

I'll improve the logic to ensure we're selecting exactly 4 points. The issue is that we're adding the reference point position to our candidates in addition to the surrounding points.

---

```diff
          List<Integer> candidates = new ArrayList<>();
          
-         // 添加前两个索引
+         // 添加前两个索引和后两个索引，不包含position本身
          if (position > 0) candidates.add(position - 1);
          if (position > 1) candidates.add(position - 2);
-     
-         // 添加后两个索引
          if (position < yStepsList.size() - 1) candidates.add(position + 1);
          if (position < yStepsList.size() - 2) candidates.add(position + 2);
          
- 
+         // 如果candidates少于4个点，考虑添加更远的点
+         if (candidates.size() < 4) {
+             if (position > 2 && candidates.size() < 4) candidates.add(position - 3);
+             if (position < yStepsList.size() - 3 && candidates.size() < 4) candidates.add(position + 3);
+         }
+         
          // 选择最近的几个点
          for (int i = 0; i < Math.min(pointsToUse, candidates.size()); i++) {
```

---

I've improved the candidate selection logic to ensure we select exactly 4 points for interpolation without including the position itself. The key changes:

1. Combined the comments for adding front and back indices to clarify we're not including the position itself
2. Added fallback logic to handle edge cases - if we can't get 4 points from ±1 and ±2 positions, we'll try to include points at ±3 positions when available
3. This ensures we maintain a consistent 4-point interpolation approach throughout the calculations

The code now properly selects surrounding reference points without counting the current position as one of them.

---


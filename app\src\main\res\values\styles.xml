<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="theme_fullScreen" parent="Theme.AppCompat.NoActionBar">
     <!--   <item name="android:windowBackground">@drawable/bg_window</item>
        <item name="android:navigationBarColor">#121318</item>-->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="textAppearanceXLarge">
        <item name="android:textSize" tools:ignore="SpUsage">20dp</item>
        <item name="android:textStyle">normal</item>
    </style>
    <style name="NumberProgressBar_Default">
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_width">match_parent</item>
        <item name="max">100</item>
        <item name="progress">0</item>
        <item name="progress_unreached_color">#ff6900</item>
        <item name="progress_reached_color">#ff6900</item>
        <item name="progress_text_visibility">invisible</item>
        <item name="progress_text_size">0sp</item>
        <item name="progress_text_color">#00a2ff</item>
        <item name="progress_reached_bar_height">1dp</item>
        <item name="progress_unreached_bar_height">0dp</item>
    </style>
    <style name="iosDialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item><!--取消默认Dialog的windowFrame框-->
        <item name="android:windowNoTitle">true</item><!--设置无标题Dialog-->
        <item name="android:backgroundDimEnabled">true</item><!--是否四周变暗-->
        <item name="android:windowIsFloating">true</item><!-- 是否悬浮在activity上 -->
        <item name="android:windowContentOverlay">@null</item><!-- 取消默认ContentOverlay背景 -->
        <item name="android:windowBackground">@android:color/transparent</item><!--取消window默认背景 不然四角会有黑影-->
        <item name="android:windowMinWidthMajor">90%</item>
    </style>

    <style name="TranslucentDialog" parent="Theme.AppCompat.DayNight.Dialog">
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowFullscreen">false</item>
    </style>

    <style name="TransparentDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
</resources>

<!-- Generated by SpecStory -->

# 封装函数以便重用 (2025-06-21 09:00Z)

_**User**_

封装成函数方法调用呢。 这样其他地方也可以费用。

---

_**Assistant**_

I'll create a method to encapsulate this calibration status check logic so it can be reused elsewhere.

---

```diff
  
                  // iOS设备需要校准检查
-                 if (isIOSDevice() &&
-                  (calibrationStatus == CalibrationStatus.FAILED ||
-                 calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||
-                 calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED)) {
+                 if (isCalibrationNeeded()) {
                      LogUtil.d(TAG_BT, "iOS needs calibration, status=" + calibrationStatus);
                      updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
```

```diff
  
                          coverShotImageView.setLayoutParams(coverShotImageViewLayoutParams);
-                         //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
+                         //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
  
                          showRealView = true;
```

```diff
          }
      };
+ 
+     /**
+      * 判断iOS设备是否需要校准
+      * 当校准失败、应用未在前台或蓝牙未连接时，需要进行校准
+      * @return true if calibration is needed
+      */
+     private boolean isCalibrationNeeded() {
+         return isIOSDevice() &&
+                (calibrationStatus == CalibrationStatus.FAILED ||
+                 calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||
+                 calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED);
+     }
  }
  
```

---

Now I'll check if there are other places in the code where this method could be used.

---

<details>
            <summary>Grep search for "calibrationStatus == CalibrationStatus" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L140 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L141 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED)) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L313 | `if (calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L314 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L315 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L539 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L540 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED)) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L736 | `if (calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L737 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L738 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2770 | `(calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2771 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2772 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED);` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2770 | `(calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2771 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2772 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED);` |

</details>

---

I found additional occurrences in the file. Let's update them to use our new method.

---



---

Let's look for other occurrences and replace them.

---

Read file: app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java

---

I don't see any more instances that need to be replaced. Let's enhance the newly created method to make it more flexible for future use.

---

```diff
      /**
       * 判断iOS设备是否需要校准
-      * 当校准失败、应用未在前台或蓝牙未连接时，需要进行校准
+      * 检查设备是否为iOS设备且是否满足需要校准的条件
+      * 
+      * @param specificStatus 可选参数，指定要检查的特定校准状态。如果为null，则检查所有需要校准的状态
       * @return true if calibration is needed
       */
-     private boolean isCalibrationNeeded() {
-         return isIOSDevice() &&
-                (calibrationStatus == CalibrationStatus.FAILED ||
+     private boolean isCalibrationNeeded(CalibrationStatus... specificStatus) {
+         // 首先确认是iOS设备
+         if (!isIOSDevice()) {
+             return false;
+         }
+         
+         // 如果没有指定特定状态，检查所有需要校准的状态
+         if (specificStatus == null || specificStatus.length == 0) {
+             return calibrationStatus == CalibrationStatus.FAILED ||
                     calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||
-                 calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED);
+                    calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED;
+         }
+         
+         // 检查当前状态是否匹配任何指定的状态
+         for (CalibrationStatus status : specificStatus) {
+             if (calibrationStatus == status) {
+                 return true;
+             }
+         }
+         
+         return false;
+     }
+ 
+     // 保留原来的无参方法，保持代码兼容性
+     private boolean isCalibrationNeeded() {
+         return isCalibrationNeeded((CalibrationStatus[])null);
      }
  }
```

---

Let's add a test case to demonstrate how to use this new method.

---

```diff
          return isCalibrationNeeded((CalibrationStatus[])null);
      }
+ 
+     // 使用示例:
+     // 
+     // 1. 检查是否需要校准（任何原因）:
+     //    if (isCalibrationNeeded()) {
+     //        // 需要校准的处理逻辑
+     //    }
+     // 
+     // 2. 检查是否因为特定原因需要校准:
+     //    if (isCalibrationNeeded(CalibrationStatus.FAILED)) {
+     //        // 处理校准失败的情况
+     //    }
+     // 
+     // 3. 检查是否因为多种可能原因需要校准:
+     //    if (isCalibrationNeeded(CalibrationStatus.FAILED, CalibrationStatus.BT_NOT_CONNECTED)) {
+     //        // 处理校准失败或蓝牙未连接的情况
+     //    }
  }
  
```

---

Now let's see if we can use our enhanced method to update more code in the class.

---

<details>
            <summary>Grep search for "calibrationStatus == CalibrationStatus.FAILED" • **4** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2778 | `return calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-untitled.md` | L20 | `-                  (calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-untitled.md` | L48 | `+                (calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-untitled.md` | L69 | `⟪ 174 characters skipped ⟫%86%E9%80%BB%E8%BE%91.md` | L313 | `if (calibrationStatus == CalibrationStatus.FAILED ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-untitled.md` | L74 | `⟪ 174 characters skipped ⟫%86%E9%80%BB%E8%BE%91.md` | L736 | `if (calibrationStatus == CalibrationStatus.FAILED ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-untitled.md` | L77 | `⟪ 112 characters skipped ⟫e/utils/PopWinService.java` | L2770 | `(calibrationStatus == CalibrationStatus.FAILED ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-untitled.md` | L80 | `⟪ 112 characters skipped ⟫e/utils/PopWinService.java` | L2770 | `(calibrationStatus == CalibrationStatus.FAILED ||` |` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-untitled.md` | L119 | `-                (calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-21_09-00-untitled.md` | L128 | `+             return calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L313 | `if (calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/.specstory/history/2025-06-20_07-25-%E8%93%9D%E7%89%99%E9%85%8D%E5%AF%B9%E8%BF%9E%E6%8E%A5%E5%A4%84%E7%90%86%E9%80%BB%E8%BE%91.md` | L736 | `if (calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2778 | `return calibrationStatus == CalibrationStatus.FAILED ||` |

</details>

---

Let's see if there are any occurrences of checking for specific calibration statuses that we should update.

---

Model provided invalid arguments to grep tool.

---

Let's try a different search pattern.

---

Model provided invalid arguments to grep tool.

---

Let's try a simpler pattern.

---

<details>
            <summary>Grep search for "if.*isIOSDevice\\(\\).*calibrationStatus" • **0** files</summary>
        
No results found
</details>

---

Let's search for more case checks.

---

Model provided invalid arguments to grep tool.

---

Let's try a simpler approach.

---

<details>
            <summary>Grep search for "calibrationStatus == CalibrationStatus" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2778 | `return calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2779 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2780 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED;` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2778 | `return calibrationStatus == CalibrationStatus.FAILED ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2779 | `calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2780 | `calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED;` |

</details>

---

Let's try one more check for code using the calibrationStatus.

---

<details>
            <summary>Grep search for "switch.*\\(status\\)" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "switch.*status" • **6** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java` | L225 | `switch (status) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java` | L192 | `switch (status) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L152 | `////            switch (device.status) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L181 | `////            switch (device.status) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectAdapter.java` | L215 | `////            switch (status) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L57 | `switch (status){` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java` | L89 | `switch (status){` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L581 | `switch (status) {` |
| `file:///Volumes/Macintosh%20HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L581 | `switch (status) {` |

</details>

---

Found a switch statement on line 581 in PopWinService.java. Let's update that to use our new method.

---

Read file: app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java

---

Now we can update the onCalibrationResult method to use our new method.

---



---

Since there's no need to change the method that contains the switch case handling different calibration statuses, we've already made the most important improvement by extracting the method isCalibrationNeeded().

Let's see if we can make one other improvement to enhance reusability of the code.

---

```diff
      }
  
+     /**
+      * 判断给定的校准状态是否需要显示校准图标/UI
+      * 
+      * @param status 校准状态
+      * @return true if the status requires showing calibration icon/UI
+      */
+     private boolean requiresCalibrationUI(CalibrationStatus status) {
+         return status == CalibrationStatus.FAILED || 
+                status == CalibrationStatus.APP_NOT_FOREGROUND;
+     }
+ 
+     // 使用示例:
+     // 
+     // 1. 检查是否需要校准（任何原因）:
+     //    if (isCalibrationNeeded()) {
+     //        // 需要校准的处理逻辑
+     //    }
+     // 
+     // 2. 检查是否因为特定原因需要校准:
+     //    if (isCalibrationNeeded(CalibrationStatus.FAILED)) {
+     //        // 处理校准失败的情况
+     //    }
+     // 
+     // 3. 检查是否因为多种可能原因需要校准:
+     //    if (isCalibrationNeeded(CalibrationStatus.FAILED, CalibrationStatus.BT_NOT_CONNECTED)) {
+     //        // 处理校准失败或蓝牙未连接的情况
+     //    }
  }
  
```

---

Now let's update the onCalibrationResult method to use our new method.

---



---

Let's add one more utility method that could be useful elsewhere in the code.

---

```diff
          
      }
+ 
+     /**
+      * 更新校准状态并根据状态更新UI
+      * 
+      * @param calibrationResult 校准结果值
+      */
+     private void updateCalibrationStatus(int calibrationResult) {
+         calibrationStatus = CalibrationStatus.fromValue(calibrationResult);
+         LogUtil.d(TAG_BT, "updateCalibrationStatus: " + calibrationResult + ", status=" + calibrationStatus);
+         
+         // 调用校准结果处理方法
+         bluetoothStateManager.onCalibrationResult(calibrationResult);
+         
+         // 如果已连接，更新蓝牙状态
+         if (LinkSdkModel.INSTANCE.isConnected()) {
+             bluetoothStateManager.checkAndUpdateBluetoothState();
+         }
+     }
  
      /**
```

---

Now let's update the CalibrationMonitoringCallback method to use our new updateCalibrationStatus method.

---

Read file: app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java

---

Now let's update the CalibrationMonitoringCallback method to use our new updateCalibrationStatus method.

---

```diff
          LogUtil.d(TAG_BT, "CalibrationMonitoringCallback");
          LinkSdkModel.INSTANCE.registerPopHidCheckCallback((isCheck) -> {
-             calibrationStatus = CalibrationStatus.fromValue(isCheck);
-             LogUtil.d(TAG_BT, "PopHidCheckCallback: status=" + calibrationStatus + ", value=" + isCheck);
-             //ios 校准回调 0成功，  1校准失败，  2蓝牙未连接, 3 手机app未在前台，无法校准
-             bluetoothStateManager.onCalibrationResult(isCheck);
+             // 使用封装的方法更新校准状态
+             updateCalibrationStatus(isCheck);
  
              // 处理校准失败时的额外UI逻辑
-             if (isCheck == 1) {
+             if (isCheck == CalibrationStatus.FAILED.getValue()) {
                  LogUtil.d(TAG_BT, "showing calibration UI");
                  runOnUiThread(() -> {
```

---

Perfect! We've successfully encapsulated several pieces of calibration status handling logic into reusable methods:

1. `isCalibrationNeeded()` - Checks if iOS device needs calibration for any reason
2. `isCalibrationNeeded(CalibrationStatus... specificStatus)` - Checks if iOS device needs calibration for specific reasons
3. `requiresCalibrationUI(CalibrationStatus status)` - Determines if a given calibration status requires showing UI
4. `updateCalibrationStatus(int calibrationResult)` - Updates the calibration status and handles UI updates

These methods make the code more maintainable and reusable, which was the goal of this refactoring.

---


<!-- Generated by SpecStory -->

# 检查待提交代码的错误 (2025-07-01 07:37Z)

_**User**_

j检查一下待提交的代码， 有没有明显的错误和问题。请仔细分析，一行一行分析，然后整理。等待确认。

---

_**Assistant**_

我将检查待提交的代码中可能存在的错误和问题。

---

<details>
            <summary>Grep search for "modified:   app/build.gradle" • **0** files</summary>
        
No results found
</details>

---

让我查看具体的修改内容：

---

```bash
git diff app/build.gradle
```

```
diff --git a/app/build.gradle b/app/build.gradle
index 67fa20c7..bfcdfb1f 100644
--- a/app/build.gradle
+++ b/app/build.gradle
@@ -4,6 +4,7 @@ plugins {
 
     id 'kotlinx-serialization'
 }
+
 android {
     compileSdkVersion rootProject.ext.android.compileSdk
 
@@ -146,14 +147,22 @@ android {
             //manifest资源文件
             manifest.srcFile 'src/product_customized/_24L/AndroidManifest.xml'
         }
+    }
 
+    sourceSets.configureEach {
+        if (name == "main") {
+            java.srcDir("src/main/java")
+            java.srcDirs += "/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55
haiwai/link-hid/avslinkhid/s
rc/main/java"
                            +        }
     }
 
+
 }
 
 dependencies {
     //noinspection GradlePath
 //    implementation files('/libs/dsv-extlink.jar')
+    implementation("androidx.annotation:annotation:1.7.1")
     //lib
     if (file("../avslink/linkHost").exists()  && SDK_TEST != "true") {
         implementation( project(path: ':linkHost')){
@@ -174,7 +183,7 @@ dependencies {
         exclude group: 'androidx.core', module: 'core-ktx'
 
     }
-    implementation "com.autoai.avs.link:linkhid:${LINK_HID_VERSION}"
+    //implementation "com.autoai.avs.link:linkhid:${LINK_HID_VERSION}"
     //24L车机双屏使用updatePadding()方法适配DP席
     implementation 'androidx.core:core-ktx:1.9.0'
 //    implementation 'com.ashokvarma.android:bottom-navigation-bar:1.4.1'

```

---

```bash
git diff app/src/main/java/com/autoai/car/os_adapter/proxy/CarInfoProxy.kt
```

```
sun@sundeMac-mini /V/M/U/s/W/L/a55haiwai (mona-dev)> git diff app/src/main/java/com/autoai/car/os_adapter/pr
oxy/CarInfoProxy.kt
                    


diff --git a/app/src/main/java/com/autoai/car/os_adapter/proxy/CarInfoProxy.kt b/app/src/main/java/com/autoa
i/car/os_adapter/proxy/CarInfoProxy.kt
index dd8c99b0..840dd0fb 100644
--- a/app/src/main/java/com/autoai/car/os_adapter/proxy/CarInfoProxy.kt
+++ b/app/src/main/java/com/autoai/car/os_adapter/proxy/CarInfoProxy.kt
@@ -1,8 +1,6 @@
 package com.autoai.car.os_adapter.proxy
 
 import android.content.Context
-import android.util.Log
-import com.autoai.car.os_adapter.imp.CarInfoImp
 import com.autoai.car.os_adapter.interfaces.CarInfoInterface
 
 
@@ -14,7 +12,7 @@ object CarInfoProxy : CarInfoInterface {
      * 获取当前行车档位
      */
     override fun init(context: Context) {
-        CarInfoImp.getInstance().init(context)
+       // CarInfoImp.getInstance().init(context)
     }
 
 
@@ -22,26 +20,28 @@ object CarInfoProxy : CarInfoInterface {
      * 获取当前行车档位
      */
     override fun getCurDriveState(): Int {
-        return CarInfoImp.getInstance().getCurDriveState()
+        //return CarInfoImp.getInstance().getCurDriveState()
+        return -1
     }
 
     /**
      * 获取当前行车档位
      */
     override fun getCurGearValue(): Int {
-        return CarInfoImp.getInstance().getCurGearValue()
+       // return CarInfoImp.getInstance().getCurGearValue()
+        return -1
     }
 
     override fun onCheckIsPlayVideoResult(isPlay:Int){
-        return CarInfoImp.getInstance().onCheckIsPlayVideoResult(isPlay)
+        //return CarInfoImp.getInstance().onCheckIsPlayVideoResult(isPlay)
     }
 
     /**
      * 获取当前行车档位
      */
     override fun addVehicleGearChangeListener(driveStateChangedListener: CarInfoInterface.DriveStateChanged
Listener) {
-        CarInfoImp.getInstance()
-            .addVehicleGearChangeListener(driveStateChangedListener)
+//        CarInfoImp.getInstance()
+//            .addVehicleGearChangeListener(driveStateChangedListener)
     }
 
 
@@ -49,7 +49,7 @@ object CarInfoProxy : CarInfoInterface {
      * 移除档位监听
      */
     override fun removeVehicleGearChangeListener(driveStateChangedListener: CarInfoInterface.DriveStateChan
gedListener) {
-        CarInfoImp.getInstance()
-            .removeVehicleGearChangeListener(driveStateChangedListener)
+//        CarInfoImp.getInstance()
+//            .removeVehicleGearChangeListener(driveStateChangedListener)
     }
 }
sun@sundeMac-mini /V/M/U/s/W/L/a55haiwai (mona-dev)>
```

---

```bash
git diff app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java
```

```
sun@sundeMac-mini /V/M/U/s/W/L/a55haiwai (mona-dev)> git diff app/src/main/java/com/autoai/wdlink/hu/frame/u
tils/PopWinService.java
                        


diff --git a/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java b/app/src/main/java/com/a
utoai/wdlink/hu/frame/utils/PopWinService.java
index 173438f6..195b96eb 100644
--- a/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java
+++ b/app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java
@@ -505,7 +505,7 @@ public class PopWinService extends Service implements HidCallback {
             }
 
             Set<BluetoothDevice> deviceSet = HidUtil.getConnectedBluetoothDevices();
-            LogUtil.i(TAG_BT, "BT devices count=" + deviceSet.size());
+            LogUtil.i(TAG_BT, "BT devices count=" + deviceSet.size() + " platform: " + platform  + " calibr
ationStatus: " + calibrationStatus);
 
             if (deviceSet.isEmpty()) {
                 // 没有连接的蓝牙设备
sun@sundeMac-mini /V/M/U/s/W/L/a55haiwai (mona-dev)>
```

---

```bash
git diff --stat avslink
```

```
sun@sundeMac-mini /V/M/U/s/W/L/a55haiwai (mona-dev)> git diff --stat avslink
 avslink | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)
sun@sundeMac-mini /V/M/U/s/W/L/a55haiwai (mona-dev)>
```

---

```bash
git diff avslink
```

```
sun@sundeMac-mini /V/M/U/s/W/L/a55haiwai (mona-dev)> git diff avslink
diff --git a/avslink b/avslink
index 3e6720a5..028d9d7e 160000
--- a/avslink
+++ b/avslink
@@ -1 +1 @@
-Subproject commit 3e6720a5614223c4aa3e0fd9f7146d37f36028c6
+Subproject commit 028d9d7e53b6c92db7978bb1dcaf75578c994691-dirty
sun@sundeMac-mini /V/M/U/s/W/L/a55haiwai (mona-dev)>
```

---

基于代码审查，我找到了以下潜在问题：

## 1. app/build.gradle 文件问题

1. **路径格式错误**：
   ```gradle
   java.srcDirs += "/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/link-hid/avslinkhid/src/main/java"
   ```
   - 绝对路径包含空格，且行被意外截断
   - 应使用正确的转义或双引号包裹路径
   - 使用绝对路径不利于项目的可移植性

2. **注释掉但未替换的依赖**：
   ```gradle
   //implementation "com.autoai.avs.link:linkhid:${LINK_HID_VERSION}"
   ```
   - 注释掉依赖可能导致功能缺失
   - 需要确认是否有替代方案或是否真的不需要该依赖

3. **缩进和格式问题**：
   - 有些代码块缩进不一致
   - 存在多余的空行

## 2. CarInfoProxy.kt 文件问题

1. **功能被注释/空实现**：
   - 几乎所有方法的实现都被注释掉
   - 被注释掉的方法替换为返回 `-1` 或空实现
   - 这可能导致调用这些方法的代码无法正常工作

2. **移除了必要的导入**：
   ```kotlin
   import android.util.Log
   import com.autoai.car.os_adapter.imp.CarInfoImp
   ```
   - 这些导入被移除，但CarInfoImp类的引用被注释而非删除
   - 如果以后需要恢复功能，可能需要重新添加这些导入

3. **缺乏替代实现**：
   - 注释掉的代码没有提供替代实现
   - 例如，`onCheckIsPlayVideoResult` 方法被注释但没有替代实现

## 3. PopWinService.java 文件问题

1. **日志输出修改**：
   ```java
   LogUtil.i(TAG_BT, "BT devices count=" + deviceSet.size() + " platform: " + platform  + " calibrationStatus: " + calibrationStatus);
   ```
   - 日志输出行过长，可能导致日志不易读
   - 日志中添加了更多信息（platform和calibrationStatus）

## 4. avslink 子模块问题

1. **子模块状态**：
   - 子模块引用从 `3e6720a5` 更改为 `028d9d7e-dirty`
   - 标记为 "dirty" 表示该子模块中有未提交的更改
   - 在提交主项目前，应先提交子模块中的更改

## 总结建议

1. **修复 build.gradle 中的路径问题**：
   - 使用正确的路径格式，避免使用绝对路径
   - 考虑使用项目相对路径以提高可移植性

2. **审查 CarInfoProxy.kt 的功能降级**：
   - 确认是否有意禁用这些功能
   - 如果确实需要禁用，考虑提供合适的替代实现或错误处理

3. **处理子模块的未提交更改**：
   - 在提交主项目前，先处理和提交 avslink 子模块中的更改
   - 确保子模块处于干净状态

4. **测试影响**：
   - 测试这些更改对应用功能的影响，特别是 CarInfoProxy 的更改
   - 确保没有引入运行时错误或功能损失

这些问题可能会影响应用的稳定性和功能。在提交前应该解决这些问题，以确保代码质量和应用功能。

---

_**User**_

你分析了那几个文件。 是否都分析了？

---

_**Assistant**_

我分析了所有被标记为修改的文件，包括：

1. app/build.gradle
2. app/src/main/java/com/autoai/car/os_adapter/proxy/CarInfoProxy.kt
3. app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java
4. avslink 子模块的变更

git status 显示这些就是当前待提交的变更文件。除了这些文件外，还有一些未跟踪的文件（如 .cursor/、docs/、link-hid/ 等），但这些未跟踪的文件不是当前待提交的内容，因此我没有分析它们。

我是否需要深入检查其他文件或特定方面？

---

_**User**_

avslink 子模块里面的代码加查了么？

---

_**Assistant**_

我还没有查看 avslink 子模块中的具体代码变更。我只看到了子模块的引用提交 ID 发生了变化，且标记为 "dirty"。让我检查一下 avslink 子模块的具体代码变更。

---

```bash
cd avslink && git status
```

```
sun@sundeMac-mini /V/M/U/s/W/L/a55haiwai (mona-dev)> cd avslink && git status
On branch dev
Your branch is up to date with 'origin/dev'.

Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
        modified:   linkHost/build.gradle
        modified:   linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java
        modified:   linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java
        modified:   linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java
        modified:   linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java
        modified:   linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java
        modified:   linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java
        modified:   linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java
        modified:   linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java
        modified:   localLibs/wlhardware/build.gradle

Untracked files:
  (use "git add <file>..." to include in what will be committed)
        app_scrcpy/build/
        wlhardware/

no changes added to commit (use "git add" and/or "git commit -a")
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)>
```

---

让我查看一下这些文件的具体修改：

---

```bash
git diff linkHost/build.gradle
```

```
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)> git diff linkHost/build.gradle
diff --git a/linkHost/build.gradle b/linkHost/build.gradle
index 0d4e102..0321064 100644
--- a/linkHost/build.gradle
+++ b/linkHost/build.gradle
@@ -47,19 +47,23 @@ android {
         jvmTarget = '1.8'
     }
 
-//    sourceSets.configureEach {
-//        if (name == "main") {
-//            java.srcDir("src/main/java")
-//            java.srcDirs += "C:\\work_space\\projects\\avslinkhid\\link-hid\\avslinkhid\\src\\main\\java"
-//        }
-//    }
+    sourceSets.configureEach {
+        if (name == "main") {
+            java.srcDir("src/main/java")
+            java.srcDirs += "/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/link-hid/avslinkhid/s
rc/main/java"
+        }
+    }
 }
 
 dependencies {
     api project(path: ':linkSdk')
     api project(':linkCommon')
     implementation 'androidx.appcompat:appcompat:1.2.0'
-    api "com.autoai.avs.link:linkhid:${LINK_HID_VERSION}"
+//    if (file("../../link-hid/avslinkhid").exists()  && SDK_TEST != "true") {
+//        implementation project(path: ':avslinkhid')
+//    } else {
+//        implementation "com.autoai.avs.link:linkhid:${LINK_HID_VERSION}"
+//    }
 
     api project(path: ':app-scrcpy')
 
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)>
```

---

```bash
git diff linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java
```

```
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)> git diff linkHost/src/main/java/com/autoai/welinkapp/checkco
nnect/AOACheckDevice.java
                          


diff --git a/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java b/linkHost/src/mai
n/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java
index e0b1a47..cb2b19b 100644
--- a/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java
+++ b/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java
@@ -28,6 +28,11 @@ import java.util.HashMap;
 public class AOACheckDevice {
     private static final String TAG = "WL_DRIVER";
 
+    /**
+     * 所有 AOA 相关的操作日志
+     */
+    private static final String TAG_AOA = ".WL_AOA";
+
     @SuppressLint("StaticFieldLeak")
     private static AOACheckDevice mInstance = null;
     private Context mContext = null;
@@ -76,12 +81,14 @@ public class AOACheckDevice {
     }
 
     public void init(Context context) {
-        LogUtil.d(TAG, "AOACheckDevice::init --> AOA CheckDevice init");
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::init start");
         mContext = context;
         mUsbManager = (UsbManager) mContext.getSystemService(Context.USB_SERVICE);
 
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::init SystemUsbReceiver register");
         mSystemUsbReceiver = new SystemUsbReceiver(mContext);
         mSystemUsbReceiver.registerReceiver();
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::init complete");
     }
 
     /**
@@ -90,18 +97,24 @@ public class AOACheckDevice {
      * @param responder
      */
     public void registerUiResponder(UIResponder responder) {
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::registerUiResponder");
         this.uiResponder = responder;
     }
+
     public void deinit() {
-        LogUtil.d(TAG, "AOA CheckDevice deinit");
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinit start");
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinit USB device deinit");
         deinitUsbDevice();
         iUsbFd = 0;
 
         if (null != mSystemUsbReceiver) {
+            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinit SystemUsbReceiver unregister");
             mSystemUsbReceiver.unregisterReceiver();
             mSystemUsbReceiver = null;
         }
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinit AoaLink deinit");
         AoaLink.deinit();
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinit complete");
     }
 
     private boolean filterDevice(UsbDevice device) {
@@ -162,13 +175,14 @@ public class AOACheckDevice {
      * @return
      */
     public boolean checkDevices() {
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::checkDevices start");
         if (mContext == null || mUsbManager == null) {
-            LogUtil.e(TAG, "AOACheckDevice::checkDevices fail");
+            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::checkDevices context or usbManager is null");
             return false;
         }
 
         if (!CommonData.isCheckingStart) {
-            LogUtil.i(TAG, "AOACheckDevice::Checking is not start, don't check usb device !!");
+            LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::checkDevices checking not started");
             CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_AOA;
             return false;
         }
@@ -178,19 +192,19 @@ public class AOACheckDevice {
                 && CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_ANDROID
                     && CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_IOS
                     */
-            LogUtil.i(TAG, "AOACheckDevice::is connected now.");
+            LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::checkDevices already connected, status=" + CommonData
.iCurrentConnectStatus);
             CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_AOA;
             return false;
         }
 
         HashMap<String, UsbDevice> deviceList = mUsbManager.getDeviceList();
-        LogUtil.i(TAG, "AOACheckDevice:: device count = " + deviceList.size());
+        LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::checkDevices device count=" + deviceList.size());
         for (UsbDevice device : deviceList.values()) {
             if (device == null) {
                 continue;
             }
             if (filterDevice(device)) {
-                LogUtil.d(TAG, "AOACheckDevice:: ignore other device");
+                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::checkDevices ignore filtered device");
                 continue;
             }
             //iCurrentConnectType 5 无感互联状态，usb互联拦截
@@ -204,13 +218,13 @@ public class AOACheckDevice {
                 return false;
             }*/
             if (mUsbManager.hasPermission(device)) {
-                LogUtil.d(TAG, "AOACheckDevice::initUsbDevice -->device has Permission");
+                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::checkDevices device has permission, init start");
                 if (initUsbDevice(device)) {
-                    LogUtil.d(TAG, "AOACheckDevice::initUsbDevice -->init device success");
+                    LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::checkDevices init device success");
                     return true;
                 }
             } else {
-                LogUtil.d(TAG, "AOACheckDevice::initUsbDevice -->device requestPermission");
+                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::checkDevices device request permission");
                 PendingIntent mPendingIntent = PendingIntent.getBroadcast(mContext, 0,
                         new Intent(SystemUsbReceiver.ACTION_USB_PERMISSION),
                         Build.VERSION.SDK_INT > Build.VERSION_CODES.R ? PendingIntent.FLAG_IMMUTABLE :
@@ -222,32 +236,38 @@ public class AOACheckDevice {
     }
 
     public boolean initUsbDevice(UsbDevice device) {
-        LogUtil.i(TAG,"AOACheckDevice::initUsbDevice --> device = " + device);
+        LogUtil.i(TAG + TAG_AOA,"AOACheckDevice::initUsbDevice start, device=" + device);
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice deinit previous device");
         deinitUsbDevice();
         if (device == null || mUsbManager == null) {
-            LogUtil.e(TAG, "initUsbDevice fail1");
+            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice device or usbManager is null");
             return false;
         }
         mUsbDevice = device;
         try {
+            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice setup USB connection");
             mUsbInterface = mUsbDevice.getInterface(0);
             mUsbDeviceConnection = mUsbManager.openDevice(mUsbDevice);
             mUsbDeviceConnection.claimInterface(mUsbInterface, true);
             //注册hid
+            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice register HID connection");
             HidUtil.setUsbDeviceConnection(mUsbDeviceConnection);
         } catch (Exception e) {
-            LogUtil.e(TAG, "AOACheckDevice::initUsbDevice --> fail2");
+            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice setup connection fail: " + e.getMessage
());
             deinitUsbDevice();
             return false;
         }
        if (isAOADevice(mUsbDevice)) {
-            LogUtil.i(TAG, "AOACheckDevice::initUsbDevice -->device is in AOA mode");
+            LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice device already in AOA mode");
+            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice ---> send CONNECT_STATUS_IN");
             CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_IN, CommonData.DEVICE_TYPE_AOA, null);
             return true;
         } else if (!switchToAOA()) {
+            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice switch to AOA fail");
             deinitUsbDevice();
             return false;
         }
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice complete");
         return true;
     }
 
@@ -262,38 +282,45 @@ public class AOACheckDevice {
         }*/
     }
     public void deinitUsbDevice() {
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinitUsbDevice start");
         try {
             iUsbFd = 0;
             if (mUsbDeviceConnection != null) {
+                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinitUsbDevice release interface and close conne
ction");
                 mUsbDeviceConnection.releaseInterface(mUsbInterface);
                 mUsbDeviceConnection.close();
             }
 
             mUsbDevice = null;
             mUsbDeviceConnection = null;
+            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinitUsbDevice complete");
         } catch (Exception ex) {
-            LogUtil.e(TAG, "deinitUsbDevice fail");
+            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::deinitUsbDevice fail: " + ex.getMessage());
         }
     }
 
     private boolean isAOADevice(UsbDevice device) {
         if (device == null) {
+            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::isAOADevice device is null");
             return false;
         }
         int vid = device.getVendorId();
         int pid = device.getProductId();
-        return (vid == VID_ACCESSORY && (pid >> 8 & 0x000000ff) == PID_ACCESSORY && (pid & 0x000f) <= 5);
+        boolean isAOA = (vid == VID_ACCESSORY && (pid >> 8 & 0x000000ff) == PID_ACCESSORY && (pid & 0x000f)
 <= 5);
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isAOADevice vid=" + vid + ", pid=" + pid + ", isAOA=" + i
sAOA);
+        return isAOA;
     }
 
     private void resetUsbDeviceIfNecessary() {
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::resetUsbDeviceIfNecessary start");
         try {
             UsbDeviceConnection usbConnection = mUsbManager.openDevice(mUsbDevice);
             usbConnection.releaseInterface(mUsbDevice.getInterface(0));
             Method resetDevice = UsbDeviceConnection.class.getMethod("resetDevice");
             Object result = resetDevice.invoke(usbConnection); // 调用反射方法，得到返回值
-            Log.e(TAG, "USB Device resetDevice result: " + result);
+            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::resetUsbDeviceIfNecessary result=" + result);
         } catch (Exception e) {
-            e.printStackTrace();
+            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::resetUsbDeviceIfNecessary exception: " + e.getMessage
());
         }
     }
 
@@ -303,7 +330,9 @@ public class AOACheckDevice {
      * @return
      */
     private boolean switchToAOA() {
+        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::switchToAOA start");
         if (mUsbDevice == null || AoaLink.getAoaDevice() == null) {
+            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::switchToAOA device or aoaDevice is null");
             return false;
         }
 
@@ -344,15 +373,15 @@ public class AOACheckDevice {
             return false;
         }
 
-        LogUtil.i(TAG,"AOACheckDevice::switchToAOA() Host->Accessory (写) Uri = " + AoaLink.getAoaDevice().
getUri());
+        LogUtil.i(TAG + TAG_AOA,"AOACheckDevice::switchToAOA ---> send Uri=" + AoaLink.getAoaDevice().getUr
i());
         if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR, A
OA_SEND_IDENT, 0, 4, AoaLink.getAoaDevice().getUri().getBytes(), AoaLink.getAoaDevice().getUri().getBytes().
length, 0) < 0) {
-            LogUtil.e(TAG, "AOACheckDevice::switchToAOA() --> send identity URI fail");
+            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::switchToAOA send URI fail");
             return false;
         }
 
-        LogUtil.i(TAG,"AOACheckDevice::switchToAOA() Host->Accessory (写) Serial = " + AoaLink.getAoaDevice
().getSerial());
+        LogUtil.i(TAG + TAG_AOA,"AOACheckDevice::switchToAOA ---> send Serial=" + AoaLink.getAoaDevice().ge
tSerial());
         if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR, A
OA_SEND_IDENT, 0, 5, AoaLink.getAoaDevice().getSerial().getBytes(), AoaLink.getAoaDevice().getSerial().getBy
tes().length, 0) < 0) {
-            LogUtil.e(TAG, "AOACheckDevice::switchToAOA() --> send identity SERIAL fail");
+            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::switchToAOA send SERIAL fail");
             return false;
         }
 
@@ -362,26 +391,35 @@ public class AOACheckDevice {
             return false;
         }
 
-        LogUtil.i(TAG, "AOACheckDevice::switchToAOA() --> switch to aoa mode success");
+        LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::switchToAOA switch to AOA mode success");
         return true;
     }
 
 
     /**
-     * fixme:@sunwj 这个逻辑是？
+     * 恢复AOA设备检测和连接
+     *
+     * 调用时机：
+     * 1. ConnectManager.startCheckConnect() 中，当 iNeedConnectType == DEVICE_TYPE_NONE 时
+     * 2. 当前连接状态不是 CONNECTED 或 CONNECTING 时
+     * 3. 通常在应用启动或从后台恢复时，用于重新检测已连接的USB设备
+     * 使用场景：
+     * - 应用启动时恢复之前的USB连接
+     * - 从无线连接切换回有线连接时
+     * - 后台切换到前台唤醒后重新检测USB设备状态
      */
     public void resume() {
-        LogUtil.i(TAG, "AOACheckDevice::resume() device count = " + (mUsbManager.getDeviceList() == null ? 
0: mUsbManager.getDeviceList().size()));
+        LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::resume start, device count=" + (mUsbManager.getDeviceList
() == null ? 0: mUsbManager.getDeviceList().size()));
         if (!mUsbManager.getDeviceList().isEmpty()) {
             if (mUsbDevice == null) {
                 HashMap<String, UsbDevice> deviceList = mUsbManager.getDeviceList();
-                LogUtil.i(TAG, "AOACheckDevice::resume() -->  device count = " + deviceList.size());
+                LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::resume find device, count=" + deviceList.size());
                 for (UsbDevice device : deviceList.values()) {
                     if (device == null) {
                         continue;
                     }
                     if (filterDevice(device)) {
-                        LogUtil.d(TAG, "AOACheckDevice::resume()  -->  ignore other device");
+                        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::resume ignore filtered device");
                         continue;
                     }
                     mUsbDevice = device;
@@ -389,9 +427,12 @@ public class AOACheckDevice {
             }
             CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT;
             if (isAOADevice(mUsbDevice)) {
-                LogUtil.i(TAG, "AOACheckDevice::resume() --> device is in AOA mode 1");
+                LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::resume device already in AOA mode, reset");
                 resetUsbDeviceIfNecessary();
-            } else checkDevices();
+            } else {
+                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::resume device not in AOA mode, check devices");
+                checkDevices();
+            }
         }
     }
 }
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)>
```

---

```bash
git diff linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java
```

```
diff --git a/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java b/linkHost/src/mai
n/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java
index e33e1e2..cbc832f 100644
--- a/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java
+++ b/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java
@@ -18,6 +18,12 @@ import com.autoai.welinkapp.model.SdkViewModel;
 public class EAPCheckDevice
         implements OnLinkEapListener {
     private static final String TAG = "WL_DRIVER";
+
+    /**
+     * 所有 EAP 相关的操作日志
+     */
+    private static final String TAG_EAP = ".WL_EAP";
+
     @SuppressLint("StaticFieldLeak")
     private static EAPCheckDevice mInstance = null;
     private Context mContext = null;
@@ -43,101 +49,125 @@ public class EAPCheckDevice
     }
 
     public void init(Context context) {
-        LogUtil.d(TAG, "EAP CheckDevice init");
+        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::init start");
         mContext = context;
         ThreadUtils.postOnBackgroundThread(() -> {
+            LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::init initMfiI2cPath start");
             EapLink.initMfiI2cPath();
-            LogUtil.i(TAG, "[IF] eAPInit end 1");
+            LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::init initMfiI2cPath end");
+            LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::init eapInit start");
             EapLink.eapInit();
-            LogUtil.i(TAG, "[IF] eAPInit end 2");
+            LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::init eapInit end");
+            LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::init register callback");
             EapLink.linkDeviceCallbackRegister(this);
-            LogUtil.i(TAG, "[IF] linkDeviceCBRegister end");
-
+            LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::init callback register complete");
         });
-
+        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::init complete");
     }
+    /**
+     * 恢复EAP设备检测和连接
+     *
+     * 调用时机：
+     * 1. ConnectManager.startCheckConnect() 中，当 iNeedConnectType == DEVICE_TYPE_NONE 时
+     * 2. 当前连接状态不是 CONNECTED 或 CONNECTING 时
+     * 3. 通常在应用启动或从后台恢复时，用于重新检测已连接的EAP设备
+
+     * 使用场景：
+     * - 应用启动时恢复之前的EAP连接
+     * - 从其他连接类型切换回EAP连接时
+     * - 后台切换到前台唤醒后重新检测EAP设备状态
+     */
     public void resume(){
-        LogUtil.d(TAG, "EAP CheckDevice resume start");
+        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::resume start");
         int eapStatus = EapLink.getEapStatus();
-        LogUtil.d(TAG, "EAP CheckDevice eapStatus  end:"+eapStatus);
+        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::resume eapStatus=" + eapStatus);
         if(eapStatus == EapStatus.EAP_ATTACH){
+            LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::resume device attached, set USB device mode");
             setUsbDeviceMode = true;
             EapLink.setUsbMode(true);
         }
-        LogUtil.d(TAG, "EAP CheckDevice resume end");
+        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::resume complete");
     }
     public void deinit() {
-        LogUtil.d(TAG, "EAP CheckDevice deinit");
+        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::deinit start");
+        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::deinit unregister callback");
         EapLink.unLinkDeviceCallbackRegister();
         if(setUsbDeviceMode){
-            LogUtil.d(TAG, "EAP resetUsb host");
+            LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::deinit reset USB to host mode");
             setUsbDeviceMode = false;
             EapLink.setUsbMode(false);
             EapLink.resetUsb();
         }
-        LogUtil.i(TAG, "[IF] eAPDeinit start");
+        LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::deinit eapDeinit start");
         EapLink.eapDeinit();
-        LogUtil.i(TAG, "[IF] eAPDeinit end");
+        LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::deinit eapDeinit end");
+        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::deinit complete");
     }
 
     @Override
     public void onLinkDeviceCallbackType(int eapStatus) {
-//        LogUtil.d(TAG, "onLinkDeviceCBType -- eapStatus: " + eapStatus);
+        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- eapStatus=" + eapStatus);
         switch (eapStatus) {
             case EapStatus.EAP_AUTHENTICATION_PASS:
-                LogUtil.d(TAG, "onLinkDeviceCBType --EAP_AUTHENTICATION_PASS");
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_AUTHENTICATION_
PASS");
                 //3、该状态表示iap和鉴权都已经准备好了（即调用activeIap之后状态） 可以调用eap的接口进行读写
eap了 这里APP根据自己的需求自行调用即可                                                                     -
 LogUtil.d(TAG, "[IF] eAPLaunchApp start");
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType eapLaunchApp start");
                 EapLink.eapLaunchApp();
-                LogUtil.d(TAG, "[IF] eAPLaunchApp end");
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType eapLaunchApp end");
                 CommonData.strDeviceSerialNum = SdkViewModel.getInstance().platformadaptor.getDeviceSerialN
um();
-                LogUtil.d(TAG, "iOS SerialNum: " + CommonData.strDeviceSerialNum);
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType iOS SerialNum=" + Common
Data.strDeviceSerialNum);
                 if (CommonData.isCheckingStart) {
+                    LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType ---> send CONNECT_ST
ATUS_IN");
                     CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_IN, CommonData.DEVICE_TYPE_EAP, 
null);
                 } else {
-                    LogUtil.d(TAG, "Checking is not start, EAP device in.");
+                    LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType checking not started
, mark need connect");
                     CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_EAP;
                 }
                 break;
             case EapStatus.EAP_ATTACH:
-                LogUtil.d(TAG, "onLinkDeviceCBType --EAP_ATTACH");
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_ATTACH");
 //                EapLink.activeEap();
                 //1、苹果手机连接才会有该状态通知上来 第三方互联若需要连接则调用setUsbMode将usb翻转为DEVICE
模式
               //注意：翻转为DEVICE模式后 该互联断开或者超时没有连接成功的时候一定要将usb翻转回到HOST模式 
不然下一次插入设备是识别不到的                                                                              +
  LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType set USB device mode");
                 setUsbDeviceMode = true;
                 EapLink.setUsbMode(true);
                 break;
             case EapStatus.EAP_DETACH:
-                LogUtil.d(TAG, "onLinkDeviceCBType --EAP_DETACH");
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_DETACH");
 //              被触发方式  一种只插入usb线但没有调用setUsbMode切device模式，然后拔掉线，
 //                另外一种就是调用了setUsbMode切成device模式就会来
 //                EapLink.deactiveEap();
 //                CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_OUT, CommonData.DEVICE_TYPE_EAP, n
ull);
                 break;
             case EapStatus.EAP_AUTHENTICATION_FAIL:
-                LogUtil.d(TAG, "onLinkDeviceCBType --EAP_AUTHENTICATION_FAIL");
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_AUTHENTICATION_
FAIL");
                 break;
             case EapStatus.EAP_ONLINE_ATTACH:
                 //2、将usb翻转为DEVICE（即调用了setUsbMode之后状态）模式后会有该状态上来 需要调用activeIap
初始化IAP
              //注意：这个状态在切换adb的时候也会触发 所以需要APP自行做过滤
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_ONLINE_ATTACH")
;
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType activeEap start");
                 EapLink.activeEap();
-                LogUtil.d(TAG, "onLinkDeviceCBType --EAP_ONLINE_ATTACH");
                 break;
             case EapStatus.EAP_ONLINE_DETACH:
                 //翻转DEVICE成功之后 然后拔掉usb线  注意：切换adb的时候也会有这个状态 app自行做过滤即可
-                LogUtil.d(TAG, "onLinkDeviceCBType --EAP_ONLINE_DETACH");
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_ONLINE_DETACH")
;
 
                 if(setUsbDeviceMode){
+                    LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType reset USB to host mo
de");
                     setUsbDeviceMode = false;
                     EapLink.setUsbMode(false);
                 }
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType deactiveEap");
                 EapLink.deactiveEap();
                 //发送广播已调用 stop ——>eapBulkClose
+                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType ---> send CONNECT_STATUS
_OUT");
                 CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_OUT, CommonData.DEVICE_TYPE_EAP, nul
l);
 //                EapLink.eapBulkClose();
                 break;
             default:
-                LogUtil.i(TAG, "onLinkDeviceCBType -- unknown");
+                LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- unknown status=" + 
eapStatus);
                 break;
         }
     }

```

---

```bash
git diff localLibs/wlhardware/build.gradle
```

```
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)> git diff localLibs/wlhardware/build.gradle
diff --git a/localLibs/wlhardware/build.gradle b/localLibs/wlhardware/build.gradle
index 2a0aff2..e05c3ef 100644
--- a/localLibs/wlhardware/build.gradle
+++ b/localLibs/wlhardware/build.gradle
@@ -1,17 +1,17 @@
 configurations.maybeCreate("default")
 artifacts.add("default", file('wlhardware.aar'))
 
-apply plugin: 'maven'
+apply plugin: 'maven-publish'
 
-uploadArchives {
-    repositories {
-        mavenDeployer {
-            repository(url: "https://wdnexus.autoai.com/content/repositories/autoai-AVS/") {
-                authentication(userName: "autoai-AVS", password: "@pIbl9kE")
-            }
-            pom.version = '1.0.2'
-            pom.groupId = 'com.autoai.avs.link'
-            pom.artifactId = 'wlhardware'
-        }
-    }
-}
\ No newline at end of file
+//uploadArchives {
+//    repositories {
+//        mavenDeployer {
+//            repository(url: "https://wdnexus.autoai.com/content/repositories/autoai-AVS/") {
+//                authentication(userName: "autoai-AVS", password: "@pIbl9kE")
+//            }
+//            pom.version = '1.0.2'
+//            pom.groupId = 'com.autoai.avs.link'
+//            pom.artifactId = 'wlhardware'
+//        }
+//    }
+//}
\ No newline at end of file
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)>
```

---

现在我已经检查了 avslink 子模块中的主要修改。基于所有的代码审查，我整理了以下问题和发现：

## 主项目问题（已在之前的分析中提到）

1. **app/build.gradle 问题**：
   - 绝对路径格式错误和行截断问题
   - 注释掉的 linkhid 依赖没有有效替代
   - 缩进和格式不一致

2. **CarInfoProxy.kt 问题**：
   - 功能被大量注释但没有替代实现
   - 移除了必要的导入但注释而非删除引用

3. **PopWinService.java 问题**：
   - 日志输出行过长

## avslink 子模块问题

1. **linkHost/build.gradle 问题**：
   - 和主项目类似的绝对路径问题：
     ```gradle
     java.srcDirs += "/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/link-hid/avslinkhid/src/main/java"
     ```
   - 注释掉 linkhid 依赖但使用条件注释替代：
     ```gradle
     //    if (file("../../link-hid/avslinkhid").exists()  && SDK_TEST != "true") {
     //        implementation project(path: ':avslinkhid')
     //    } else {
     //        implementation "com.autoai.avs.link:linkhid:${LINK_HID_VERSION}"
     //    }
     ```
   - 这可能导致构建失败，因为依赖被完全注释掉

2. **AOACheckDevice.java 和 EAPCheckDevice.java 问题**：
   - 这些文件的修改主要是增加日志输出和改进日志格式
   - 添加了常量 `TAG_AOA` 和 `TAG_EAP` 用于日志分类
   - 格式化和增强日志信息，添加方法名和更详细的状态信息
   - 添加了一些注释文档
   - 这些更改本身不是问题，但可能导致日志输出过多

3. **localLibs/wlhardware/build.gradle 问题**：
   - 从 `maven` 插件迁移到 `maven-publish` 插件
   - 注释掉了 `uploadArchives` 配置
   - 这可能会影响库的发布过程

## 总体分析和建议

1. **路径硬编码问题**：
   - 主项目和子模块都使用了绝对路径指向同一开发者机器
   - 应使用相对路径或项目变量引用路径

2. **依赖管理问题**：
   - 多处注释掉依赖但未提供适当替代
   - 潜在的构建失败风险

3. **功能禁用问题**：
   - CarInfoProxy 中大量功能被禁用但没有替代
   - 这可能导致应用功能损失

4. **日志增强**：
   - avslink 子模块的日志增强是有益的改进
   - 但需确保不会导致日志过多影响性能

5. **发布配置变更**：
   - 从 maven 到 maven-publish 的迁移可能需要额外配置

## 修复建议

1. **修复路径问题**：
   ```gradle
   // 替换绝对路径为相对路径
   java.srcDirs += "../../link-hid/avslinkhid/src/main/java"
   ```

2. **处理依赖问题**：
   - 如果 linkhid 依赖不再需要，应从其他地方提供功能
   - 如果仍需要，应取消注释并确保可用

3. **处理功能禁用**：
   - 为 CarInfoProxy 中的注释掉方法提供适当替代
   - 或完全移除相关功能并更新调用点

4. **完成子模块发布配置**：
   - 如果迁移到 maven-publish，应提供完整配置
   - 或恢复原有 maven 配置

5. **解决子模块状态**：
   - 提交 avslink 子模块更改以移除 "dirty" 状态
   - 确保子模块版本和主项目兼容

这些问题中，路径硬编码和依赖管理问题最为严重，可能导致构建失败。功能禁用问题可能导致运行时错误或功能损失。建议在提交前优先解决这些问题。

---

_**User**_

avslink 最近的 commit，有啥问题么。检查一下。

---

_**Assistant**_

让我检查一下 avslink 子模块的最近提交信息和详细变更。

---

```bash
git log -1 --pretty=format:"%h - %an, %ar : %s"
```

```
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)> git log -1 --pretty=format:"%h - %an, %ar : %s"
92f2469 - sunwenjun, 5 minutes ago : 临时修改开关蓝牙后，iOS 没有重新校准导致的不能点击的问题。⏎            
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)>
```

---

```bash
git show --name-only
```

```
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)> git show --name-only
commit 92f246900fe667a21778db3a8e5c9fe6ef7301f8 (HEAD -> dev)
Author: sunwenjun <<EMAIL>>
Date:   Tue Jul 1 15:58:34 2025 +0800

    临时修改开关蓝牙后，iOS 没有重新校准导致的不能点击的问题。

linkHost/src/main/java/com/autoai/welinkapp/checkconnect/AOACheckDevice.java
linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java
linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java
linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java
linkHost/src/main/java/com/autoai/welinkapp/checkconnect/EAPCheckDevice.java
linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java
linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DataTransfer.java
linkHost/src/main/java/com/autoai/welinkapp/datatransfer/EAPManager.java
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)>
```

---

```bash
git diff HEAD~1 linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java
```

```
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)> git diff HEAD~1 linkHost/src/main/java/com/autoai/welinkapp/
checkconnect/CommonData.java
                             


diff --git a/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java b/linkHost/src/main/ja
va/com/autoai/welinkapp/checkconnect/CommonData.java
index 613722c..640daea 100644
--- a/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java
+++ b/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/CommonData.java
@@ -15,6 +15,17 @@ import com.autoai.welink.macro.WL_API_DEVICE_TYPE;
 @SuppressWarnings("unused")
 public class CommonData {
     private static final String TAG = "WL_DRIVER";
+
+    /**
+     * 所有 AOA 相关的操作日志
+     */
+    public static final String TAG_AOA = ".WL_AOA";
+
+    /**
+     * 所有 EAP 相关的操作日志
+     */
+    public static final String TAG_EAP = ".WL_EAP";
+
     /**
      * Device connection status
      */
sun@sundeMac-mini /V/M/U/s/W/L/a/avslink (dev)>
```

---

```bash
git diff HEAD~1 linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java
```

```
diff --git a/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java b/linkHost/src/mai
n/java/com/autoai/welinkapp/checkconnect/ConnectManager.java
index aa454e8..4193883 100644
--- a/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java
+++ b/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/ConnectManager.java
@@ -18,6 +18,16 @@ import java.util.logging.LogManager;
 public class ConnectManager {
     private static final String TAG = "WL_DRIVER";
 
+    /**
+     * 所有 AOA 相关的操作日志
+     */
+    private static final String TAG_AOA = ".WL_AOA";
+
+    /**
+     * 所有 EAP 相关的操作日志
+     */
+    private static final String TAG_EAP = ".WL_EAP";
+
     private Context mContext = null;
     private DeviceStatusReceiver mStatusReceiver = null;
     private DeviceStatusListener mStatusListener = null;
@@ -44,40 +54,51 @@ public class ConnectManager {
      * @param statusListener
      */
     public void init(Context context, DeviceStatusListener statusListener) {
-        LogUtil.d(TAG, "ConnectManager::init()");
+        LogUtil.d(TAG, "ConnectManager::init() start");
         mContext = context;
         mStatusListener = statusListener;
 //        ConnectAdapter.getInstance().init(mContext);
+        LogUtil.d(TAG + TAG_AOA, "ConnectManager::init AOACheckDevice init start");
         AOACheckDevice.getInstance().init(mContext);
         if (IdbLink.isIdbModel())
             IDBCheckDevice.getInstance().init(mContext);
-        if (EapLink.isEapModel())
+        if (EapLink.isEapModel()) {
+            LogUtil.d(TAG + TAG_EAP, "ConnectManager::init EAPCheckDevice init start");
             EAPCheckDevice.getInstance().init(mContext);
+        }
         //设备状态广播接收器
+        LogUtil.d(TAG, "ConnectManager::init DeviceStatusReceiver register start");
         mStatusReceiver = new DeviceStatusReceiver(mContext, mStatusListener);
         mStatusReceiver.registerReceiver();
+        LogUtil.d(TAG, "ConnectManager::init() complete");
     }
 
     public void registerUiResponder(UIResponder uiResponder) {
         LogUtil.d(TAG, "ConnectManager::registerUiResponder()");
+        LogUtil.d(TAG + TAG_AOA, "ConnectManager::registerUiResponder AOACheckDevice register");
         AOACheckDevice.getInstance().registerUiResponder(uiResponder);
     }
 
     public void deinit() {
-        LogUtil.d(TAG, "ConnectManager::deinit");
+        LogUtil.d(TAG, "ConnectManager::deinit start");
+        LogUtil.d(TAG + TAG_AOA, "ConnectManager::deinit AOACheckDevice deinit start");
         AOACheckDevice.getInstance().deinit();
-        if (EapLink.isEapModel())
+        if (EapLink.isEapModel()) {
+            LogUtil.d(TAG + TAG_EAP, "ConnectManager::deinit EAPCheckDevice deinit start");
             EAPCheckDevice.getInstance().deinit();
+        }
         if (IdbLink.isIdbModel())
             IDBCheckDevice.getInstance().deinit();
 //        ConnectAdapter.getInstance().deinit();
 
         if (null != mStatusReceiver) {
+            LogUtil.d(TAG, "ConnectManager::deinit DeviceStatusReceiver unregister");
             mStatusReceiver.unregisterReceiver();
             mStatusReceiver = null;
         }
 
         mStatusListener = null;
+        LogUtil.d(TAG, "ConnectManager::deinit complete");
     }
 
     /**
@@ -85,22 +106,29 @@ public class ConnectManager {
      */
     public void startCheckConnect() {
         LogUtil.printStackTraceString(TAG, "ConnectManager::startCheckConnect");
-        LogUtil.d(TAG, "ConnectManager::startCheckConnect(), need connect type: " + CommonData.iNeedConnect
Type + ",iCurrentConnectType:" + CommonData.iCurrentConnectType);
+        LogUtil.d(TAG, "ConnectManager::startCheckConnect needType=" + CommonData.iNeedConnectType +
+                ", currentType=" + CommonData.iCurrentConnectType
+        + " connectStatus: " + SdkViewModel.getInstance().connectStatus);
         //--> 开启有线设备搜索
         CommonData.isCheckingStart = true;
 
         if (CommonData.DEVICE_TYPE_AOA == CommonData.iNeedConnectType) {
+            LogUtil.d(TAG + TAG_AOA, "ConnectManager::startCheckConnect AOA device check start");
             AOACheckDevice.getInstance().checkDevices();
         } else if (CommonData.DEVICE_TYPE_EAP == CommonData.iNeedConnectType) {
+            LogUtil.d(TAG + TAG_EAP, "ConnectManager::startCheckConnect ---> EAP send CONNECT_STATUS_IN");
             CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_IN, CommonData.iNeedConnectType, null);
         } else if (CommonData.DEVICE_TYPE_IDB == CommonData.iNeedConnectType) {
+            LogUtil.d(TAG, "ConnectManager::startCheckConnect IDB device check start");
             IDBCheckDevice.getInstance().checkDevices();
         } else if (CommonData.iNeedConnectType == CommonData.DEVICE_TYPE_NONE) {
             if (SdkViewModel.getInstance().connectStatus != PlatformAdaptor.CONNECT_STATE.CONNECTED
                     && SdkViewModel.getInstance().connectStatus != PlatformAdaptor.CONNECT_STATE.CONNECTING
) {
-                LogUtil.d(TAG, "ConnectManager startCheckConnect, connectStatus:" + SdkViewModel.getInstanc
e().connectStatus);
+                LogUtil.d(TAG, "ConnectManager::startCheckConnect resume devices, connectStatus=" + SdkView
Model.getInstance().connectStatus);
 //             if(SdkViewModel.getInstance().connectStatus == 0){
+                LogUtil.d(TAG + TAG_AOA, "ConnectManager::startCheckConnect AOACheckDevice resume");
                 AOACheckDevice.getInstance().resume();
+                LogUtil.d(TAG + TAG_EAP, "ConnectManager::startCheckConnect EAPCheckDevice resume");
                 EAPCheckDevice.getInstance().resume();
             }
         }
@@ -115,11 +143,12 @@ public class ConnectManager {
     }
 
     public void stopCheckConnect() {
-        LogUtil.d(TAG, "ConnectManager::stopCheckConnect  iCurrentConnectType：" + CommonData.iCurrentConne
ctType);
+        LogUtil.d(TAG, "ConnectManager::stopCheckConnect iCurrentConnectType=" + CommonData.iCurrentConnect
Type);
         CommonData.isCheckingStart = false;
         if (CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_ANDROID &&
                 CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_IOS &&
                 CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_HARMONY) {
+            LogUtil.d(TAG, "ConnectManager::stopCheckConnect ---> stop wireless check");
             stopCheckWireless(STOP_BY_APP_REQUEST);
         }
     }
@@ -131,17 +160,38 @@ public class ConnectManager {
      * @param status 连接状态, 取值范围 DEVICE_STATUS_*
      */
     public void connectStatusChange(int type, int status) {
-        LogUtil.d(TAG, "ConnectManager::deviceStatusChange --> type: " + type + ", status: " + status);
+        String deviceTypeStr = getDeviceTypeString(type);
+        LogUtil.d(TAG, "ConnectManager::connectStatusChange <--- type=" + type + "(" + deviceTypeStr + "), 
status=" + status);
+
+        if (type == DEVICE_TYPE_AOA) {
+            LogUtil.d(TAG + TAG_AOA, "ConnectManager::connectStatusChange <--- AOA status=" + status);
+        } else if (type == DEVICE_TYPE_EAP) {
+            LogUtil.d(TAG + TAG_EAP, "ConnectManager::connectStatusChange <--- EAP status=" + status);
+        }
+
         if (DEVICE_STATUS_FAIL == status || DEVICE_STATUS_NONE == status) {
 //            ConnectAdapter.getInstance().setConnectStatus(type, DEVICE_STATUS_NONE);
             if (CommonData.DEVICE_TYPE_WIFI_HARMONY == type || CommonData.DEVICE_TYPE_WIFI_ANDROID == type 
|| CommonData.DEVICE_TYPE_WIFI_IOS == type) {
+                LogUtil.d(TAG, "ConnectManager::connectStatusChange wireless disconnect, stopCheckWireless"
);
                 //wirelessDisconnect();
                 stopCheckWireless(STOP_BY_CHANNEL_CONTROL_MSG);
             }
         } else if (DEVICE_STATUS_CONNECTED == status) {
 //            ConnectAdapter.getInstance().setConnectStatus(type, DEVICE_STATUS_CONNECTED);
+            LogUtil.d(TAG, "ConnectManager::connectStatusChange device connected successfully");
         } else {
-            LogUtil.d(TAG, "ConnectManager::deviceStatusChange --> connectStatusChange, other connect statu
s.");
+            LogUtil.d(TAG, "ConnectManager::connectStatusChange other status=" + status);
+        }
+    }
+
+    private String getDeviceTypeString(int type) {
+        switch (type) {
+            case DEVICE_TYPE_AOA: return "AOA";
+            case DEVICE_TYPE_EAP: return "EAP";
+            case DEVICE_TYPE_IDB: return "IDB";
+            case DEVICE_TYPE_WIFI_ANDROID: return "WIFI_ANDROID";
+            case DEVICE_TYPE_WIFI_IOS: return "WIFI_IOS";
+            default: return "UNKNOWN";
         }
     }
 
@@ -157,7 +207,7 @@ public class ConnectManager {
 
     //TODO.WIRELESS
     public void startCheckWireless() {
-        LogUtil.d(TAG, "ConnectManager::startCheckWireless");
+        LogUtil.d(TAG, "ConnectManager::startCheckWireless ---> send WIRELESS_START_CHECK");
         CommonData.sendMsg(mContext, CommonData.WIRELESS_START_CHECK, 0, null);
     }
 
@@ -175,12 +225,13 @@ public class ConnectManager {
      * she tips:添加追溯入参
      */
     public void stopCheckWireless(int reason) {
-        LogUtil.printStackTraceString(TAG, "ConnectManager::stopCheckWireless reason = " + reason);
+        LogUtil.printStackTraceString(TAG, "ConnectManager::stopCheckWireless reason=" + reason);
+        LogUtil.d(TAG, "ConnectManager::stopCheckWireless ---> send WIRELESS_STOP_CHECK");
         CommonData.sendMsg(mContext, CommonData.WIRELESS_STOP_CHECK, 0, null);
     }
 
     public void stopConnectWireless() {
-        LogUtil.d(TAG, "stopConnectWireless");
+        LogUtil.d(TAG, "ConnectManager::stopConnectWireless ---> send WIRELESS_STOP_CONNECT");
         CommonData.sendMsg(mContext, CommonData.WIRELESS_STOP_CONNECT, 0, null);
     }
 }

```

---

```bash
git diff HEAD~1 linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java
```

```
diff --git a/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java b/linkHost/s
rc/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java
index 6fef5ac..3269f00 100644
--- a/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java
+++ b/linkHost/src/main/java/com/autoai/welinkapp/checkconnect/DeviceStatusReceiver.java
@@ -16,11 +16,23 @@ import com.autoai.welinkapp.datatransfer.DataTransfer;
  */
 public class DeviceStatusReceiver extends BroadcastReceiver {
     private static final String TAG = "WL_DRIVER";
+
+    /**
+     * 所有 AOA 相关的操作日志
+     */
+    private static final String TAG_AOA = ".WL_AOA";
+
+    /**
+     * 所有 EAP 相关的操作日志
+     */
+    private static final String TAG_EAP = ".WL_EAP";
+
     private final Context mContext;
     private final DataTransfer mDataTransfer;
     private final DeviceStatusListener mListener;
 
     public DeviceStatusReceiver(Context context, DeviceStatusListener listener) {
+        LogUtil.d(TAG, "DeviceStatusReceiver::init");
         mContext = context;
         mListener = listener;
         //--> fixme: 是否重复
@@ -33,12 +45,13 @@ public class DeviceStatusReceiver extends BroadcastReceiver {
      */
     @SuppressLint("UnspecifiedRegisterReceiverFlag")
     public void registerReceiver() {
-        LogUtil.d(TAG, "DeviceStatusReceiver::registerReceiver()");
+        LogUtil.d(TAG, "DeviceStatusReceiver::registerReceiver start");
         IntentFilter filter = new IntentFilter();
         filter.addAction(CommonData.DEVICE_IN_MSG);
         filter.addAction(CommonData.DEVICE_OUT_MSG);
         filter.addAction(CommonData.DEVICE_CONNECTING);
         mContext.registerReceiver(this, filter,Context.RECEIVER_NOT_EXPORTED);
+        LogUtil.d(TAG, "DeviceStatusReceiver::registerReceiver complete");
     }
 
     public void unregisterReceiver() {
@@ -47,67 +60,71 @@ public class DeviceStatusReceiver extends BroadcastReceiver {
     }
 
     private void filterDeviceInMsg(int type, String ip) {
-        LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg --> type: " + type + ", ip: " + ip);
+        String deviceTypeStr = getDeviceTypeString(type);
+        LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg <--- type=" + type + "(" + deviceTypeStr + 
"), ip=" + ip);
         if (CommonData.iCurrentConnectStatus != CommonData.CONNECT_STATUS_IN) {
             if (CommonData.DEVICE_TYPE_AOA == type) {
-                LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg --> AOA Device in");
+                LogUtil.d(TAG + TAG_AOA, "DeviceStatusReceiver::filterDeviceInMsg AOA device in, start conn
ect");
                 mDataTransfer.startConnect(ConnectManager.DEVICE_TYPE_AOA);
             } else if (CommonData.DEVICE_TYPE_EAP == type) {
-                LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg EAP Device in");
+                LogUtil.d(TAG + TAG_EAP, "DeviceStatusReceiver::filterDeviceInMsg EAP device in, start conn
ect");
                 mDataTransfer.startConnect(ConnectManager.DEVICE_TYPE_EAP);
             } else if (CommonData.DEVICE_TYPE_IDB == type) {
-                LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg IDB Device in");
+                LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg IDB device in, start connect");
                 mDataTransfer.startConnect(ConnectManager.DEVICE_TYPE_IDB);
             } else {
-                LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg Wireless Device in");
+                LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg Wireless device in, start connect")
;
             }
             if (mListener != null) {
-                LogUtil.i(TAG, "DeviceStatusReceiver::filterDeviceInMsg --> onDeviceIn, type: " + type);
+                LogUtil.i(TAG, "DeviceStatusReceiver::filterDeviceInMsg notify listener onDeviceIn, type=" 
+ type);
                 mListener.onDeviceIn(type, ip);
             }
             CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN;
             CommonData.iCurrentConnectType = type;
+            LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg status updated: currentType=" + type + 
", status=IN");
         } else if (CommonData.iCurrentConnectType != type) {
-            LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg device has in.type is " + type);
+            LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg device already connected, new type=" + 
type + ", current=" + CommonData.iCurrentConnectType);
             //--> fixme：@zhenggc 留意
 //            CommonData.iNeedConnectType = type;
 //            CommonData.strNeedConnectIp = ip;
         } else {
-            LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg device in again.");
+            LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg device in again, type=" + type);
         }
     }
 
     private void filterDeviceOutMsg(int type) {
-        Log.i(TAG,"DeviceStatusReceiver::filterDeviceOutMsg --> type = " + type);
+        String deviceTypeStr = getDeviceTypeString(type);
+        LogUtil.i(TAG,"DeviceStatusReceiver::filterDeviceOutMsg <--- type=" + type + "(" + deviceTypeStr + 
")");
         if (CommonData.iCurrentConnectStatus != CommonData.CONNECT_STATUS_OUT) {
             if (CommonData.iCurrentConnectType == type) {
                 if (CommonData.DEVICE_TYPE_AOA == type) {
-                    LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceOutMsg --> AOA Device out");
+                    LogUtil.d(TAG + TAG_AOA, "DeviceStatusReceiver::filterDeviceOutMsg AOA device out, stop
 connect");
                     mDataTransfer.stopConnect(ConnectManager.DEVICE_TYPE_AOA);
                 } else if (CommonData.DEVICE_TYPE_EAP == type) {
-                    LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceOutMsg --> EAP Device out");
+                    LogUtil.d(TAG + TAG_EAP, "DeviceStatusReceiver::filterDeviceOutMsg EAP device out, stop
 connect");
                     mDataTransfer.stopConnect(ConnectManager.DEVICE_TYPE_EAP);
                 } else if (CommonData.DEVICE_TYPE_IDB == type) {
-                    LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceOutMsg --> IDB Device out");
+                    LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceOutMsg IDB device out, stop connect")
;
                     mDataTransfer.stopConnect(ConnectManager.DEVICE_TYPE_IDB);
                 } else {
-                    LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceOutMsg --> Wireless Device out");
+                    LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceOutMsg Wireless device out, stop conn
ect");
                 }
                 if (mListener != null) {
-                    LogUtil.i(TAG, "DeviceStatusReceiver::filterDeviceOutMsg -->onDeviceOut, type: " + type
);
+                    LogUtil.i(TAG, "DeviceStatusReceiver::filterDeviceOutMsg notify listener onDeviceOut, t
ype=" + type);
                     mListener.onDeviceOut(type);
                 }
+                LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceOutMsg reset connect status");
                 CommonData.resetConnectStatus();
               /*  if (CommonData.iNeedConnectType != CommonData.DEVICE_TYPE_NONE) {
                     checkOtherConnect(CommonData.iNeedConnectType);
                 }*/
             } else {
-                LogUtil.e(TAG, "DeviceStatusReceiver::filterDeviceOutMsg -->CommonData.iCurrentConnectType 
!= " + type);
+                LogUtil.e(TAG, "DeviceStatusReceiver::filterDeviceOutMsg type mismatch: current=" + CommonD
ata.iCurrentConnectType + ", received=" + type);
                 CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_NONE;
                 CommonData.strNeedConnectIp = null;
             }
         } else {
-            LogUtil.e(TAG, "DeviceStatusReceiver::filterDeviceOutMsg --> CommonData.iCurrentConnectStatus =
= CommonData.CONNECT_STATUS_OUT  type = " + type);
+            LogUtil.e(TAG, "DeviceStatusReceiver::filterDeviceOutMsg already disconnected, type=" + type);
         }
     }
 
@@ -140,23 +157,40 @@ public class DeviceStatusReceiver extends BroadcastReceiver {
     public void onReceive(Context context, Intent intent) {
         String action = intent.getAction();
         ProcessUtil.printProcess(getClass());
+        LogUtil.d(TAG, "DeviceStatusReceiver::onReceive <--- broadcast action=" + action);
+
         if (CommonData.DEVICE_IN_MSG.equals(action)) {
             int type = intent.getIntExtra(CommonData.MSG_KEY_TYPE, -1);
             String ip = intent.getStringExtra(CommonData.MSG_KEY_IP);
-            LogUtil.d(TAG, "DeviceStatusReceiver::onReceive --> Device in, type: " + type + ", ip: " + ip);
+            String deviceTypeStr = getDeviceTypeString(type);
+            LogUtil.d(TAG, "DeviceStatusReceiver::onReceive <--- DEVICE_IN_MSG: type=" + type + "(" + devic
eTypeStr + "), ip=" + ip);
             filterDeviceInMsg(type, ip);
         } else if (CommonData.DEVICE_OUT_MSG.equals(action)) {
             int type = intent.getIntExtra(CommonData.MSG_KEY_TYPE, -1);
-            LogUtil.d(TAG, "DeviceStatusReceiver::onReceive --> Device out, type: " + type);
+            String deviceTypeStr = getDeviceTypeString(type);
+            LogUtil.d(TAG, "DeviceStatusReceiver::onReceive <--- DEVICE_OUT_MSG: type=" + type + "(" + devi
ceTypeStr + ")");
             filterDeviceOutMsg(type);
         } else if (CommonData.DEVICE_CONNECTING.equals(action)) {
             if (mListener != null) {
                 int type = intent.getIntExtra(CommonData.MSG_KEY_TYPE, -1);
-                LogUtil.i(TAG, "DeviceStatusReceiver::onReceive --> on Devices connecting, type: " + type);
+                String deviceTypeStr = getDeviceTypeString(type);
+                LogUtil.i(TAG, "DeviceStatusReceiver::onReceive <--- DEVICE_CONNECTING: type=" + type + "("
 + deviceTypeStr + ")");
                 mListener.onDevicesConnecting(type);
             }
         } else {
-            LogUtil.e(TAG, "DeviceStatusReceiver::onReceive --> unknown broadcast: " + action);
+            LogUtil.e(TAG, "DeviceStatusReceiver::onReceive <--- unknown broadcast: " + action);
+        }
+    }
+
+    private String getDeviceTypeString(int type) {
+        switch (type) {
+            case CommonData.DEVICE_TYPE_AOA: return "AOA";
+            case CommonData.DEVICE_TYPE_EAP: return "EAP";
+            case CommonData.DEVICE_TYPE_IDB: return "IDB";
+            case CommonData.DEVICE_TYPE_WIFI_ANDROID: return "WIFI_ANDROID";
+            case CommonData.DEVICE_TYPE_WIFI_IOS: return "WIFI_IOS";
+            case CommonData.DEVICE_TYPE_WIFI_HARMONY: return "WIFI_HARMONY";
+            default: return "UNKNOWN";
         }
     }
 

```

---

```bash
git diff HEAD~1 linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java
```

```
diff --git a/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java b/linkHost/src/main/ja
va/com/autoai/welinkapp/datatransfer/AOAManager.java
index 2d6a7f7..10d85c2 100644
--- a/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java
+++ b/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java
@@ -24,6 +24,11 @@ public class AOAManager {
     public static int MAX_ERROR_COUNT = 200;
     private static final String TAG = "WL_DRIVER";
 
+    /**
+     * 所有 AOA 相关的操作日志
+     */
+    private static final String TAG_AOA = ".WL_AOA";
+
     private static final boolean CHECK_AOA_MANAGER = LogUtil.isFullLog();
 
     @SuppressLint("StaticFieldLeak")
@@ -73,68 +78,81 @@ public class AOAManager {
     }
 
     public void init(Context context) {
-        LogUtil.d(TAG, "AOAManager::init");
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::init start");
         mContext = context;
         mUsbManager = (UsbManager) mContext.getSystemService(Context.USB_SERVICE);
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::init DeviceManager create");
         mDeviceManager = new DeviceManager(mContext);
         try {
+            LogUtil.d(TAG + TAG_AOA, "AOAManager::init AOAServerThread start, port=" + AOA_SOCKET_PORT);
             mAOAServerThread = new AOAServerThread(AOA_SOCKET_PORT);
             mAOAServerThread.start();
         } catch (Exception e) {
-            LogUtil.e(TAG, "Create socket fail");
+            LogUtil.e(TAG + TAG_AOA, "AOAManager::init create socket fail: " + e.getMessage());
         }
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::init complete");
     }
 
     public void deinit() {
-        LogUtil.d(TAG, "AOAManager deinit");
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit start");
         try {
             if (null != mSocket) {
+                LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit close socket");
                 mSocket.close();
                 mSocket = null;
             }
         } catch (Exception e) {
-            LogUtil.e(TAG, "Exception in deinit");
+            LogUtil.e(TAG + TAG_AOA, "AOAManager::deinit socket close exception: " + e.getMessage());
         }
         if (mAOARecvThread != null) {
+            LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit cancel AOARecvThread");
             mAOARecvThread.cancel();
             mAOARecvThread = null;
         }
         if (mAOASendThread != null) {
+            LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit cancel AOASendThread");
             mAOASendThread.cancel();
             mAOASendThread = null;
         }
         if (mAOAServerThread != null) {
+            LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit cancel AOAServerThread");
             mAOAServerThread.cancel();
             mAOAServerThread = null;
         }
         if(mDeviceManager != null) {
+            LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit USB device deinit");
             mDeviceManager.deinitUsbDevice();
         }
         isAOADeviceStart = false;
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit complete");
     }
 
     public void startAOA() {
-        LogUtil.d(TAG, "AOAManager::startAOA");
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::startAOA start");
         if (!checkDevices()) {
-            LogUtil.e(TAG, "AOAManager::startAOA --> check device fail");
+            LogUtil.e(TAG + TAG_AOA, "AOAManager::startAOA check device fail");
         } else {
             isAOADeviceStart = true;
+            LogUtil.d(TAG + TAG_AOA, "AOAManager::startAOA device start success");
         }
     }
 
     public void stopAOA() {
-        LogUtil.d(TAG, "AOAManager stopAOA");
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::stopAOA start");
         if (mAOARecvThread != null) {
+            LogUtil.d(TAG + TAG_AOA, "AOAManager::stopAOA cancel AOARecvThread");
             mAOARecvThread.cancel();
             mAOARecvThread = null;
         }
         if (mAOASendThread != null) {
+            LogUtil.d(TAG + TAG_AOA, "AOAManager::stopAOA cancel AOASendThread");
             mAOASendThread.cancel();
             mAOASendThread = null;
         }
         isAOADeviceStart = false;
         iWidth = 0;
         iHeight = 0;
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::stopAOA complete");
     }
 
 //    public int registerHID(int w, int h) {
@@ -199,40 +217,45 @@ public class AOAManager {
     }
 
     private boolean checkDevices() {
-        LogUtil.d(TAG, "AOAManager::checkDevices()");
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices start");
 
         if (mContext == null || mUsbManager == null) {
-            LogUtil.e(TAG, "AOAManager::checkDevices() --> AOAManager checkDevices fail");
+            LogUtil.e(TAG + TAG_AOA, "AOAManager::checkDevices context or usbManager is null");
             return false;
         }
 
         HashMap<String, UsbDevice> deviceList = mUsbManager.getDeviceList();
-        LogUtil.d(TAG, "AOAManager::checkDevices() --> device count = " + deviceList.size());
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices device count=" + deviceList.size());
         for (UsbDevice device : deviceList.values()) {
             if (device == null) {
                 continue;
             }
 
             if (!isAOADevice(device)) {
+                LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices not AOA device, vid=" + device.getVendor
Id() + ", pid=" + device.getProductId());
                 continue;
             }
 
+            LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices found AOA device, init start");
             if (mDeviceManager.init(mUsbManager, device)) {
-                LogUtil.d(TAG, "AOAManager::checkDevices() --> init device success");
+                LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices init device success");
                 return true;
             }
         }
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices no valid AOA device found");
         return false;
     }
 
     private boolean isAOADevice(UsbDevice device) {
         if (device == null) {
-            LogUtil.e(TAG, "device is null in isAOADevice");
+            LogUtil.e(TAG + TAG_AOA, "AOAManager::isAOADevice device is null");
             return false;
         }
         int vid = device.getVendorId();
         int pid = device.getProductId();
-        return (vid == VID_ACCESSORY && (pid >> 8 & 0x000000ff) == PID_ACCESSORY && (pid & 0x000f) <= 5);
+        boolean isAOA = (vid == VID_ACCESSORY && (pid >> 8 & 0x000000ff) == PID_ACCESSORY && (pid & 0x000f)
 <= 5);
+        LogUtil.d(TAG + TAG_AOA, "AOAManager::isAOADevice vid=" + vid + ", pid=" + pid + ", isAOA=" + isAOA
);
+        return isAOA;
     }
 
     private class AOAServerThread extends Thread {
@@ -243,7 +266,7 @@ public class AOAManager {
         private BufferedOutputStream mOutputStream = null;
 
         public AOAServerThread(int port) {
-            LogUtil.d(TAG, "AOAServerThread::<init>, port: " + port);
+            LogUtil.d(TAG + TAG_AOA, "AOAServerThread::init port=" + port);
             setName(AOA_SERVER_THREAD_NAME);
 
             try {
@@ -251,8 +274,9 @@ public class AOAManager {
                 mServerSocket.setReuseAddress(true);
                 mServerSocket.bind(new InetSocketAddress(port));
                 isRunning = true;
+                LogUtil.d(TAG + TAG_AOA, "AOAServerThread::init socket bind success");
             } catch (Exception e) {
-                LogUtil.e(TAG, "Create Socket Fail");
+                LogUtil.e(TAG + TAG_AOA, "AOAServerThread::init create socket fail: " + e.getMessage());
             }
         }
 
@@ -322,45 +346,47 @@ public class AOAManager {
 
         @Override
         public void run() {
-            LogUtil.d(TAG, "AOAManager::AOAServerThread --> Begin to listen in AOAServerThread");
+            LogUtil.d(TAG + TAG_AOA, "AOAServerThread::run begin listen");
             try {
                 while (isRunning) {
                     mThreadSocket = mServerSocket.accept();
-                    LogUtil.e(TAG, "AOAManager::AOAServerThread --> AOA ServerSocket is  accept !");
+                    LogUtil.d(TAG + TAG_AOA, "AOAServerThread::run <--- client connected");
                     if (null == mThreadSocket) {
-                        LogUtil.e(TAG, "AOAManager::AOAServerThread --> client connected fail");
+                        LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run client connect fail");
                         continue;
                     }
                     if (!isAOADeviceStart) {
-                        LogUtil.e(TAG, "AOAManager::AOAServerThread --> AOA is not start!");
+                        LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run AOA not started, close connection");
                         mThreadSocket.close();
                         mThreadSocket = null;
                         continue;
                     }
                     if ((null == mInputStream) && (null == mOutputStream)) {
-                        LogUtil.d(TAG, "AOAManager::AOAServerThread --> client connected in AOAServerThread
");
+                        LogUtil.d(TAG + TAG_AOA, "AOAServerThread::run setup streams and start threads");
                         mSocket = mThreadSocket;
                         mSocket.setTcpNoDelay(true);
                         mInputStream = new BufferedInputStream(mSocket.getInputStream());
                         mOutputStream = new BufferedOutputStream(mSocket.getOutputStream());
 
+                        LogUtil.d(TAG + TAG_AOA, "AOAServerThread::run start AOASendThread");
                         mAOASendThread = new AOASendThread();
                         mAOASendThread.start();
+                        LogUtil.d(TAG + TAG_AOA, "AOAServerThread::run start AOARecvThread");
                         mAOARecvThread = new AOARecvThread();
                         mAOARecvThread.start();
                     } else {
-                        LogUtil.e(TAG, "AOAManager::AOAServerThread --> thread is already start");
+                        LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run threads already started, close conne
ction");
                         mThreadSocket.close();
                         mThreadSocket = null;
                     }
                     sleep(100);
                 }
-                LogUtil.e(TAG, "AOAManager::AOAServerThread --> AOAServerThread loop stop !!!");
+                LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run loop stopped");
             } catch (Exception e) {
-                LogUtil.e(TAG, "AOAManager::AOAServerThread --> Get Exception in AOAServerThread");
+                LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run exception: " + e.getMessage());
             }
 
-            LogUtil.e(TAG, "AOAManager::AOAServerThread --> AOAServerThread exit !!!");
+            LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run thread exit");
         }
     }
 
@@ -370,18 +396,19 @@ public class AOAManager {
         private final byte[] buffer = new byte[AOA_BUF_SIZE];
 
         public AOASendThread() {
-            LogUtil.d(TAG, "AOAManager::AOASendThread Created");
+            LogUtil.d(TAG + TAG_AOA, "AOASendThread::init created");
             setName(AOA_SEND_THREAD_NAME);
             isRunning = true;
         }
 
         public void cancel() {
+            LogUtil.d(TAG + TAG_AOA, "AOASendThread::cancel stop running");
             isRunning = false;
         }
 
         public int readData(byte[] data, int offset, int len) {
             if (mAOAServerThread.mInputStream == null) {
-                LogUtil.e(TAG, "AOAManager::AOASendThread --> mInputStream is null");
+                LogUtil.e(TAG + TAG_AOA, "AOASendThread::readData mInputStream is null");
                 return -1;
             }
 
@@ -389,7 +416,7 @@ public class AOAManager {
             try {
                 ret = mAOAServerThread.mInputStream.read(data, offset, len);
             } catch (IOException e) {
-                LogUtil.e(TAG, "AOAManager::AOASendThread --> readData IOException");
+                LogUtil.e(TAG + TAG_AOA, "AOASendThread::readData IOException: " + e.getMessage());
                 return -2;
             }
 
@@ -398,15 +425,17 @@ public class AOAManager {
 
         public int writeData(byte[] data, int offset, int len) {
             if (mAOAServerThread.mOutputStream == null) {
-                LogUtil.e(TAG, "AOAManager::AOASendThread --> mOutputStream is null");
+                LogUtil.e(TAG + TAG_AOA, "AOASendThread::writeData mOutputStream is null");
                 return -1;
             }
 
             try {
                 mAOAServerThread.mOutputStream.write(data, offset, len);
                 mAOAServerThread.mOutputStream.flush();
+                if (CHECK_AOA_MANAGER)
+                    LogUtil.v(TAG + TAG_AOA, "AOASendThread::writeData ---> USB to Socket, len=" + len);
             } catch (IOException e) {
-                LogUtil.e(TAG, "AOAManager::AOASendThread --> writeData IOException");
+                LogUtil.e(TAG + TAG_AOA, "AOASendThread::writeData IOException: " + e.getMessage());
                 return -2;
             }
 
@@ -415,24 +444,25 @@ public class AOAManager {
 
         @Override
         public void run() {
+            LogUtil.d(TAG + TAG_AOA, "AOASendThread::run start");
             while (mSocket != null && isRunning) {
                 if (!mSocket.isConnected()) {
-                    LogUtil.e(TAG, "AOAManager::AOASendThread --> socket is disconnected when read data");
+                    LogUtil.e(TAG + TAG_AOA, "AOASendThread::run socket disconnected");
                     break;
                 }
 
                 Arrays.fill(buffer, (byte) 0x00);
                 int len = readData(buffer, 0, AOA_BUF_SIZE);
                 if (len < 0) {
-                    LogUtil.e(TAG, "AOAManager::AOASendThread --> read from socket fail, ret: " + len);
+                    LogUtil.e(TAG + TAG_AOA, "AOASendThread::run read socket fail, ret=" + len);
                     break;
                 }
                 if (CHECK_AOA_MANAGER)
-                    LogUtil.v(TAG, "AOAManager::AOASendThread --> read from socket end, send to usb, len: "
 + len);
+                    LogUtil.v(TAG + TAG_AOA, "AOASendThread::run ---> Socket to USB, len=" + len);
 
                 int ret = mDeviceManager.bulkTransferOut(buffer, len);
                 if (ret < 0) {
-                    LogUtil.e(TAG, "AOAManager::AOASendThread --> bulkTransferOut fail, ret: " + ret);
+                    LogUtil.e(TAG + TAG_AOA, "AOASendThread::run USB bulkTransferOut fail, ret=" + ret + ",
 errorCount=" + errorCount);
                     if (errorCount > MAX_ERROR_COUNT) {
                         errorCount = 0;
                         break;
@@ -451,7 +481,7 @@ public class AOAManager {
             if (mAOAServerThread != null) {
                 mAOAServerThread.closeSocket();
             }
-            LogUtil.d(TAG, "AOAManager::AOASendThread -->  exit !!!");
+            LogUtil.d(TAG + TAG_AOA, "AOASendThread::run exit");
         }
     }
 
@@ -461,27 +491,29 @@ public class AOAManager {
         private final byte[] buffer = new byte[AOA_BUF_SIZE];
 
         public AOARecvThread() {
-            LogUtil.d(TAG, "AOAManager::AOARecvThread -->  Created");
+            LogUtil.d(TAG + TAG_AOA, "AOARecvThread::init created");
             setName(AOA_RECV_THREAD_NAME);
             isRunning = true;
         }
 
         public void cancel() {
+            LogUtil.d(TAG + TAG_AOA, "AOARecvThread::cancel stop running");
             isRunning = false;
         }
 
         @Override
         public void run() {
+            LogUtil.d(TAG + TAG_AOA, "AOARecvThread::run start");
             while (mSocket != null && isRunning) {
                 if (!mSocket.isConnected()) {
-                    LogUtil.e(TAG, "AOAManager::AOARecvThread --> read data cancled");
+                    LogUtil.e(TAG + TAG_AOA, "AOARecvThread::run socket disconnected");
                     break;
                 }
 
                 Arrays.fill(buffer, (byte) 0x00);
                 int len = mDeviceManager.bulkTransferIn(buffer, AOA_BUF_SIZE);
                 if (len < 0) {
-                    LogUtil.e(TAG, "AOAManager::AOARecvThread --> bulkTransferIn fail");
+                    LogUtil.e(TAG + TAG_AOA, "AOARecvThread::run USB bulkTransferIn fail, errorCount=" + er
rorCount);
                     if (errorCount > MAX_ERROR_COUNT) {
                         errorCount = 0;
                         break;
@@ -500,22 +532,22 @@ public class AOAManager {
                     errorCount = 0;
                 }
                 if (CHECK_AOA_MANAGER)
-                    LogUtil.v(TAG, "AOAManager::AOARecvThread --> read from usb end, send to socket, len: "
 + len);
+                    LogUtil.v(TAG + TAG_AOA, "AOARecvThread::run <--- USB to Socket, len=" + len);
                 try {
                     int ret = mAOASendThread.writeData(buffer, 0, len);
                     if (ret < 0) {
-                        LogUtil.e(TAG, "AOAManager::AOARecvThread --> write to socket fail, ret: " + ret);
+                        LogUtil.e(TAG + TAG_AOA, "AOARecvThread::run write socket fail, ret=" + ret);
                         break;
                     }
                 } catch (NullPointerException e) {
-                    LogUtil.e(TAG, "AOAManager::AOARecvThread --> null");
+                    LogUtil.e(TAG + TAG_AOA, "AOARecvThread::run mAOASendThread is null");
                 }
             }
 
             if (mAOAServerThread != null) {
                 mAOAServerThread.closeSocket();
             }
-            LogUtil.d(TAG, "AOAManager::AOARecvThread --> exit !!!");
+            LogUtil.d(TAG + TAG_AOA, "AOARecvThread::run exit");
         }
     }
 }

```

---

基于对 avslink 子模块最近提交的分析，以下是我发现的问题和分析：

## avslink 子模块最近提交分析

最近的提交信息：
- 提交哈希：92f2469
- 作者：sunwenjun
- 提交信息："临时修改开关蓝牙后，iOS 没有重新校准导致的不能点击的问题。"

### 主要变更内容

1. **日志增强和格式标准化**：
   - 添加了专用的日志标签（TAG_AOA 和 TAG_EAP）用于区分不同模块的日志
   - 增加了方法名前缀（例如 "AOACheckDevice::"）以便于追踪调用链
   - 统一了日志格式（参数名=值）
   - 添加了更多的日志点以便于调试

2. **代码文档改进**：
   - 为 AOACheckDevice 和 EAPCheckDevice 中的一些方法添加了详细注释
   - 例如 `resume()` 方法现在有详细的调用时机和使用场景说明

3. **设备类型转换为字符串**：
   - 添加了 `getDeviceTypeString()` 方法以将设备类型的数字转换为可读的字符串
   - 这提高了日志的可读性

### 潜在问题

1. **日志过多**：
   - 添加了大量新的日志输出，特别是在关键路径上
   - 可能导致日志文件快速增长，影响性能或存储

2. **没有修复 build.gradle 路径问题**：
   - 之前分析中提到的绝对路径问题在此次提交中未解决
   ```gradle
   java.srcDirs += "/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/link-hid/avslinkhid/src/main/java"
   ```

3. **依赖注释问题**：
   - 提交未解决之前提到的依赖问题，linkhid 依赖仍然被注释掉：
   ```gradle
   //    if (file("../../link-hid/avslinkhid").exists()  && SDK_TEST != "true") {
   //        implementation project(path: ':avslinkhid')
   //    } else {
   //        implementation "com.autoai.avs.link:linkhid:${LINK_HID_VERSION}"
   //    }
   ```

4. **提交信息与实际改动不完全匹配**：
   - 提交信息提到"临时修改开关蓝牙后，iOS 没有重新校准导致的不能点击的问题"
   - 但代码变更主要是日志增强和格式改进，没有明显解决蓝牙校准问题的逻辑修改
   - 可能实际的问题修复隐藏在日志改进中，但不够明显

5. **"dirty" 子模块状态**：
   - 主项目引用显示该子模块处于 "dirty" 状态，说明还有未提交的更改
   - 这可能意味着修复尚未完成或有其他未提交的更改

### 总结建议

1. **完善修复文档**：
   - 提交信息应更明确地描述实际的问题修复机制
   - 考虑添加注释说明具体的问题修复点

2. **解决构建问题**：
   - 修复绝对路径问题，使用相对路径
   - 解决依赖注释问题，确保项目能正确构建

3. **优化日志输出**：
   - 考虑减少非关键路径上的详细日志
   - 保留关键错误和状态变更的日志点

4. **提交所有更改**：
   - 处理子模块中的所有更改，消除 "dirty" 状态
   - 确保所有修复都被正确提交

5. **测试蓝牙校准功能**：
   - 确保提交确实解决了蓝牙校准问题
   - 添加测试用例验证修复效果

这次提交主要是增强了日志和改进了代码文档，但没有明显解决提交信息中提到的蓝牙校准问题。建议完善文档并进行更全面的测试，以确保修复是有效的。

---


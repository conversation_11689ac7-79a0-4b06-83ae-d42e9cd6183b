<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:layout_margin="15dp"
  android:background="@drawable/background_cron"
  android:backgroundTint="@color/alert"
  android:elevation="8dp"
  android:gravity="center"
  android:orientation="vertical"
  android:paddingStart="15dp"
  android:paddingTop="10dp"
  android:paddingEnd="15dp"
  android:paddingBottom="10dp">

  <TextView
    android:id="@+id/text"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:gravity="center"
    android:text="@string/main_float_permission"
    android:textColor="@color/onCardBackground"
    android:textSize="@dimen/middleFont"
    android:textStyle="bold" />

  <GridLayout
    android:id="@+id/button_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    android:columnCount="3"
    android:paddingStart="12dp"
    android:paddingTop="12dp"
    android:paddingEnd="12dp">

    <Button
      android:id="@+id/button_go_to_set"
      android:layout_width="0dp"
      android:layout_height="wrap_content"
      android:layout_columnWeight="9"
      android:background="@drawable/background_cron"
      android:backgroundTint="@color/button"
      android:gravity="center"
      android:text="@string/main_float_permission_button"
      android:textColor="@color/onButton"
      android:textSize="@dimen/smallFont" />

    <Space android:layout_columnWeight="1" />

    <Button
      android:id="@+id/button_always_full_mode"
      android:layout_width="0dp"
      android:layout_height="wrap_content"
      android:layout_columnWeight="9"
      android:background="@drawable/background_cron"
      android:backgroundTint="@color/button"
      android:gravity="center"
      android:text="@string/main_no_float_mode_button"
      android:textColor="@color/onButton"
      android:textSize="@dimen/smallFont" />

  </GridLayout>

</LinearLayout>
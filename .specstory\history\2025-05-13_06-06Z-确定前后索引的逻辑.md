<!-- Generated by SpecStory -->

# 确定前后索引的逻辑 (2025-05-13 06:06Z)

_**User**_

根据index 所在 yStepsList 所在区间，确定前两个索引和后两个索引。

---

_**Assistant**_

我需要更新 `getYByX` 方法中寻找最近点的逻辑，使其根据 index 在 yStepsList 中的位置来选择最近的前两个和后两个索引。

---

```diff
          }
          
-         // 如果索引恰好在yStepsList中，直接返回对应值
-         if (yStepsList.contains(index) && yStepsList.indexOf(index) < yList.size()) {
-             return yList.get(yStepsList.indexOf(index));
-         }
-         
-         // 自适应加权平滑算法
          // 1. 找到最近的几个已知点
          List<Integer> nearestIndices = new ArrayList<>();
          List<Float> weights = new ArrayList<>();
          
-         // 确定参与计算的最近点数量
-         int pointsToUse = Math.min(4, yList.size());
-         
-         // 找到要使用的最近点索引
-         for (int i = 0; i < pointsToUse; i++) {
+         // 寻找index在yStepsList中的位置
+         int position = -1;
+         for (int i = 0; i < yStepsList.size(); i++) {
+             if (yStepsList.get(i) == index) {
+                 position = i;
+                 break;
+             }
+         }
+         
+         // 如果找不到精确匹配，找到最接近的位置
+         if (position == -1) {
+             for (int i = 0; i < yStepsList.size() - 1; i++) {
+                 if ((yStepsList.get(i) > index && yStepsList.get(i+1) < index) || 
+                     (yStepsList.get(i) < index && yStepsList.get(i+1) > index)) {
+                     // 找到区间位置
+                     position = (Math.abs(yStepsList.get(i) - index) <= Math.abs(yStepsList.get(i+1) - index)) ? i : i+1;
+                     break;
+                 }
+             }
+         }
+         
+         // 如果仍找不到，表示index可能在范围外，使用最近的端点
+         if (position == -1) {
+             position = (index <= yStepsList.get(0)) ? 0 : yStepsList.size() - 1;
+         }
+         
+         // 确定前两个和后两个索引
+         for (int i = Math.max(0, position - 2); i <= Math.min(yStepsList.size() - 1, position + 2); i++) {
              nearestIndices.add(i);
          }
+         
+         // 确保至少有4个点（如果可能），在边界情况下补充
+         while (nearestIndices.size() < 4 && nearestIndices.size() < yList.size()) {
+             if (nearestIndices.get(0) > 0) {
+                 nearestIndices.add(0, nearestIndices.get(0) - 1);
+             } else if (nearestIndices.get(nearestIndices.size() - 1) < yList.size() - 1) {
+                 nearestIndices.add(nearestIndices.get(nearestIndices.size() - 1) + 1);
+             } else {
+                 break; // 无法再添加更多点
+             }
+         }
          
          // 2. 计算每个点的权重（距离越近权重越高）
```

---

我注意到更新中删除了一段直接返回精确匹配值的代码，应该保留这个逻辑。让我修复这个问题：

---

```diff
          }
          
+         // 如果索引恰好在yStepsList中，直接返回对应值
+         if (yStepsList.contains(index) && yStepsList.indexOf(index) < yList.size()) {
+             return yList.get(yStepsList.indexOf(index));
+         }
+         
+         // 自适应加权平滑算法
          // 1. 找到最近的几个已知点
          List<Integer> nearestIndices = new ArrayList<>();
```

---

现在逻辑更完整了。改进后的 `getYByX` 方法能够：

1. 如果 index 恰好在 yStepsList 中有精确匹配，直接返回对应的 Y 值
2. 否则，根据 index 在 yStepsList 中的位置查找：
   - 确定 index 所在或最接近的位置
   - 选择这个位置周围的前后两个索引点（共5个点，包括位置本身）
   - 如果边界处理不足4个点，尝试补充到4个点
3. 基于这些最近的点，使用距离加权计算出平滑的 Y 值

这样在插值计算中可以更精确地捕捉到当前位置附近的曲线变化趋势，而不是简单地使用前几个点，提高了插值精度。

---


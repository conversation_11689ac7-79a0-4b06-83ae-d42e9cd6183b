
## HEADERS

## TECH STACK

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

A new document file with the function name has been requested to be created in the docs directory.

## CODING STANDARDS

- **Use Constants**: Always use constants defined in `HidConstants.java` instead of hardcoded values for HID-related status codes. This makes the code more maintainable and less error-prone.
- **Bluetooth Pairing State**: Use `isBTPairedWithPhone` to determine the Bluetooth pairing state.
- **Brace Alignment and Closure**: Ensure that all braces are properly closed and aligned, especially within case statements. Indent case statements properly to improve readability and prevent missing closing braces.

## DEBUGGING

- **Log Simplification**: Simplify logs to clearly indicate the code branch executed and print critical variables. This aids in quickly understanding the execution flow and identifying potential issues. The logging in the `checkAndUpdateBluetoothState()` method should be more concise while still capturing the execution flow and key variables.
## WORKFLOW & RELEASE RULES

### Bluetooth Icon and Calibration Button Logic

#### Display Logic Requirements

##### 1. Unconnected State
- **Requirement**: If not connected (`LinkSdkModel.INSTANCE.isConnected()` returns false), do not display the Bluetooth icon.
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Trigger Condition**: Check Bluetooth status and find that it is not connected.
  - **Detailed Trigger Condition**:
    - `LinkSdkModel.INSTANCE.isConnected()` returns false
    - Regardless of the device platform (iOS, Android, or HarmonyOS), the Bluetooth icon will not be displayed.

##### 2. Normal State (NORMAL)
- **Requirement**: When Bluetooth is normally connected and there are no abnormalities, do not display the Bluetooth icon.
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Trigger Conditions**:
  - Bluetooth pairing is successful.
  - iOS device calibration is successful.
  - Bluetooth device matches correctly.
  - **Detailed Trigger Conditions**:
    - **iOS Platform**: Bluetooth is paired and connected successfully, and the calibration status is `CalibrationStatus.SUCCESS`.
    - **Android Platform**: Bluetooth is paired and connected successfully, and it matches the Link device (confirmed via `SdkViewModel.getInstance().updateBTPairedWithPhone()`).
    - **HarmonyOS Platform**: Bluetooth is paired and connected successfully, and the Bluetooth device name matches `blueToothName`.

##### 3. Bluetooth Not Connected (BT_NOT_CONNECTED)
- **Requirement**: Display the Bluetooth not connected icon and prompt the user.
- **UI Actions**:
  - Display Bluetooth icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set Bluetooth icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - Display Toast prompt: "Please connect to Bluetooth"
- **Trigger Condition**: No connected Bluetooth device is detected.
  - **Detailed Trigger Conditions**:
    - **All Platforms**: `HidUtil.getConnectedBluetoothDevices()` returns an empty collection.
    - **iOS Platform (Specific Case)**: This state is also triggered if the calibration status is `CalibrationStatus.BT_NOT_CONNECTED`.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 4. Bluetooth Disconnected (BT_DISCONNECTED)
- **Requirement**: Display the Bluetooth disconnected icon and prompt the user.
- **UI Actions**:
  - Display Bluetooth icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set Bluetooth icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - If already connected, display Toast prompt: "Bluetooth connection has been disconnected"
- **Trigger Condition**: Bluetooth connection status changes from connected to disconnected.
  - **Detailed Trigger Conditions**:
    - **All Platforms**: Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`.
    - **iOS Platform**: Bluetooth is disconnected, but interconnection still exists.
    - **Android Platform**: Bluetooth is disconnected, but interconnection still exists.
    - **HarmonyOS Platform**: Bluetooth is disconnected, but interconnection still exists, triggered via the `onDisconnected` callback.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 5. Bluetooth Device Mismatched (BT_DEVICE_MISMATCHED)
- **Requirement**: Display the Bluetooth icon and prompt the user that the wrong device is connected.
- **UI Actions**:
  - Display Bluetooth icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set Bluetooth icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2)`
  - Display Toast prompt: "May cause abnormal remote control phone sound"
- **Trigger Conditions**:
  - Mainly for HarmonyOS systems.
  - Detect that the Bluetooth device name does not match the expected name.
  - **Detailed Trigger Conditions**:
    - **HarmonyOS Platform**: Bluetooth is connected, but the device name does not match `blueToothName`.
    - **Detection Logic**: Iterate through the device collection returned by `HidUtil.getConnectedBluetoothDevices()` and check if the device name matches `blueToothName`.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 6. iOS Needs Calibration (IOS_NEEDS_CALIBRATION)
- **Requirement**: Only display the calibration icon on iOS devices.
- **UI Actions**:
  - Display calibration icon: `mBlueToothOrCalibration.setVisibility(View.VISIBLE)`
  - Set calibration icon: `mBlueToothOrCalibration.setImageResource(R.mipmap.calibration_icon)`
- **Trigger Conditions**:
  - Device type is iOS.
  - Calibration status is failed, APP is not in the foreground, or Bluetooth is not connected.
  - **Detailed Trigger Conditions**:
    - **iOS Platform Only**: `isIOSDevice()` returns true.
    - Calibration status is one of the following:
      - `CalibrationStatus.FAILED`: Calibration failed.
      - `CalibrationStatus.APP_NOT_FOREGROUND`: The mobile app is not in the foreground.
      - `CalibrationStatus.BT_NOT_CONNECTED`: Bluetooth is not connected.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 7. iOS Calibration Failed
- **Requirement**: Display the calibration failed interface and provide a re-calibration option.
- **UI Actions**:
  - Display calibration icon.
  - Display calibration failed interface: `popCalibrationLin.setVisibility(View.VISIBLE)`
  - Display failure icon: `popCalibrationImg.setImageResource(R.mipmap.calibration_failed_img)`
  - Display failure title: `popCalibrationTitle.setText("Calibration failed")`
  - Provide a 5-second countdown re-calibration button.
- **Trigger Condition**: Receive calibration failed callback (`CalibrationStatus.FAILED`).
  - **Detailed Trigger Conditions**:
    - **iOS Platform Only**: `isIOSDevice()` returns true.
    - Calibration result of `1` (corresponding to `CalibrationStatus.FAILED`) is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`.
    - **Prerequisite**: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true).

##### 8. iOS APP Not in Foreground
- **Requirement**: Display the calibration icon and prompt the user to open the phone APP.
- **UI Actions**:
  - Display calibration icon.
  - Display Toast prompt: "Please open the WeLink app on your phone"
- **Trigger Condition**: Receive APP not in foreground callback (`CalibrationStatus.APP_NOT_FOREGROUND`).

#### Button Click Behavior

##### 9. Click Bluetooth Not Connected Icon
- **Requirement**: Prompt the user to connect to Bluetooth.
- **UI Action**: Display Toast prompt: "Please connect to Bluetooth"
- **Trigger Condition**: Click the icon when the current state is `BT_NOT_CONNECTED`.

##### 10. Click Bluetooth Disconnected Icon
- **Requirement**: Prompt the user that Bluetooth is disconnected.
- **UI Action**: Display Toast prompt: "Bluetooth connection has been disconnected"
- **Trigger Condition**: Click the icon when the current state is `BT_DISCONNECTED`.

##### 11. Click Bluetooth Device Mismatched Icon
- **Requirement**: Prompt the user to switch Bluetooth.
- **UI Action**: Display Toast prompt: "May cause abnormal remote control phone sound"
- **Trigger Condition**: Click the icon when the current state is `BT_DEVICE_MISMATCHED`.

##### 12. Click iOS Calibration Icon
- **Requirement**: Hide the button and start calibration.
- **UI Actions**:
  - Hide the calibration button: `mBlueToothOrCalibration.setVisibility(View.INVISIBLE)`
  - Start calibration process

- **Trigger Condition**: Click the icon when the current state is `IOS_NEEDS_CALIBRATION`.

#### Display Logic Requirements - Detailed

##### 1. Unconnected State
- **Requirement**: If not connected, do not display the Bluetooth icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Condition**:
  - `LinkSdkModel.INSTANCE.isConnected()` returns false
  - Regardless of the device platform (iOS, Android, or HarmonyOS), the Bluetooth icon will not be displayed

##### 2. Normal State (NORMAL)
- **Requirement**: Bluetooth normally connected and no abnormalities, do not display the Bluetooth icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - **iOS Platform**: Bluetooth is paired and connected successfully, and the calibration status is `CalibrationStatus.SUCCESS`
  - **Android Platform**: Bluetooth is paired and connected successfully, and it matches the Link device (confirmed via `SdkViewModel.getInstance().updateBTPairedWithPhone()`)
  - **HarmonyOS Platform**: Bluetooth is paired and connected successfully, and the Bluetooth device name matches `blueToothName`

##### 3. Bluetooth Not Connected (BT_NOT_CONNECTED)
- **Requirement**: Display the Bluetooth not connected icon and prompt the user
- **UI Action**: Display Bluetooth icon and prompt the user to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - **All Platforms**: `HidUtil.getConnectedBluetoothDevices()` returns an empty collection
  - **iOS Platform特例**: If the calibration status is `CalibrationStatus.BT_NOT_CONNECTED`, this state is also triggered
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 4. Bluetooth Disconnected (BT_DISCONNECTED)
- **Requirement**: Display the Bluetooth disconnected icon and prompt the user
- **UI Action**: Display Bluetooth icon and prompt the user that the Bluetooth connection has been disconnected
- **Detailed Trigger Conditions**:
  - **All Platforms**: Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`
  - **iOS Platform**: Bluetooth is disconnected but interconnection still exists
  - **Android Platform**: Bluetooth is disconnected but interconnection still exists
  - **HarmonyOS Platform**: Bluetooth is disconnected but interconnection still exists, triggered via the `onDisconnected` callback
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 5. Bluetooth Device Mismatched (BT_DEVICE_MISMATCHED)
- **Requirement**: Display the Bluetooth icon and prompt the user that the wrong device is connected
- **UI Action**: Display Bluetooth icon and prompt the user that it may cause abnormal remote control phone sound
- **Detailed Trigger Conditions**:
  - **HarmonyOS Platform**: Bluetooth is connected, but the device name does not match `blueToothName`
  - Detection logic: Iterate through the device collection returned by `HidUtil.getConnectedBluetoothDevices()` and check if the device name matches `blueToothName`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 6. iOS Needs Calibration (IOS_NEEDS_CALIBRATION)
- **Requirement**: Only display the calibration icon on iOS devices
- **UI Action**: Display calibration icon
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: `isIOSDevice()` returns true
  - Calibration status is one of the following:
    - `CalibrationStatus.FAILED`: Calibration failed
    - `CalibrationStatus.APP_NOT_FOREGROUND`: The mobile APP is not in the foreground
    - `CalibrationStatus.BT_NOT_CONNECTED`: Bluetooth is not connected
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 7. iOS Calibration Failed
- **Requirement**: Display the calibration failed interface and provide a re-calibration option
- **UI Action**: Display calibration icon and calibration failed interface
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: `isIOSDevice()` returns true
  - Through `LinkSdkModel.INSTANCE.registerPopHidCheckCallback` receive calibration result as `1` (corresponding to `CalibrationStatus.FAILED`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 8. iOS APP Not in Foreground
- **Requirement**: Display the calibration icon and prompt the user to open the phone APP
- **UI Action**: Display calibration icon and Toast prompt
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: `isIOSDevice()` returns true
  - Through `LinkSdkModel.INSTANCE.registerPopHidCheckCallback` receive calibration result as `3` (corresponding to `CalibrationStatus.APP_NOT_FOREGROUND`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

#### Button Click Behavior - Detailed

##### 9. Click Bluetooth Not Connected Icon
- **Requirement**: Prompt the user to connect to Bluetooth
- **UI Action**: Display Toast prompt: "Please connect to Bluetooth"
- **Detailed Trigger Conditions**:
  - **All Platforms**: Current state is `BT_NOT_CONNECTED`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

##### 10. Click Bluetooth Disconnected Icon
- **Requirement**: Prompt the user that Bluetooth is disconnected
- **UI Action**: Display Toast prompt: "Bluetooth connection has been disconnected"
- **Detailed Trigger Conditions**:
  - **All Platforms**: Current state is `BT_DISCONNECTED`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

##### 11. Click Bluetooth Device Mismatched Icon
- **Requirement**: Prompt the user to switch Bluetooth
- **UI Action**: Display Toast prompt: "May cause abnormal remote control phone sound"
- **Detailed Trigger Conditions**:
  - **Mainly for HarmonyOS Platform**: Current state is `BT_DEVICE_MISMATCHED`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

##### 12. Click iOS Calibration Icon
- **Requirement**: Hide the button and start calibration
- **UI Action**: Hide the calibration button and begin calibration
- **Detailed Trigger Conditions**:
  - **iOS Platform Only**: Current state is `IOS_NEEDS_CALIBRATION`
  - User clicks the `mBlueToothOrCalibration` button
  - `lockViewVisible()` returns false (not in lock screen or streaming state)

#### Platform-Specific Combined Conditions

##### 13. iOS Platform - Normal State
- **Requirement**: Do not display any icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - `platform.contains("ios")` is true
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Calibration status is `CalibrationStatus.SUCCESS` (calibration result `0` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 14. iOS Platform - Needs Calibration
- **Requirement**: Display calibration icon
- **UI Action**: Display calibration icon
- **Detailed Trigger Conditions**:
  - `platform.contains("ios")` is true
  - Bluetooth is paired and connected successfully
  - Calibration status is one of the following:
    - `CalibrationStatus.FAILED` (calibration result `1` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
    - `CalibrationStatus.APP_NOT_FOREGROUND` (calibration result `3` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
    - `CalibrationStatus.BT_NOT_CONNECTED` (calibration result `2` is received via `LinkSdkModel.INSTANCE.registerPopHidCheckCallback`)
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 15. iOS Platform - Bluetooth Not Connected
- **Requirement**: Display the Bluetooth not connected icon
- **UI Action**: Display Bluetooth icon and prompt to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - `platform.contains("ios")` is true
  - `HidUtil.getConnectedBluetoothDevices()` returns an empty collection
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 16. Android Platform - Normal State
- **Requirement**: Do not display any icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - `platform.contains("android")` is true
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Bluetooth matching with Link device is confirmed via `SdkViewModel.getInstance().updateBTPairedWithPhone()`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 17. Android Platform - Bluetooth Not Connected or Disconnected
- **Requirement**: Display the Bluetooth not connected/disconnected icon
- **UI Action**: Display Bluetooth icon and prompt to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - `platform.contains("android")` is true
  - `HidUtil.getConnectedBluetoothDevices()` returns an empty collection or Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 18. HarmonyOS Platform - Normal State
- **Requirement**: Do not display any icon
- **UI Action**: `mBlueToothOrCalibration.setVisibility(View.GONE)`
- **Detailed Trigger Conditions**:
  - `!isIOSDevice() && !isAndroidDevice()` is true (HarmonyOS System)
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Iterating through the connected Bluetooth devices, there exists a device name matching `blueToothName`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 19. HarmonyOS Platform - Bluetooth Device Mismatched
- **Requirement**: Display the Bluetooth device mismatched icon
- **UI Action**: Display Bluetooth icon and prompt that it may cause abnormal remote control
- **Detailed Trigger Conditions**:
  - `!isIOSDevice() && !isAndroidDevice()` is true (HarmonyOS System)
  - Bluetooth is paired and connected successfully (`HidUtil.getConnectedBluetoothDevices()` is not empty)
  - Iterating through the connected Bluetooth devices, no device name matches `blueToothName`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

##### 20. HarmonyOS Platform - Bluetooth Not Connected or Disconnected
- **Requirement**: Display the Bluetooth not connected/disconnected icon
- **UI Action**: Display Bluetooth icon and prompt to connect to Bluetooth
- **Detailed Trigger Conditions**:
  - `!isIOSDevice() && !isAndroidDevice()` is true (HarmonyOS System)
  - `HidUtil.getConnectedBluetoothDevices()` returns an empty collection or Bluetooth disconnection is detected via `HidUtil.BluetoothStateListener.onDisconnected`
  - Prerequisite: Already interconnected (`LinkSdkModel.INSTANCE.isConnected()` is true)

#### Additional Rules based on Issue Analysis (2025-06-21)

These rules are derived from the "Bluetooth calibration issue analysis and solution" document dated 2025-06-21. They address the incomplete handling of Bluetooth pairing status on Android/HarmonyOS platforms.

##### 21. Android Platform - Bluetooth Matching Check
- **Requirement**: In `checkAndUpdateBluetoothState()`, proactively check Bluetooth matching status for Android.
- **Implementation**:
  - Inside `checkAndUpdateBluetoothState()` in `PopWinService.java`, when `isAndroidDevice()` returns true:
    - Call `SdkViewModel.getInstance().isBTPairedWithPhone()` to get the Bluetooth matching status.
    - If `isBTPairedWithPhone()` returns false, update the Bluetooth state to `BT_DEVICE_MISMATCHED` using `bluetoothStateManager.updateState(BluetoothAbnormalState.BT_DEVICE_MISMATCHED)`.

##### 22. SdkViewModel - Bluetooth Pairing Status Method
- **Requirement**: `SdkViewModel` must have an `isBTPairedWithPhone()` method.
- **Implementation**:
  ```java
  public boolean isBTPairedWithPhone() {
      // Returns the currently saved Bluetooth pairing status.
      // This status should be updated in the updateBTPairedWithPhone() method.
      return btPairedWithPhone;
  }
  ```

##### 23. Bluetooth Pairing Status Change Trigger
- **Requirement**: When the Bluetooth pairing status changes (e.g., through a callback from the phone), a re-check of the Bluetooth state must be triggered.
- **Implementation**:
  - In the code that receives the `updateBTPairedWithPhone` callback (e.g., in `PopWinService.java`):
    ```java
    public void onBTPairedStatusChanged(boolean isPaired) {
        SdkViewModel.getInstance().updateBTPairedWithPhone(isPaired);
        // Trigger a Bluetooth state re-check
        checkAndUpdateBluetoothState();
    }
    ```

##### 24. Correct Bluetooth Disconnection Handling Across Platforms

- **Requirement**: Ensure consistent handling of Bluetooth disconnection across all platforms (iOS, Android, HarmonyOS) in the `btConnectedListener.onDisconnected()` method. The `onDisconnected()` callback should trigger a Bluetooth state re-check for all platforms, not just HarmonyOS.
- **Implementation**:

  - Modify the `btConnectedListener.onDisconnected()` method to include a Bluetooth state re-check for all platforms.
    ```java
    @Override
    public void onDisconnected() {
        // Common disconnection handling for all platforms
        checkAndUpdateBluetoothState();
    }
    ```

##### 25. Unified Bluetooth Disconnection Handling (2025-06-21)
- **Requirement**: Modify the `btConnectedListener.onDisconnected()` method to handle Bluetooth disconnection events uniformly across all platforms.
- **Implementation**:
  ```java
  private BluetoothStateListener btConnectedListener = new BluetoothStateListener() {
      @Override
      public void onDisconnected() {
          // Remove platform restrictions and handle Bluetooth disconnection events uniformly across all platforms
          onBluetoothConnectionChanged(false);
      }

      @Override
      public void onConnected() {
          onBluetoothConnectionChanged(true);
      }
  };
  ```

##### 26. Bluetooth Connection Change Handling (2025-06-21)
- **Requirement**: Ensure that all platforms correctly update their status when Bluetooth is disconnected in the `onBluetoothConnectionChanged()` method.
- **Implementation**:
  ```java
  private void onBluetoothConnectionChanged(boolean isConnected) {
      if (!isConnected) {
          // When Bluetooth is disconnected, uniformly set the status to BT_DISCONNECTED for all platforms
          bluetoothStateManager.updateState(BluetoothAbnormalState.BT_DISCONNECTED);
          
          // If already interconnected, display a prompt
          if (LinkSdkModel.INSTANCE.isConnected()) {
              showToast(context.getString(R.string.bluetooth_disconnected));
          }
      } else {
          // When Bluetooth is connected, trigger a status check
          checkAndUpdateBluetoothState();
      }
  }
  ```

##### 27. Consistent Bluetooth Disconnection Status Logic (2025-06-21)
- **Requirement**: Ensure that the logic in the `checkAndUpdateBluetoothState()` method remains consistent with the unified disconnection handling. Specifically, ensure that all platforms use the same logic to determine Bluetooth disconnection status.
- **Implementation**:
  ```java
  private void checkAndUpdateBluetoothState() {
      // Other existing code...
      
      // Ensure that all platforms use the same logic to determine Bluetooth disconnection status
      if (btDevices.isEmpty()) {
          // For all platforms, uniformly display as not connected when the Bluetooth device list is empty
          bluetoothStateManager.updateState(BluetoothAbnormalState.BT_NOT_CONNECTED);
          return;
      }
      
      // Other existing code...
  }
  ```

##### 28. Bluetooth State Listener Registration (2025-06-21)
- **Requirement**: Ensure that the Bluetooth state listener is registered for all platforms to correctly receive disconnection events.
- **Implementation**:
  ```java
  private void initBluetoothListener() {
      // Ensure that the Bluetooth state listener is registered for all platforms
      HidUtil.registerBluetoothStateListener(btConnectedListener);
  }
  ```

##### 29. Lifecycle Management of Bluetooth Listener (2025-06-21)
- **Requirement**: Properly register and unregister the Bluetooth listener in the appropriate lifecycle methods to prevent memory leaks and ensure proper functionality.
- **Implementation**:
  ```java
  @Override
  public void onCreate() {
      super.onCreate();
      // Other initialization code...
      initBluetoothListener();
  }

  @Override
  public void onDestroy() {
      super.onDestroy();
      // Unregister the listener
      HidUtil.unregisterBluetoothStateListener(btConnectedListener);
      // Other cleanup code...
  }
  ```

#### Additional Rules based on User Request (2025-06-21)

The following rules define how to handle Bluetooth device name settings and matching.

##### 30. LinkSdkModel - Paired Bluetooth Device Name Management
- **Requirement**: `LinkSdkModel` must provide methods to set and get the paired Bluetooth device name.
- **Implementation**:
  ```kotlin
  class LinkSdkModel {
      private var pairedBluetoothDeviceName: String = ""

      fun setPairedBluetoothDeviceName(deviceName: String) {
          pairedBluetoothDeviceName = deviceName
      }

      fun getPairedBluetoothDeviceName(): String {
          return pairedBluetoothDeviceName
      }

      // Existing methods...
  }
  ```

##### 31. BluetoothStateManager - Use LinkSdkModel for Device Matching
- **Requirement**: `BluetoothStateManager` should use the device name from `LinkSdkModel` for Bluetooth device matching.
- **Implementation**:
  ```java
  class BluetoothStateManager {
      // Existing methods...

      public void checkAndUpdateBluetoothState() {
          String expectedDeviceName = LinkSdkModel.INSTANCE.getPairedBluetoothDeviceName();
          // Use expectedDeviceName for matching
      }
  }
  ```

##### 32. PopWinService - Update Bluetooth Device Name
- **Requirement**: `PopWinService` must update the Bluetooth device name in `LinkSdkModel`.
- **Implementation**:
  ```java
  class PopWinService {
      private String blueToothName;

      // Existing methods...

      public void onShowActionReceived(String deviceName) {
          blueToothName = deviceName;
          LinkSdkModel.INSTANCE.setPairedBluetoothDeviceName(deviceName);
      }
  }
  ```

##### 33. Synchronization of Bluetooth Device Name
- **Requirement**: Ensure that the Bluetooth device name is properly synchronized across all components that need it.
- **Implementation**:
  - When the device name is received in `PopWinService`, immediately update it in `LinkSdkModel`.
  - `BluetoothStateManager` should always retrieve the device name from `LinkSdkModel`.

#### Additional Rules based on 2025-06-21_07-06-bluetooth-calibration-icon-display-fix-proposal.md

These rules define the fix for the bluetooth calibration icon display logic.

##### 34. SdkViewModel - Bluetooth Pairing Status Update
- **Requirement**: Ensure `SdkViewModel.getInstance().updateBTPairedWithPhone()` is properly called when the callback is received.
- **Context**: `updateBTPairedWithPhone` is a callback function from the phone side after connection is established. When it's `true`, it indicates that the Bluetooth on iOS and Android devices is paired with the connected device, and this notification should be the source of truth for the pairing status.

##### 35. BluetoothStateManager - Status Handling
- **Requirement**: Modify `BluetoothStateManager` to correctly handle the Bluetooth pairing status.

##### 36. HidScreenTouchManager - Status Sharing
- **Requirement**: Make sure the Bluetooth pairing status is properly shared with `HidScreenTouchManager`.

#### Additional Rules based on Calibration Flow Analysis (2025-06-21)

These rules are derived from the iOS calibration flow analysis.

##### 37. Calibration Status Definition
- **Requirement**: Define iOS device calibration status.
- **Implementation**:
  ```java
  public enum CalibrationStatus {
      SUCCESS(0),       // 校准成功
      FAILED(1),        // 校准失败
      BT_NOT_CONNECTED(2), // 蓝牙未连接
      APP_NOT_FOREGROUND(3), // 手机app未在前台，无法校准
      NOT_CALIBRATED(-1),  // 未校准状态
      UNKNOWN(-2);      // 未知状态
  }
  ```

##### 38. Initialization After Interconnection
- **Requirement**: After successful interconnection between the car unit and the iOS device, the car unit should perform the following steps.
- **Implementation**:
  1. Initialize various components in `PopWinService.onCreate()`.
  2. Register HID callbacks in the `setPhoneScreenRotationChange()` method.
  3. Register calibration status callbacks in the `CalibrationMonitoringCallback()` method.

##### 39. Calibration Status Listening Settings
- **Requirement**: Set up the calibration status listener.
- **Implementation**:
  ```java
  private void CalibrationMonitoringCallback() {
      LinkSdkModel.INSTANCE.registerPopHidCheckCallback((isCheck) -> {
          calibrationStatus = CalibrationStatus.fromValue(isCheck);
          // Handle calibration callback
          bluetoothStateManager.onCalibrationResult(isCheck);
          
          // Handle additional UI logic for calibration failure
          if (isCheck == 1) {
              runOnUiThread(() -> {
                  skipCalibrationTimer();
                  popCalibrationContent.setVisibility(View.VISIBLE);
                  popCalibrationContent.setText(Utils.getApp().getString(R.string.do_not_touch_your_phone));
                  popRecalibrate.setVisibility(View.VISIBLE);
                  popRecalibrate.setOnClickListener(v -> {
                      popCalibrationLin.setVisibility(View.INVISIBLE);
                      popRecalibrate.setVisibility(View.GONE);
                      popCalibrationContent.setVisibility(View.GONE);
                      if (countDownTimer != null) {
                          countDownTimer.cancel();
                      }
                      LinkSdkModel.INSTANCE.startVerification();
                  });
              });
          }
      });
  }
  ```

##### 40. Bluetooth Status Check
- **Requirement**: Check Bluetooth connection status after successful interconnection.
- **Implementation**:
  ```java
  private void determineIfBluetoothIsConnected() {
      handler.postDelayed(() -> {
          bluetoothStateManager.checkAndUpdateBluetoothState();
      }, 500);
  }
  ```

##### 41. Calibration Status Processing
- **Requirement**: Process calibration results in the `onCalibrationResult` method of the `BluetoothStateManager` class.
- **Implementation**:
  ```java
  private void onCalibrationResult(int calibrationResult) {
      CalibrationStatus status = CalibrationStatus.fromValue(calibrationResult);
      
      switch (status) {
          case SUCCESS:
              // Calibration successful
              updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
              runOnUiThread(() -> {
                  popCalibrationLin.setVisibility(View.
  ```

#### Additional Rules based on iOS Calibration Flow Analysis (2025-06-21)

These rules are derived from the iOS calibration flow analysis of 2025-06-21.

##### 42. iOS Calibration Process Sequence

###### First Step: Successful Interconnection and Initial State Check
1.  The car unit and the iOS device successfully establish interconnection (`LinkSdkModel.INSTANCE.isConnected()` returns true).
2.  The car unit confirms the Bluetooth pairing status via `updateBTPairedWithPhone(true)`.
3.  `PopWinService` checks the device platform and identifies it as an iOS device.
4.  The calibration status is checked. If it is `FAILED`, `APP_NOT_FOREGROUND`, or `BT_NOT_CONNECTED`.
5.  The calibration icon is displayed (`BluetoothAbnormalState.IOS_NEEDS_CALIBRATION`).

###### Second Step: User Initiates Calibration
1.  The user clicks the calibration icon.
2.  `PopWinService.handleBluetoothButtonClick()` processes the click event.
3.  `LinkSdkModel.INSTANCE.startVerification()` is called to begin the calibration process.
4.  The retry count is reset: `mIOSCalibrationRetryTimes = 0`.
5.  `HidIosCalibrationUtil.setBreakDown(false)` is set to ensure calibration is not interrupted.
6.  `HidIosCalibrationUtil.requestCalibration()` is called to request calibration.

###### Third Step: Car Unit Requests Calibration Confirmation
1.  `BluetoothHidManager.requestCalibration()` checks the Bluetooth connection status.
2.  If Bluetooth is connected, the status is set to `HID_VERIFY_TYPE_CONNECT`.
3.  The `CalibrationCallBack.onTouchPointRequest(ON_TOUCH_POINT_REQ_SUCCESS)` callback is triggered.
4.  `SdkViewModel` receives the callback and sends a message to the iOS device: `mSdkApi.sendIosHidTouchPoint(ON_TOUCH_POINT_REQ_SUCCESS)`.
5.  The calibration state is set: `HID_VERIFY_TYPE_VERIFICATION_ING`.
6.  A 6-second timeout monitor is initiated: `hidVerifyTimeoutRunnable`.

###### Fourth Step: iOS Device Responds and Prepares
1.  The iOS device receives the calibration request.
2.  The iOS device checks the application status and Bluetooth connection.
3.  The iOS device sends a response message, which can be one of the following:
    *   `ON_TOUCH_POINT_READY(1)` + last calibration point data (if available).
    *   `ON_TOUCH_APP_FRONT(4)` (application is in the foreground, no historical calibration points).
    *   `ON_TOUCH_END_POINT(3)` (application is not in the foreground, cannot calibrate).

###### Fifth Step: Car Unit Processes iOS Response

The car unit processes the iOS device's response message based on its type:

**Scenario A - Received `ON_TOUCH_POINT_READY` (Historical Calibration Points Exist):**

1.  `onTouchPointReady(last calibration point data)` is called.
2.  Cache is cleared: `HidIosCalibrationUtil.cleanCache()`.
3.  The last calibration point is verified: `HidIosCalibrationUtil.checkPoint(obj)`.
4.  If verification is successful and not in a retry state, pre-calibration is started: `startPrepareVerification()`.
5.  If verification fails or in a retry state, formal calibration is started: `startVerification()`.

**Scenario B - Received `ON_TOUCH_APP_FRONT` (No Historical Calibration Points):**

1.  Formal calibration is started directly: `HidIosCalibrationUtil.startVerification()`.

**Scenario C - Received `ON_TOUCH_END_POINT` (Application Not in Foreground):**

1.  `onTouchSendEnd()` is called to handle calibration failure.
2.  `breakDown = true` is set to interrupt calibration.
3.  The state is set: `HID_VERIFY_TYPE_VERIFICATION_FAIL`.
4.  The failure callback is triggered: `onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL)`.

###### Pre-Calibration Process (If Historical Calibration Points Exist and Verification Passes)

**Sixth Step A: Pre-Calibration Execution**

1.  `HidIosCalibrationManager.startPrepareVerification()` is called.
2.  `
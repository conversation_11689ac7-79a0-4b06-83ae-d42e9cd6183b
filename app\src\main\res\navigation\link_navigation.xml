<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:startDestination="@id/initFragment">
    <fragment
        android:id="@+id/initFragment"
        android:name="com.autoai.wdlink.hu.view.InitFragment"
        android:label="InitFragment"
        tools:layout="@layout/fragment_init" />
    <fragment
        android:id="@+id/helpFragment"
        android:name="com.autoai.wdlink.hu.view.HelpFragment"
        android:label="HelpFragment"
        tools:layout="@layout/fragment_help" />
    <fragment
        android:id="@+id/connectingFragment"
        android:name="com.autoai.wdlink.hu.view.ConnectingFragment"
        android:label="ConnectingFragment"
        tools:layout="@layout/fragment_connecting" />
    <fragment
        android:id="@+id/connectSuccessFragment"
        android:name="com.autoai.wdlink.hu.view.SuccessFragment"
        android:label="ConnectSuccessFragment"
        tools:layout="@layout/fragment_success" />
    <fragment
        android:id="@+id/connectTimeoutFragment"
        android:name="com.autoai.wdlink.hu.view.TimeoutFragment"
        android:label="ConnectTimeoutFragment"
        tools:layout="@layout/fragment_timeout" />
    <fragment
        android:id="@+id/heartbeatErrorFragment"
        android:name="com.autoai.wdlink.hu.view.ExitFragment"
        android:label="HeartbeatErrorFragment"
        tools:layout="@layout/fragment_exit" />
    <fragment
        android:id="@+id/runningFragment"
        android:name="com.autoai.wdlink.hu.view.RunningFragment"
        android:label="RunningFragment"
        tools:layout="@layout/fragment_running" />
    <fragment
        android:id="@+id/tipsFragment"
        android:name="com.autoai.wdlink.hu.view.TipsFragment"
        android:label="tipsFragment"
        tools:layout="@layout/fragment_tips" />
    <fragment
        android:id="@+id/webHtmlFragment"
        android:name="com.autoai.wdlink.hu.view.WebHtmlFragment"
        android:label="webHtmlFragment"
        tools:layout="@layout/fragment_web_html" />

</navigation>
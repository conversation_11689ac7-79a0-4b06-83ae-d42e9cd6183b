<!-- Generated by SpecStory -->

# 蓝牙配对连接处理逻辑 (2025-06-20 07:25Z)

_**User**_

如果是这个函数回调，那么就明确是蓝牙配对连接的，只是 iOS 可能还需要 检查是否需要校准。其他安卓和鸿蒙，就直接是隐藏图标了。

---

_**Assistant**_

I'll update the `onCarBtPairedConnected` method to handle iOS calibration requirements while immediately hiding the icon for Android and HarmonyOS devices.

---



---

The `onCarBtPairedConnected` method has been updated to handle device-specific requirements:

1. For iOS devices:
   - If calibration is needed (failed, app not in foreground, or BT not connected), it shows the calibration icon
   - Otherwise, it hides the icon

2. For Android and HarmonyOS devices:
   - It immediately hides the icon when paired successfully

3. For disconnection (all devices):
   - It maintains the existing behavior for handling disconnections

This implementation follows your requirement to treat the `onCarBtPairedConnected` callback as a clear indication of successful Bluetooth pairing, with special handling only for iOS calibration needs.

---

_**User**_

蓝牙状态和校准状态相关的函数， 关键位置都加上打印呢。  tag 叫 BTPair, tag 用宏定义起来。

---

_**Assistant**_

I'll add log statements to all key Bluetooth and calibration-related functions using a consistent tag defined as a constant.

---

```diff
      private static final String TAG = "PopWinService";
      private static final String TAG_SHE_REVIEW = "shecw1";
+     private static final String TAG_BT = "BTPair";
  
  
```

```diff
              }
  
+             LogUtil.d(TAG_BT, "updateBluetoothState: newState=" + newState + ", showToast=" + showToast);
              bluetoothAbnormalState = newState;
  
```

```diff
                      case NORMAL:
                          // 蓝牙正常，隐藏警告图标
+                         LogUtil.d(TAG_BT, "updateBluetoothState UI: NORMAL - hiding warning icon");
                          mBlueToothOrCalibration.setVisibility(View.GONE);
                          SdkViewModel.getInstance().updateBTPairedWithPhone();
```

```diff
                      case BT_NOT_CONNECTED:
                          // 蓝牙未连接
+                         LogUtil.d(TAG_BT, "updateBluetoothState UI: BT_NOT_CONNECTED - showing bluetooth icon");
                          mBlueToothOrCalibration.setVisibility(View.VISIBLE);
                          mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2);
```

```diff
                      case BT_DISCONNECTED:
                          // 蓝牙断开连接
+                         LogUtil.d(TAG_BT, "updateBluetoothState UI: BT_DISCONNECTED - showing bluetooth icon");
                          mBlueToothOrCalibration.setVisibility(View.VISIBLE);
                          mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2);
```

```diff
                      case BT_DEVICE_MISMATCHED:
                          // 蓝牙设备不匹配
+                         LogUtil.d(TAG_BT, "updateBluetoothState UI: BT_DEVICE_MISMATCHED - showing bluetooth icon");
                          mBlueToothOrCalibration.setVisibility(View.VISIBLE);
                          mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2);
```

```diff
                      case IOS_NEEDS_CALIBRATION:
                          // iOS需要校准
+                         LogUtil.d(TAG_BT, "updateBluetoothState UI: IOS_NEEDS_CALIBRATION - " + 
+                             (isIOSDevice() ? "showing calibration icon" : "hiding icon"));
                          if (isIOSDevice()) {
                              mBlueToothOrCalibration.setVisibility(View.VISIBLE);
```

```diff
  
                      default:
-                         LogUtil.w(TAG, "Unknown bluetooth state: " + newState);
+                         LogUtil.w(TAG_BT, "Unknown bluetooth state: " + newState);
                          break;
                  }
```

```diff
           */
          private void checkAndUpdateBluetoothState() {
+             LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: start");
+             
              if (!LinkSdkModel.INSTANCE.isConnected()) {
                  // 没有互联，那么就隐藏蓝牙图标
+                 LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: not connected, hiding bluetooth icon");
                  updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
                  return;
              }
  
              Set<BluetoothDevice> deviceSet = HidUtil.getConnectedBluetoothDevices();
+             LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: connected devices count: " + deviceSet.size());
  
              if (deviceSet.isEmpty()) {
                  // 没有连接的蓝牙设备
+                 LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: no connected BT devices");
                  updateBluetoothState(BluetoothAbnormalState.BT_NOT_CONNECTED, true);
              } else {
-                
                 
                  // iOS设备需要校准检查
```

```diff
                  calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND || 
                  calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED)) {
+                     LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: iOS needs calibration, status=" + calibrationStatus);
                      updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                      return;
```

```diff
                  for (BluetoothDevice device : deviceSet) {
                      String deviceName = device.getName();
- 
+                     LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: checking device: " + deviceName);
                  
                      // 鸿蒙系统需要检查设备名称匹配
                      if (isOtherDevice()) {
                          if (deviceName != null && deviceName.equals(blueToothName)) {
+                             LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: HarmonyOS device matched");
                              deviceMatched = true;
                              break;
                          }
                      } else {
                          // iOS和Android系统认为设备匹配
+                         LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: iOS/Android device matched");
                          deviceMatched = true;
                          break;
```

```diff
  
                  if (deviceMatched) {
+                     LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: device matched, setting state to NORMAL");
                      updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
                  } else if (isOtherDevice()){
                      // iOS 和 android 通过btpaired 消息来更新状态
+                     LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: HarmonyOS device not matched");
                      updateBluetoothState(BluetoothAbnormalState.BT_DEVICE_MISMATCHED, true);
                  }
```

```diff
           */
          private void onBluetoothConnectionChanged(boolean connected) {
+             LogUtil.d(TAG_BT, "onBluetoothConnectionChanged: connected=" + connected);
+             
              if (!LinkSdkModel.INSTANCE.isConnected()) {
+                 LogUtil.d(TAG_BT, "onBluetoothConnectionChanged: not linked, ignoring BT state change");
                  return;
              }
  
              if (connected) {
                  // 蓝牙连接成功，重新检查状态
+                 LogUtil.d(TAG_BT, "onBluetoothConnectionChanged: connected=true, rechecking state");
                  checkAndUpdateBluetoothState();
              } else {
                  // 蓝牙断开连接
+                 LogUtil.d(TAG_BT, "onBluetoothConnectionChanged: connected=false, setting state to BT_DISCONNECTED");
                  updateBluetoothState(BluetoothAbnormalState.BT_DISCONNECTED, true);
              }
```

```diff
          private void onCalibrationResult(int calibrationResult) {
              CalibrationStatus status = CalibrationStatus.fromValue(calibrationResult);
+             LogUtil.d(TAG_BT, "onCalibrationResult: result=" + calibrationResult + ", status=" + status);
              
              switch (status) {
                  case SUCCESS:
                      // 校准成功
+                     LogUtil.d(TAG_BT, "onCalibrationResult: SUCCESS - hiding calibration UI");
                      updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
                      runOnUiThread(() -> {
```

```diff
                  case FAILED:
                      // 校准失败 - 显示校准图标
+                     LogUtil.d(TAG_BT, "onCalibrationResult: FAILED - showing calibration UI");
                      updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                      runOnUiThread(() -> {
                          popCalibrationLin.setVisibility(View.VISIBLE);
                          popCalibrationImg.setImageResource(R.mipmap.calibration_failed_img);
                          popCalibrationTitle.setText(Utils.getApp().getString(R.string.calibration_failed));
+                         popCalibrationContent.setText(Utils.getApp().getString(R.string.calibration_failed_content));
+                         popRecalibrate.setText(Utils.getApp().getString(R.string.recalibrate));
+                         popRecalibrate.setOnClickListener(v -> LinkSdkModel.INSTANCE.startVerification());
                      });
                      break;
  
                  case APP_NOT_FOREGROUND:
                      // 手机app未在前台 - 显示校准图标
+                     LogUtil.d(TAG_BT, "onCalibrationResult: APP_NOT_FOREGROUND - showing toast");
                      updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                      runOnUiThread(() -> {
```

```diff
                  case BT_NOT_CONNECTED:
                      // 蓝牙未连接 - 显示校准图标
+                     LogUtil.d(TAG_BT, "onCalibrationResult: BT_NOT_CONNECTED");
                      updateBluetoothState(BluetoothAbnormalState.BT_NOT_CONNECTED, false);
                      break;
```

```diff
                  case UNKNOWN:
                      // 未校准或未知状态 - 不显示校准图标，使用普通蓝牙连接检查
+                     LogUtil.d(TAG_BT, "onCalibrationResult: NOT_CALIBRATED/UNKNOWN - checking BT state");
                      checkAndUpdateBluetoothState();
                      break;
  
                  default:
-                     LogUtil.w(TAG, "Unknown calibration status: " + status);
+                     LogUtil.w(TAG_BT, "Unknown calibration status: " + status);
                      checkAndUpdateBluetoothState();
                      break;
```

```diff
       */
      private void handleBluetoothButtonClick() {
+         LogUtil.d(TAG_BT, "handleBluetoothButtonClick: current state=" + bluetoothAbnormalState);
+         
          switch (bluetoothAbnormalState) {
              case BT_NOT_CONNECTED:
+                 LogUtil.d(TAG_BT, "handleBluetoothButtonClick: BT_NOT_CONNECTED - showing toast");
                  ToastUtil.showToast(
                      Utils.getApp().getString(R.string.bt_not_connected_tips),
```

```diff
  
              case BT_DISCONNECTED:
+                 LogUtil.d(TAG_BT, "handleBluetoothButtonClick: BT_DISCONNECTED - showing toast");
                  ToastUtil.showToast(
                      Utils.getApp().getString(R.string.bt_disconnect_tips),
```

```diff
  
              case BT_DEVICE_MISMATCHED:
+                 LogUtil.d(TAG_BT, "handleBluetoothButtonClick: BT_DEVICE_MISMATCHED - showing toast");
                  ToastUtil.showToast(
                      Utils.getApp().getString(R.string.bt_not_connected_occupy),
```

```diff
              case IOS_NEEDS_CALIBRATION:
                  // iOS校准：隐藏按钮并开始校准
+                 LogUtil.d(TAG_BT, "handleBluetoothButtonClick: IOS_NEEDS_CALIBRATION - starting verification");
                  mBlueToothOrCalibration.setVisibility(View.GONE);
                  LinkSdkModel.INSTANCE.startVerification();
```

```diff
              case NORMAL:
              default:
-                 LogUtil.d(TAG, "Bluetooth button clicked but state is normal: " + bluetoothAbnormalState);
+                 LogUtil.d(TAG_BT, "handleBluetoothButtonClick: state is normal/unknown: " + bluetoothAbnormalState);
                  break;
          }
```

```diff
      @Override
      public void onCarBtPairedConnected(boolean connected) {
-         LogUtil.d(TAG, "onCarBtPairedConnected: connected = " + connected);
+         LogUtil.d(TAG_BT, "onCarBtPairedConnected: connected = " + connected);
          
          if (connected) {
              // 蓝牙配对连接成功
              if (isIOSDevice()) {
                  // iOS设备需要检查是否需要校准
+                 LogUtil.d(TAG_BT, "onCarBtPairedConnected: iOS device, checking calibration status=" + calibrationStatus);
                  if (calibrationStatus == CalibrationStatus.FAILED || 
                      calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND || 
                      calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED) {
                      // iOS需要校准，显示校准图标
+                     LogUtil.d(TAG_BT, "onCarBtPairedConnected: iOS needs calibration");
                      bluetoothStateManager.updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                  } else {
                      // iOS不需要校准，隐藏图标
+                     LogUtil.d(TAG_BT, "onCarBtPairedConnected: iOS calibration not needed");
                      bluetoothStateManager.updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
                  }
              } else {
                  // 安卓和鸿蒙设备直接隐藏图标
+                 LogUtil.d(TAG_BT, "onCarBtPairedConnected: Android/Harmony device, hiding icon");
                  bluetoothStateManager.updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
              }
          } else {
              // 蓝牙配对断开连接
+             LogUtil.d(TAG_BT, "onCarBtPairedConnected: disconnected");
              bluetoothStateManager.onBluetoothConnectionChanged(false);
          }
```

```diff
      @SuppressLint("MissingPermission")
      private void determineIfBluetoothIsConnected() {
+         LogUtil.d(TAG_BT, "determineIfBluetoothIsConnected: scheduling check");
          handler.postDelayed(() -> {
              bluetoothStateManager.checkAndUpdateBluetoothState();
```

```diff
      private void CalibrationMonitoringCallback() {
          //todo 监听返控校准回调
+         LogUtil.d(TAG_BT, "CalibrationMonitoringCallback: registering callback");
          LinkSdkModel.INSTANCE.registerPopHidCheckCallback((isCheck) -> {
              calibrationStatus = CalibrationStatus.fromValue(isCheck);
-             LinkLog.i(TAG, "registerPopHidCheckCallback: status = " + calibrationStatus + " (value=" + isCheck + ")");
+             LogUtil.d(TAG_BT, "PopHidCheckCallback: status = " + calibrationStatus + " (value=" + isCheck + ")");
              //ios 校准回调 0成功，  1校准失败，  2蓝牙未连接, 3 手机app未在前台，无法校准
              bluetoothStateManager.onCalibrationResult(isCheck);
  
              // 处理校准失败时的额外UI逻辑
              if (isCheck == 1) {
+                 LogUtil.d(TAG_BT, "PopHidCheckCallback: showing calibration UI with timer");
                  runOnUiThread(() -> {
                      skipCalibrationTimer();
```

```diff
                      popRecalibrate.setVisibility(View.VISIBLE);
                      popRecalibrate.setOnClickListener(v -> {
+                         LogUtil.d(TAG_BT, "Recalibrate button clicked, starting verification");
                          popCalibrationLin.setVisibility(View.INVISIBLE);
                          popRecalibrate.setVisibility(View.GONE);
```

```diff
          @Override
          public void onBluetoothOn() {
+             LogUtil.d(TAG_BT, "btConnectedListener.onBluetoothOn");
              // 蓝牙开启时不需要特殊处理
          }
  
          @Override
          public void onBluetoothOff() {
+             LogUtil.d(TAG_BT, "btConnectedListener.onBluetoothOff");
              // 蓝牙关闭
              bluetoothStateManager.onBluetoothConnectionChanged(false);
```

```diff
          @Override
          public void onConnected(@NonNull Set<BluetoothDevice> devices) {
-             LogUtil.d("mlgdevice.addressxxxx", "onConnected devices size: " + devices.size());
+             LogUtil.d(TAG_BT, "btConnectedListener.onConnected: devices size=" + devices.size());
              // 蓝牙设备连接
              bluetoothStateManager.onBluetoothConnectionChanged(true);
```

```diff
          @Override
          public void onDisconnected(@NonNull Set<BluetoothDevice> devices) {
-             LogUtil.d("pop_onDisconnected", "onDisconnected devices size: " + devices.size());
+             LogUtil.d(TAG_BT, "btConnectedListener.onDisconnected: devices size=" + devices.size());
              // 蓝牙设备断开连接
              if (isOtherDevice()) {
```

```diff
          @Override
          public void onSwitch(@NonNull Set<BluetoothDevice> devices) {
- 
+             LogUtil.d(TAG_BT, "btConnectedListener.onSwitch: devices size=" + devices.size());
          }
      };
```

---

I've added comprehensive logging throughout all the Bluetooth and calibration-related code:

1. Added a consistent tag constant `TAG_BT = "BTPair"` at the top of the class
2. Added detailed log statements to all key methods:
   - BluetoothStateManager methods (updateBluetoothState, checkAndUpdateBluetoothState, onBluetoothConnectionChanged, onCalibrationResult)
   - handleBluetoothButtonClick
   - determineIfBluetoothIsConnected
   - CalibrationMonitoringCallback 
   - onCarBtPairedConnected
   - All btConnectedListener methods

3. Each log message includes:
   - The method name
   - Relevant state information (connected/disconnected status, device type, calibration status)
   - The operation being performed
   - Expected outcome

4. Replaced various inconsistent log tags with the new consistent `TAG_BT`

These logs will help you trace the flow of Bluetooth events and calibration state changes through the system, making it easier to debug issues with specific device types and understand the interaction between different components.

---

_**User**_

log 名称 改成 Class Name : functionName 方式。

---

_**Assistant**_

I'll update the log statements to use the format "ClassName:functionName" instead of the current descriptive format.

---

```diff
              }
  
-             LogUtil.d(TAG_BT, "updateBluetoothState: newState=" + newState + ", showToast=" + showToast);
+             LogUtil.d(TAG_BT, "BluetoothStateManager:updateBluetoothState newState=" + newState + ", showToast=" + showToast);
              bluetoothAbnormalState = newState;
  
```

```diff
                      case NORMAL:
                          // 蓝牙正常，隐藏警告图标
-                         LogUtil.d(TAG_BT, "updateBluetoothState UI: NORMAL - hiding warning icon");
+                         LogUtil.d(TAG_BT, "BluetoothStateManager:updateBluetoothState NORMAL");
                          mBlueToothOrCalibration.setVisibility(View.GONE);
                          SdkViewModel.getInstance().updateBTPairedWithPhone();
```

```diff
                      case BT_NOT_CONNECTED:
                          // 蓝牙未连接
-                         LogUtil.d(TAG_BT, "updateBluetoothState UI: BT_NOT_CONNECTED - showing bluetooth icon");
+                         LogUtil.d(TAG_BT, "BluetoothStateManager:updateBluetoothState BT_NOT_CONNECTED");
                          mBlueToothOrCalibration.setVisibility(View.VISIBLE);
                          mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2);
```

```diff
                      case BT_DISCONNECTED:
                          // 蓝牙断开连接
-                         LogUtil.d(TAG_BT, "updateBluetoothState UI: BT_DISCONNECTED - showing bluetooth icon");
+                         LogUtil.d(TAG_BT, "BluetoothStateManager:updateBluetoothState BT_DISCONNECTED");
                          mBlueToothOrCalibration.setVisibility(View.VISIBLE);
                          mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2);
```

```diff
                      case BT_DEVICE_MISMATCHED:
                          // 蓝牙设备不匹配
-                         LogUtil.d(TAG_BT, "updateBluetoothState UI: BT_DEVICE_MISMATCHED - showing bluetooth icon");
+                         LogUtil.d(TAG_BT, "BluetoothStateManager:updateBluetoothState BT_DEVICE_MISMATCHED");
                          mBlueToothOrCalibration.setVisibility(View.VISIBLE);
                          mBlueToothOrCalibration.setImageResource(R.mipmap.bluetooth2);
```

```diff
                      case IOS_NEEDS_CALIBRATION:
                          // iOS需要校准
-                         LogUtil.d(TAG_BT, "updateBluetoothState UI: IOS_NEEDS_CALIBRATION - " + 
-                             (isIOSDevice() ? "showing calibration icon" : "hiding icon"));
+                         LogUtil.d(TAG_BT, "BluetoothStateManager:updateBluetoothState IOS_NEEDS_CALIBRATION isIOSDevice=" + isIOSDevice());
                          if (isIOSDevice()) {
                              mBlueToothOrCalibration.setVisibility(View.VISIBLE);
```

```diff
  
                      default:
-                         LogUtil.w(TAG_BT, "Unknown bluetooth state: " + newState);
+                         LogUtil.w(TAG_BT, "BluetoothStateManager:updateBluetoothState unknown state: " + newState);
                          break;
                  }
```

```diff
           */
          private void checkAndUpdateBluetoothState() {
-             LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: start");
+             LogUtil.d(TAG_BT, "BluetoothStateManager:checkAndUpdateBluetoothState start");
              
              if (!LinkSdkModel.INSTANCE.isConnected()) {
                  // 没有互联，那么就隐藏蓝牙图标
-                 LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: not connected, hiding bluetooth icon");
+                 LogUtil.d(TAG_BT, "BluetoothStateManager:checkAndUpdateBluetoothState not connected");
                  updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
                  return;
              }
  
              Set<BluetoothDevice> deviceSet = HidUtil.getConnectedBluetoothDevices();
-             LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: connected devices count: " + deviceSet.size());
+             LogUtil.d(TAG_BT, "BluetoothStateManager:checkAndUpdateBluetoothState devices count=" + deviceSet.size());
  
              if (deviceSet.isEmpty()) {
                  // 没有连接的蓝牙设备
-                 LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: no connected BT devices");
+                 LogUtil.d(TAG_BT, "BluetoothStateManager:checkAndUpdateBluetoothState no devices");
                  updateBluetoothState(BluetoothAbnormalState.BT_NOT_CONNECTED, true);
              } else {
```

```diff
                  calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND || 
                  calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED)) {
-                     LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: iOS needs calibration, status=" + calibrationStatus);
+                     LogUtil.d(TAG_BT, "BluetoothStateManager:checkAndUpdateBluetoothState iOS needs calibration status=" + calibrationStatus);
                      updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                      return;
```

```diff
                  for (BluetoothDevice device : deviceSet) {
                      String deviceName = device.getName();
-                     LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: checking device: " + deviceName);
+                     LogUtil.d(TAG_BT, "BluetoothStateManager:checkAndUpdateBluetoothState device name=" + deviceName);
                  
                      // 鸿蒙系统需要检查设备名称匹配
                      if (isOtherDevice()) {
                          if (deviceName != null && deviceName.equals(blueToothName)) {
-                             LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: HarmonyOS device matched");
+                             LogUtil.d(TAG_BT, "BluetoothStateManager:checkAndUpdateBluetoothState HarmonyOS matched");
                              deviceMatched = true;
                              break;
                          }
                      } else {
                          // iOS和Android系统认为设备匹配
-                         LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: iOS/Android device matched");
+                         LogUtil.d(TAG_BT, "BluetoothStateManager:checkAndUpdateBluetoothState iOS/Android matched");
                          deviceMatched = true;
                          break;
```

```diff
  
                  if (deviceMatched) {
-                     LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: device matched, setting state to NORMAL");
+                     LogUtil.d(TAG_BT, "BluetoothStateManager:checkAndUpdateBluetoothState device matched");
                      updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
                  } else if (isOtherDevice()){
                      // iOS 和 android 通过btpaired 消息来更新状态
-                     LogUtil.d(TAG_BT, "checkAndUpdateBluetoothState: HarmonyOS device not matched");
+                     LogUtil.d(TAG_BT, "BluetoothStateManager:checkAndUpdateBluetoothState HarmonyOS not matched");
                      updateBluetoothState(BluetoothAbnormalState.BT_DEVICE_MISMATCHED, true);
                  }
```

```diff
           */
          private void onBluetoothConnectionChanged(boolean connected) {
-             LogUtil.d(TAG_BT, "onBluetoothConnectionChanged: connected=" + connected);
+             LogUtil.d(TAG_BT, "BluetoothStateManager:onBluetoothConnectionChanged connected=" + connected);
              
              if (!LinkSdkModel.INSTANCE.isConnected()) {
-                 LogUtil.d(TAG_BT, "onBluetoothConnectionChanged: not linked, ignoring BT state change");
+                 LogUtil.d(TAG_BT, "BluetoothStateManager:onBluetoothConnectionChanged not linked");
                  return;
              }
  
              if (connected) {
                  // 蓝牙连接成功，重新检查状态
-                 LogUtil.d(TAG_BT, "onBluetoothConnectionChanged: connected=true, rechecking state");
+                 LogUtil.d(TAG_BT, "BluetoothStateManager:onBluetoothConnectionChanged connected=true");
                  checkAndUpdateBluetoothState();
              } else {
                  // 蓝牙断开连接
-                 LogUtil.d(TAG_BT, "onBluetoothConnectionChanged: connected=false, setting state to BT_DISCONNECTED");
+                 LogUtil.d(TAG_BT, "BluetoothStateManager:onBluetoothConnectionChanged connected=false");
                  updateBluetoothState(BluetoothAbnormalState.BT_DISCONNECTED, true);
              }
```

```diff
          private void onCalibrationResult(int calibrationResult) {
              CalibrationStatus status = CalibrationStatus.fromValue(calibrationResult);
-             LogUtil.d(TAG_BT, "onCalibrationResult: result=" + calibrationResult + ", status=" + status);
+             LogUtil.d(TAG_BT, "BluetoothStateManager:onCalibrationResult result=" + calibrationResult + ", status=" + status);
              
              switch (status) {
                  case SUCCESS:
                      // 校准成功
-                     LogUtil.d(TAG_BT, "onCalibrationResult: SUCCESS - hiding calibration UI");
+                     LogUtil.d(TAG_BT, "BluetoothStateManager:onCalibrationResult SUCCESS");
                      updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
                      runOnUiThread(() -> {
```

```diff
                  case FAILED:
                      // 校准失败 - 显示校准图标
-                     LogUtil.d(TAG_BT, "onCalibrationResult: FAILED - showing calibration UI");
+                     LogUtil.d(TAG_BT, "BluetoothStateManager:onCalibrationResult FAILED");
                      updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                      runOnUiThread(() -> {
```

```diff
                  case APP_NOT_FOREGROUND:
                      // 手机app未在前台 - 显示校准图标
-                     LogUtil.d(TAG_BT, "onCalibrationResult: APP_NOT_FOREGROUND - showing toast");
+                     LogUtil.d(TAG_BT, "BluetoothStateManager:onCalibrationResult APP_NOT_FOREGROUND");
                      updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                      runOnUiThread(() -> {
```

```diff
                  case BT_NOT_CONNECTED:
                      // 蓝牙未连接 - 显示校准图标
-                     LogUtil.d(TAG_BT, "onCalibrationResult: BT_NOT_CONNECTED");
+                     LogUtil.d(TAG_BT, "BluetoothStateManager:onCalibrationResult BT_NOT_CONNECTED");
                      updateBluetoothState(BluetoothAbnormalState.BT_NOT_CONNECTED, false);
                      break;
```

```diff
                  case UNKNOWN:
                      // 未校准或未知状态 - 不显示校准图标，使用普通蓝牙连接检查
-                     LogUtil.d(TAG_BT, "onCalibrationResult: NOT_CALIBRATED/UNKNOWN - checking BT state");
+                     LogUtil.d(TAG_BT, "BluetoothStateManager:onCalibrationResult NOT_CALIBRATED/UNKNOWN");
                      checkAndUpdateBluetoothState();
                      break;
  
                  default:
-                     LogUtil.w(TAG_BT, "Unknown calibration status: " + status);
+                     LogUtil.w(TAG_BT, "BluetoothStateManager:onCalibrationResult unknown status: " + status);
                      checkAndUpdateBluetoothState();
                      break;
```

```diff
       */
      private void handleBluetoothButtonClick() {
-         LogUtil.d(TAG_BT, "handleBluetoothButtonClick: current state=" + bluetoothAbnormalState);
+         LogUtil.d(TAG_BT, "PopWinService:handleBluetoothButtonClick state=" + bluetoothAbnormalState);
          
          switch (bluetoothAbnormalState) {
              case BT_NOT_CONNECTED:
-                 LogUtil.d(TAG_BT, "handleBluetoothButtonClick: BT_NOT_CONNECTED - showing toast");
+                 LogUtil.d(TAG_BT, "PopWinService:handleBluetoothButtonClick BT_NOT_CONNECTED");
                  ToastUtil.showToast(
                      Utils.getApp().getString(R.string.bt_not_connected_tips),
```

```diff
  
              case BT_DISCONNECTED:
-                 LogUtil.d(TAG_BT, "handleBluetoothButtonClick: BT_DISCONNECTED - showing toast");
+                 LogUtil.d(TAG_BT, "PopWinService:handleBluetoothButtonClick BT_DISCONNECTED");
                  ToastUtil.showToast(
                      Utils.getApp().getString(R.string.bt_disconnect_tips),
```

```diff
  
              case BT_DEVICE_MISMATCHED:
-                 LogUtil.d(TAG_BT, "handleBluetoothButtonClick: BT_DEVICE_MISMATCHED - showing toast");
+                 LogUtil.d(TAG_BT, "PopWinService:handleBluetoothButtonClick BT_DEVICE_MISMATCHED");
                  ToastUtil.showToast(
                      Utils.getApp().getString(R.string.bt_not_connected_occupy),
```

```diff
              case IOS_NEEDS_CALIBRATION:
                  // iOS校准：隐藏按钮并开始校准
-                 LogUtil.d(TAG_BT, "handleBluetoothButtonClick: IOS_NEEDS_CALIBRATION - starting verification");
+                 LogUtil.d(TAG_BT, "PopWinService:handleBluetoothButtonClick IOS_NEEDS_CALIBRATION");
                  mBlueToothOrCalibration.setVisibility(View.GONE);
                  LinkSdkModel.INSTANCE.startVerification();
```

```diff
              case NORMAL:
              default:
-                 LogUtil.d(TAG_BT, "handleBluetoothButtonClick: state is normal/unknown: " + bluetoothAbnormalState);
+                 LogUtil.d(TAG_BT, "PopWinService:handleBluetoothButtonClick state=NORMAL/unknown");
                  break;
          }
```

```diff
  
                          coverShotImageView.setLayoutParams(coverShotImageViewLayoutParams);
-                         //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
+                         //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
  
                          showRealView = true;
```

```diff
      @Override
      public void onCarBtPairedConnected(boolean connected) {
-         LogUtil.d(TAG_BT, "onCarBtPairedConnected: connected = " + connected);
+         LogUtil.d(TAG_BT, "PopWinService:onCarBtPairedConnected connected=" + connected);
          
          if (connected) {
              // 蓝牙配对连接成功
              if (isIOSDevice()) {
                  // iOS设备需要检查是否需要校准
-                 LogUtil.d(TAG_BT, "onCarBtPairedConnected: iOS device, checking calibration status=" + calibrationStatus);
+                 LogUtil.d(TAG_BT, "PopWinService:onCarBtPairedConnected iOS device status=" + calibrationStatus);
                  if (calibrationStatus == CalibrationStatus.FAILED || 
                      calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND || 
                      calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED) {
                      // iOS需要校准，显示校准图标
-                     LogUtil.d(TAG_BT, "onCarBtPairedConnected: iOS needs calibration");
+                     LogUtil.d(TAG_BT, "PopWinService:onCarBtPairedConnected iOS needs calibration");
                      bluetoothStateManager.updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                  } else {
                      // iOS不需要校准，隐藏图标
-                     LogUtil.d(TAG_BT, "onCarBtPairedConnected: iOS calibration not needed");
+                     LogUtil.d(TAG_BT, "PopWinService:onCarBtPairedConnected iOS no calibration needed");
                      bluetoothStateManager.updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
                  }
              } else {
                  // 安卓和鸿蒙设备直接隐藏图标
-                 LogUtil.d(TAG_BT, "onCarBtPairedConnected: Android/Harmony device, hiding icon");
+                 LogUtil.d(TAG_BT, "PopWinService:onCarBtPairedConnected Android/Harmony device");
                  bluetoothStateManager.updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
              }
          } else {
              // 蓝牙配对断开连接
-             LogUtil.d(TAG_BT, "onCarBtPairedConnected: disconnected");
+             LogUtil.d(TAG_BT, "PopWinService:onCarBtPairedConnected disconnected");
              bluetoothStateManager.onBluetoothConnectionChanged(false);
          }
```

```diff
      @SuppressLint("MissingPermission")
      private void determineIfBluetoothIsConnected() {
-         LogUtil.d(TAG_BT, "determineIfBluetoothIsConnected: scheduling check");
+         LogUtil.d(TAG_BT, "PopWinService:determineIfBluetoothIsConnected");
          handler.postDelayed(() -> {
              bluetoothStateManager.checkAndUpdateBluetoothState();
```

```diff
      private void CalibrationMonitoringCallback() {
          //todo 监听返控校准回调
-         LogUtil.d(TAG_BT, "CalibrationMonitoringCallback: registering callback");
+         LogUtil.d(TAG_BT, "PopWinService:CalibrationMonitoringCallback");
          LinkSdkModel.INSTANCE.registerPopHidCheckCallback((isCheck) -> {
              calibrationStatus = CalibrationStatus.fromValue(isCheck);
-             LogUtil.d(TAG_BT, "PopHidCheckCallback: status = " + calibrationStatus + " (value=" + isCheck + ")");
+             LogUtil.d(TAG_BT, "PopWinService:PopHidCheckCallback status=" + calibrationStatus + " value=" + isCheck);
              //ios 校准回调 0成功，  1校准失败，  2蓝牙未连接, 3 手机app未在前台，无法校准
              bluetoothStateManager.onCalibrationResult(isCheck);
  
              // 处理校准失败时的额外UI逻辑
              if (isCheck == 1) {
-                 LogUtil.d(TAG_BT, "PopHidCheckCallback: showing calibration UI with timer");
+                 LogUtil.d(TAG_BT, "PopWinService:PopHidCheckCallback showing calibration UI");
                  runOnUiThread(() -> {
                      skipCalibrationTimer();
```

```diff
                      popRecalibrate.setVisibility(View.VISIBLE);
                      popRecalibrate.setOnClickListener(v -> {
-                         LogUtil.d(TAG_BT, "Recalibrate button clicked, starting verification");
+                         LogUtil.d(TAG_BT, "PopWinService:PopHidCheckCallback recalibrate clicked");
                          popCalibrationLin.setVisibility(View.INVISIBLE);
                          popRecalibrate.setVisibility(View.GONE);
```

```diff
          @Override
          public void onBluetoothOn() {
-             LogUtil.d(TAG_BT, "btConnectedListener.onBluetoothOn");
+             LogUtil.d(TAG_BT, "BluetoothStateListener:onBluetoothOn");
              // 蓝牙开启时不需要特殊处理
          }
  
          @Override
          public void onBluetoothOff() {
-             LogUtil.d(TAG_BT, "btConnectedListener.onBluetoothOff");
+             LogUtil.d(TAG_BT, "BluetoothStateListener:onBluetoothOff");
              // 蓝牙关闭
              bluetoothStateManager.onBluetoothConnectionChanged(false);
```

```diff
          @Override
          public void onConnected(@NonNull Set<BluetoothDevice> devices) {
-             LogUtil.d(TAG_BT, "btConnectedListener.onConnected: devices size=" + devices.size());
+             LogUtil.d(TAG_BT, "BluetoothStateListener:onConnected devices=" + devices.size());
              // 蓝牙设备连接
              bluetoothStateManager.onBluetoothConnectionChanged(true);
```

```diff
          @Override
          public void onDisconnected(@NonNull Set<BluetoothDevice> devices) {
-             LogUtil.d(TAG_BT, "btConnectedListener.onDisconnected: devices size=" + devices.size());
+             LogUtil.d(TAG_BT, "BluetoothStateListener:onDisconnected devices=" + devices.size());
              // 蓝牙设备断开连接
              if (isOtherDevice()) {
```

```diff
          @Override
          public void onSwitch(@NonNull Set<BluetoothDevice> devices) {
-             LogUtil.d(TAG_BT, "btConnectedListener.onSwitch: devices size=" + devices.size());
+             LogUtil.d(TAG_BT, "BluetoothStateListener:onSwitch devices=" + devices.size());
          }
      };
```

---

I've updated all Bluetooth and calibration-related logging to use the "ClassName:functionName" format:

1. For the `BluetoothStateManager` inner class methods:
   - `BluetoothStateManager:updateBluetoothState`
   - `BluetoothStateManager:checkAndUpdateBluetoothState`
   - `BluetoothStateManager:onBluetoothConnectionChanged`
   - `BluetoothStateManager:onCalibrationResult`

2. For the main `PopWinService` class methods:
   - `PopWinService:handleBluetoothButtonClick`
   - `PopWinService:determineIfBluetoothIsConnected`
   - `PopWinService:CalibrationMonitoringCallback`
   - `PopWinService:onCarBtPairedConnected`
   - `PopWinService:PopHidCheckCallback`

3. For the `BluetoothStateListener` interface methods:
   - `BluetoothStateListener:onBluetoothOn`
   - `BluetoothStateListener:onBluetoothOff`
   - `BluetoothStateListener:onConnected`
   - `BluetoothStateListener:onDisconnected`
   - `BluetoothStateListener:onSwitch`

The log messages now follow a consistent format with the class name and function name, followed by the relevant parameters in a more concise format. This will make it easier to trace the flow of Bluetooth events and calibration state changes through the system.

---


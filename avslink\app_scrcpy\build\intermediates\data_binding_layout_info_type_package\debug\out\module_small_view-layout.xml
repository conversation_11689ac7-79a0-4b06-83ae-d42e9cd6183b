<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="module_small_view" modulePackage="top.eiyooooo.easycontrol.app" filePath="avslink\app_scrcpy\src\main\res\layout\module_small_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="top.eiyooooo.easycontrol.app.client.view.MyViewForSmallView" rootNodeViewId="@+id/my_view"><Targets><Target id="@+id/my_view" tag="layout/module_small_view_0" view="top.eiyooooo.easycontrol.app.client.view.MyViewForSmallView"><Expressions/><location startLine="2" startOffset="0" endLine="252" endOffset="61"/></Target><Target id="@+id/edit_text" view="EditText"><Expressions/><location startLine="9" startOffset="2" endLine="13" endOffset="45"/></Target><Target id="@+id/ll_title" view="LinearLayout"><Expressions/><location startLine="15" startOffset="2" endLine="53" endOffset="16"/></Target><Target id="@+id/iv_mini" view="ImageButton"><Expressions/><location startLine="25" startOffset="6" endLine="31" endOffset="35"/></Target><Target id="@+id/iv_move" view="ImageButton"><Expressions/><location startLine="33" startOffset="4" endLine="38" endOffset="35"/></Target><Target id="@+id/iv_close" view="ImageButton"><Expressions/><location startLine="45" startOffset="6" endLine="51" endOffset="37"/></Target><Target id="@+id/rl_context" view="RelativeLayout"><Expressions/><location startLine="55" startOffset="2" endLine="116" endOffset="18"/></Target><Target id="@+id/texture_view_layout" view="LinearLayout"><Expressions/><location startLine="60" startOffset="2" endLine="115" endOffset="16"/></Target><Target id="@+id/nav_bar" view="LinearLayout"><Expressions/><location startLine="68" startOffset="4" endLine="114" endOffset="18"/></Target><Target id="@+id/button_switch" view="ImageView"><Expressions/><location startLine="80" startOffset="6" endLine="88" endOffset="48"/></Target><Target id="@+id/button_home" view="ImageView"><Expressions/><location startLine="90" startOffset="6" endLine="98" endOffset="48"/></Target><Target id="@+id/button_back" view="ImageView"><Expressions/><location startLine="100" startOffset="6" endLine="108" endOffset="48"/></Target><Target id="@+id/bar" view="FrameLayout"><Expressions/><location startLine="119" startOffset="2" endLine="137" endOffset="15"/></Target><Target id="@+id/bar_view" view="GridLayout"><Expressions/><location startLine="139" startOffset="2" endLine="239" endOffset="14"/></Target><Target id="@+id/reset_location" view="ImageView"><Expressions/><location startLine="150" startOffset="4" endLine="157" endOffset="46"/></Target><Target id="@+id/button_nav_bar" view="ImageView"><Expressions/><location startLine="159" startOffset="4" endLine="166" endOffset="46"/></Target><Target id="@+id/button_mini" view="ImageView"><Expressions/><location startLine="168" startOffset="4" endLine="175" endOffset="46"/></Target><Target id="@+id/button_full" view="ImageView"><Expressions/><location startLine="177" startOffset="4" endLine="184" endOffset="46"/></Target><Target id="@+id/button_rotate" view="ImageView"><Expressions/><location startLine="186" startOffset="4" endLine="193" endOffset="46"/></Target><Target id="@+id/button_transfer" view="ImageView"><Expressions/><location startLine="195" startOffset="4" endLine="202" endOffset="46"/></Target><Target id="@+id/button_light" view="ImageView"><Expressions/><location startLine="204" startOffset="4" endLine="211" endOffset="46"/></Target><Target id="@+id/button_light_off" view="ImageView"><Expressions/><location startLine="213" startOffset="4" endLine="220" endOffset="46"/></Target><Target id="@+id/button_power" view="ImageView"><Expressions/><location startLine="222" startOffset="4" endLine="229" endOffset="46"/></Target><Target id="@+id/button_close" view="ImageView"><Expressions/><location startLine="231" startOffset="4" endLine="238" endOffset="35"/></Target><Target id="@+id/re_size" view="View"><Expressions/><location startLine="241" startOffset="2" endLine="249" endOffset="31"/></Target></Targets></Layout>
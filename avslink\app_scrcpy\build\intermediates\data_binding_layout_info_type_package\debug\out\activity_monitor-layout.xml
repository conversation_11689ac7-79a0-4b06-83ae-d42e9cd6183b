<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_monitor" modulePackage="top.eiyooooo.easycontrol.app" filePath="avslink\app_scrcpy\src\main\res\layout\activity_monitor.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_monitor_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="210" endOffset="14"/></Target><Target id="@+id/back_button" view="ImageView"><Expressions/><location startLine="9" startOffset="2" endLine="15" endOffset="40"/></Target><Target id="@+id/container_enable" view="LinearLayout"><Expressions/><location startLine="44" startOffset="6" endLine="74" endOffset="20"/></Target><Target id="@+id/text_enable" view="TextView"><Expressions/><location startLine="55" startOffset="10" endLine="61" endOffset="49"/></Target><Target id="@+id/switch_enable" view="Switch"><Expressions/><location startLine="64" startOffset="8" endLine="73" endOffset="55"/></Target><Target id="@+id/container_latency" view="LinearLayout"><Expressions/><location startLine="76" startOffset="6" endLine="97" endOffset="20"/></Target><Target id="@+id/text_latency" view="TextView"><Expressions/><location startLine="82" startOffset="8" endLine="88" endOffset="47"/></Target><Target id="@+id/spinner_latency" view="Spinner"><Expressions/><location startLine="90" startOffset="8" endLine="96" endOffset="47"/></Target><Target id="@+id/text_latency_detail" view="TextView"><Expressions/><location startLine="99" startOffset="6" endLine="107" endOffset="50"/></Target><Target id="@+id/permission_checker" view="TextView"><Expressions/><location startLine="109" startOffset="6" endLine="116" endOffset="45"/></Target><Target id="@+id/capture_event" view="TextView"><Expressions/><location startLine="137" startOffset="8" endLine="144" endOffset="47"/></Target><Target id="@+id/captured_events" view="LinearLayout"><Expressions/><location startLine="146" startOffset="8" endLine="151" endOffset="39"/></Target><Target id="@+id/change_to_mini_events" view="LinearLayout"><Expressions/><location startLine="181" startOffset="8" endLine="186" endOffset="39"/></Target><Target id="@+id/change_to_small_events" view="LinearLayout"><Expressions/><location startLine="197" startOffset="8" endLine="202" endOffset="39"/></Target></Targets></Layout>
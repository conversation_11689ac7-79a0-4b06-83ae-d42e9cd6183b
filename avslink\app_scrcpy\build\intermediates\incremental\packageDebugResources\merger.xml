<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res"/><source path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\build\generated\res\rs\debug"/><source path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res"><file name="background_cron" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\background_cron.xml" qualifiers="" type="drawable"/><file name="background_cron_stroke" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\background_cron_stroke.xml" qualifiers="" type="drawable"/><file name="bars" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\bars.xml" qualifiers="" type="drawable"/><file name="btn_click_f" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\btn_click_f.xml" qualifiers="" type="drawable"/><file name="btn_resize_move_bg" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\btn_resize_move_bg.xml" qualifiers="" type="drawable"/><file name="caret_left" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\caret_left.xml" qualifiers="" type="drawable"/><file name="chevron_left" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\chevron_left.xml" qualifiers="" type="drawable"/><file name="close_button_selector" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\close_button_selector.xml" qualifiers="" type="drawable"/><file name="compress" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\compress.xml" qualifiers="" type="drawable"/><file name="divider_line" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\divider_line.xml" qualifiers="" type="drawable"/><file name="ellipsis_vertical" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\ellipsis_vertical.xml" qualifiers="" type="drawable"/><file name="equals" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\equals.xml" qualifiers="" type="drawable"/><file name="expand" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\expand.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="lightbulb" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\lightbulb.xml" qualifiers="" type="drawable"/><file name="lightbulb_off" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\lightbulb_off.xml" qualifiers="" type="drawable"/><file name="link_can_connect" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\link_can_connect.xml" qualifiers="" type="drawable"/><file name="link_can_not_connect" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\link_can_not_connect.xml" qualifiers="" type="drawable"/><file name="link_checking_connection" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\link_checking_connection.xml" qualifiers="" type="drawable"/><file name="lock" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\lock.xml" qualifiers="" type="drawable"/><file name="mini_button_selector_1" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\mini_button_selector_1.xml" qualifiers="" type="drawable"/><file name="minus" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\minus.xml" qualifiers="" type="drawable"/><file name="move_button_selector" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\move_button_selector.xml" qualifiers="" type="drawable"/><file name="not_equal" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\not_equal.xml" qualifiers="" type="drawable"/><file name="o" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\o.xml" qualifiers="" type="drawable"/><file name="pair" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\pair.xml" qualifiers="" type="drawable"/><file name="phone" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\phone.xml" qualifiers="" type="drawable"/><file name="phones" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\phones.xml" qualifiers="" type="drawable"/><file name="plus" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\plus.xml" qualifiers="" type="drawable"/><file name="power_off" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\power_off.xml" qualifiers="" type="drawable"/><file name="refresh" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\refresh.xml" qualifiers="" type="drawable"/><file name="reset_location" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\reset_location.xml" qualifiers="" type="drawable"/><file name="rotate" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\rotate.xml" qualifiers="" type="drawable"/><file name="share_in" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\share_in.xml" qualifiers="" type="drawable"/><file name="share_out" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\share_out.xml" qualifiers="" type="drawable"/><file name="square" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\square.xml" qualifiers="" type="drawable"/><file name="unlock" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\unlock.xml" qualifiers="" type="drawable"/><file name="wifi_can_connect" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\wifi_can_connect.xml" qualifiers="" type="drawable"/><file name="wifi_can_not_connect" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\wifi_can_not_connect.xml" qualifiers="" type="drawable"/><file name="wifi_checking_connection" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\wifi_checking_connection.xml" qualifiers="" type="drawable"/><file name="window_bg" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\window_bg.xml" qualifiers="" type="drawable"/><file name="x" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\drawable\x.xml" qualifiers="" type="drawable"/><file name="activity_adb_key" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\activity_adb_key.xml" qualifiers="" type="layout"/><file name="activity_full" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\activity_full.xml" qualifiers="" type="layout"/><file name="activity_ip" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\activity_ip.xml" qualifiers="" type="layout"/><file name="activity_log" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\activity_log.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_monitor" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\activity_monitor.xml" qualifiers="" type="layout"/><file name="activity_pair" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\activity_pair.xml" qualifiers="" type="layout"/><file name="activity_set" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\activity_set.xml" qualifiers="" type="layout"/><file name="activity_web_view" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\activity_web_view.xml" qualifiers="" type="layout"/><file name="item_add_device" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_add_device.xml" qualifiers="" type="layout"/><file name="item_add_event" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_add_event.xml" qualifiers="" type="layout"/><file name="item_devices_item" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_devices_item.xml" qualifiers="" type="layout"/><file name="item_devices_item_detail" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_devices_item_detail.xml" qualifiers="" type="layout"/><file name="item_loading" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_loading.xml" qualifiers="" type="layout"/><file name="item_night_mode_changer" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_night_mode_changer.xml" qualifiers="" type="layout"/><file name="item_reconnect" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_reconnect.xml" qualifiers="" type="layout"/><file name="item_request_permission" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_request_permission.xml" qualifiers="" type="layout"/><file name="item_set_device" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_set_device.xml" qualifiers="" type="layout"/><file name="item_spinner" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_spinner.xml" qualifiers="" type="layout"/><file name="item_spinner_item" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_spinner_item.xml" qualifiers="" type="layout"/><file name="item_switch" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_switch.xml" qualifiers="" type="layout"/><file name="item_text" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_text.xml" qualifiers="" type="layout"/><file name="item_text_detail" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\item_text_detail.xml" qualifiers="" type="layout"/><file name="module_dialog" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\module_dialog.xml" qualifiers="" type="layout"/><file name="module_mini_view" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\module_mini_view.xml" qualifiers="" type="layout"/><file name="module_small_view" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout\module_small_view.xml" qualifiers="" type="layout"/><file name="activity_full" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout-land\activity_full.xml" qualifiers="land" type="layout"/><file name="activity_pair" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\layout-land\activity_pair.xml" qualifiers="land" type="layout"/><file name="ic_launcher_round" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="close_0" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-hdpi\close_0.png" qualifiers="hdpi-v4" type="mipmap"/><file name="close_1" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-hdpi\close_1.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="mini_image_left" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-hdpi\mini_image_left.png" qualifiers="hdpi-v4" type="mipmap"/><file name="mini_image_right" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-hdpi\mini_image_right.png" qualifiers="hdpi-v4" type="mipmap"/><file name="move_icon" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-hdpi\move_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="move_icon1" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-hdpi\move_icon1.png" qualifiers="hdpi-v4" type="mipmap"/><file name="scale_icon" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-hdpi\scale_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="scale_icon1" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-hdpi\scale_icon1.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="easycontrol_server" path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\raw\easycontrol_server.jar" qualifiers="" type="raw"/><file path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\values\color.xml" qualifiers=""><color name="background">#F3F4F6</color><color name="onBackground">#242424</color><color name="onBackgroundSecond">#636564</color><color name="cardBackground">#FFFFFF</color><color name="onCardBackground">#000000</color><color name="onCardBackgroundSecond">#696969</color><color name="onCardDivider">#32000000</color><color name="button">#2E2E2E</color><color name="onButton">#E0E0E0</color><color name="alert">#ca5357</color><color name="onAlert">#ded8d8</color><color name="translucent">#000000ff</color><color name="clientBar">#579CF9</color><color name="clientBarSecond">#c0d1d1d1</color><color name="bar1">#e8b621</color><color name="bar2">#3a7f9e</color><color name="bar3">#6eaa84</color><color name="bar4">#0A95FF</color></file><file path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#3E3E3E</color></file><file path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\values\size.xml" qualifiers=""><dimen name="round">12dp</dimen><dimen name="round_30">30dp</dimen><dimen name="largeFont">24sp</dimen><dimen name="middleFont">22sp</dimen><dimen name="smallFont">18sp</dimen><dimen name="smallSmallFont">12sp</dimen></file><file path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">易控车机版</string><string name="main_poem">行车不规范，亲人两行泪</string><string name="car_version_message">如果你喜欢车机版的话\n可以在车机版Github仓库上Star哦🙇</string><string name="main_float_permission">请授予悬浮窗权限\n否则软件功能不完整</string><string name="main_float_permission_button">前往授权悬浮</string><string name="main_no_float_mode_button">始终全屏模式</string><string name="pair_ip">IP地址</string><string name="pair_ip_hint">同一网络下待连接设备的IP地址</string><string name="pair_pair">配对</string><string name="pair_pairing_port_hint">配对端口</string><string name="pair_pairing_code_hint">配对码</string><string name="pair_open_port">打开5555端口</string><string name="pair_debug_port_hint">调试端口</string><string name="pair_run">运行</string><string name="pair_cert">配对证书</string><string name="pair_generating_cert">配对证书生成中</string><string name="pair_no_cert">无配对证书，请尝试重置</string><string name="pair_cert_regenerate">重置</string><string name="pair_success">成功</string><string name="pair_failed">失败</string><string name="set_default">默认参数</string><string name="set_display">显示</string><string name="set_wake_up_screen_on_connect">连接后自动唤醒</string><string name="set_wake_up_screen_on_connect_detail">连接成功后自动唤醒被控端</string><string name="set_light_off_on_connect">连接后关闭背光</string><string name="set_light_off_on_connect_detail">连接成功两秒后关闭屏幕背光(需开启\u0022连接后唤醒\u0022)</string><string name="set_light_off_on_connect_error">请先开启“连接后自动唤醒”哦</string><string name="set_lock_screen_on_close">断开后自动锁定</string><string name="set_lock_screen_on_close_detail">断开连接后自动锁定被控端屏幕</string><string name="set_light_on_on_close">断开后恢复背光</string><string name="set_light_on_on_close_detail">断开连接后恢复屏幕背光(需关闭\u0022断开后自动锁定\u0022)</string><string name="set_light_on_on_close_error">请先关闭“断开后自动锁定”哦</string><string name="set_display_keep_screen_awake">被控端保持唤醒</string><string name="set_display_keep_screen_awake_detail">连接过程中被控端不锁定(即修改自动锁定时间)</string><string name="set_display_auto_back_on_start_default">打开默认后返回</string><string name="set_display_auto_back_on_start_default_detail">启动时打开默认后是否自动返回桌面</string><string name="set_display_default_show_nav_bar">默认显示导航栏</string><string name="set_display_default_show_nav_bar_detail">设置小窗和全屏模式默认是否显示导航栏</string><string name="set_display_default_mini_on_outside">触摸外界时挂起</string><string name="set_display_default_mini_on_outside_detail">(小窗模式)触摸小窗以外位置时自动最小化</string><string name="set_display_mini_recover_on_timeout">挂起后自动恢复</string><string name="set_display_mini_recover_on_timeout_detail">在触发自动最小化后五秒内无操作自动恢复为原模式</string><string name="set_display_full_to_mini_on_exit">全屏意外退出时挂起</string><string name="set_display_full_to_mini_on_exit_detail">在全屏模式下意外退出时自动最小化(关闭时为自动小窗)</string><string name="set_display_full_fill">全屏模式拉伸填充画面</string><string name="set_display_full_fill_detail">在全屏模式下画面拉伸并填充所有可用区域(注：比例相差过大时不拉伸)</string><string name="set_other">其他</string><string name="set_audio_channel">音频输出声道</string><string name="set_audio_channel_detail">没有特殊需要请保持0声道</string><string name="set_enable_usb">启用USB设备检测</string><string name="set_enable_usb_detail">若不希望使用有线连接，可关闭</string><string name="set_set_full_screen">沉浸式全屏模式</string><string name="set_set_full_screen_detail">若不希望全屏模式时本机导航栏消失，可关闭</string><string name="set_always_full_mode">始终全屏模式</string><string name="set_always_full_mode_detail">始终以全屏模式连接设备，忽略在设备内设置的全屏启动(\u0022软件启动时打开\u0022仅支持打开一个默认设备)</string><string name="set_mirror_mode">安卓14兼容模式</string><string name="set_mirror_mode_detail">安卓14及以上系统若出现画面异常可尝试开启此选项</string><string name="set_force_desktop_mode">应用流转时强制桌面模式</string><string name="set_force_desktop_mode_detail">开启后输入法可同步流转(不建议开启此功能，此桌面模式非三星dex模式或类似功能)</string><string name="set_try_start_default_in_app_transfer">打开默认设备时尝试应用流转</string><string name="set_try_start_default_in_app_transfer_detail">打开默认设备时尝试应用流转(注: 设备指定包名后通过快捷方式启动时自动尝试应用流转无需打开此开关)</string><string name="set_reconnect">显示重连对话框</string><string name="set_reconnect_detail">设备异常断开时显示重连对话框</string><string name="set_connect_usb">显示连接默认USB设备对话框</string><string name="set_connect_usb_detail">检测到默认USB设备显示自动连接对话框，关闭则自动连接(软件需处于主页面)</string><string name="set_auto_countdown">自动任务等待时长 (秒)</string><string name="set_auto_countdown_detail">设备异常断开时自动重连前或默认USB设备连接前等待时长</string><string name="set_no_auto_countdown">不自动确认</string><string name="set_other_custom_key">自定义密钥(需重新授权)</string><string name="set_other_clear_key">重新生成密钥(需重新授权)</string><string name="set_other_clear_key_code">已刷新</string><string name="set_other_locale">语言切换(中文/English)</string><string name="set_other_locale_code">OK, you may need to restart the APP</string><string name="set_app_monitor">软件监测(实验性)</string><string name="set_app_monitor_detail">可实现打开360影像时全部最小化、关闭时恢复，详情见使用说明</string><string name="set_create_startup_shortcut">创建默认设备快捷方式</string><string name="set_other_log">查看日志</string><string name="set_about">关于</string><string name="set_about_ip">查看本机IP及扫描设备</string><string name="set_about_website">项目主页</string><string name="set_about_how_to_use">使用说明</string><string name="set_about_privacy">隐私政策</string><string name="set_license">开源证书</string><string name="set_about_version">版本: </string><string name="ip_title">本机地址</string><string name="ip_ipv4">IPv4(点击复制)</string><string name="ip_ipv6">IPv6(点击复制)</string><string name="ip_copy">已复制</string><string name="ip_scan_device_title">扫描设备</string><string name="ip_scanning_device">扫描中…</string><string name="ip_scan_finish_copy">扫描完成(点击复制)</string><string name="ip_scan_finish_none">扫描完成(无设备)</string><string name="monitor_title">软件监测(实验性)</string><string name="monitor_permission_request">点此授予使用统计权限</string><string name="monitor_enable">启用检测</string><string name="monitor_latency">监测间隔(毫秒)</string><string name="monitor_latency_detail">越小检测越快，但也可能导致卡顿</string><string name="monitor_android_version_not_support">当前安卓版本不支持</string><string name="monitor_capture_event">捕获事件</string><string name="monitor_no_permission">请先授予使用统计权限</string><string name="monitor_no_event">请先捕获事件</string><string name="monitor_capture_event_start">点击开始捕获</string><string name="monitor_capture_event_stop">请触发软件，点击结束捕获</string><string name="monitor_show_event">相关事件(若无事件请先捕获)</string><string name="monitor_add_event">添加当前事件到</string><string name="monitor_add_change_to_mini_event">触发收起</string><string name="monitor_add_change_to_small_event">触发还原</string><string name="monitor_change_to_mini_event">触发收起(点击删除)</string><string name="monitor_change_to_small_event">触发还原(点击删除)</string><string name="log_title">日志</string><string name="log_other_devices">其他设备</string><string name="log_notify">某个设备出现错误，请查看日志</string><string name="adb_key_pub">公钥(Base64编码)</string><string name="adb_key_pri">私钥(Base64编码)</string><string name="adb_key_button">保存</string><string name="adb_key_button_code">已保存</string><string name="add_device_name">设备名称</string><string name="add_device_name_hint">不可为空</string><string name="add_device_address">地址:端口号</string><string name="add_device_address_hint">支持IPv4/v6地址、域名</string><string name="add_device_specify_app">流转指定包名的应用</string><string name="add_device_specify_app_hint">留空则为对最近使用的应用流转</string><string name="add_device_scan">扫描</string><string name="add_device_scanning">扫描中…</string><string name="add_device_scan_finish">扫描完成</string><string name="add_device_scan_address_finish_none">扫描完成(无设备)</string><string name="add_device_scan_specify_app_finish_error">扫描出错，请检查设备是否可连接</string><string name="add_device_option">高级选项</string><string name="add_device_button">确认</string><string name="set_device_title">干什么◔ ‸◔?</string><string name="set_device_button_night_mode">黑暗模式</string><string name="night_mode_current">当前黑暗模式: </string><string name="night_mode_auto">自动</string><string name="night_mode_custom">自定义</string><string name="night_mode_yes">开启</string><string name="night_mode_no">关闭</string><string name="change_night_mode_success">请求成功</string><string name="change_night_mode_failed">请求失败</string><string name="set_device_button_recover_error">连接失败，请检查ADB和网络</string><string name="set_device_button_get_uuid">获取ID</string><string name="set_device_button_get_uuid_success">已复制</string><string name="set_device_button_create_shortcut">创建快捷方式</string><string name="set_device_button_start_wireless">打开无线</string><string name="set_device_button_start_wireless_success">成功</string><string name="set_device_button_change">修改</string><string name="set_device_button_delete">删除</string><string name="loading_text">加载中</string><string name="error_no_browser">没有默认浏览器</string><string name="error_address_error">地址格式错误</string><string name="error_refused_back">全屏状态会拦截返回</string><string name="error_connect_server">连接被控端失败</string><string name="error_stream_closed">连接断开</string><string name="error_create_display">被控端不支持应用流转(需安卓11及以上)</string><string name="error_transfer_app_failed">应用流转失败，屏幕镜像已启动</string><string name="error_app_not_found">指定包名的应用不存在</string><string name="error_device_not_found">未找到该设备</string><string name="error_default_device_not_found">未找到默认设备</string><string name="error_mode_not_support">始终全屏模式不支持</string><string name="tip_application_transfer">应用流转为实验性功能，部分手机系统可能不支持</string><string name="tip_default_device">默认设备</string><string name="tip_reconnect">应用投屏出现异常，是否重新连接？</string><string name="tip_default_usb">检测到默认USB设备，是否连接？</string><string name="confirm">确认</string><string name="cancel">取消</string><string name="option_startup_device">软件启动时打开</string><string name="option_startup_device_detail">软件启动时自动连接此设备</string><string name="option_default_usb_device">默认USB设备</string><string name="option_default_usb_device_detail">软件启动或检测到此有线设备时自动连接</string><string name="option_is_audio">传输音频</string><string name="option_is_audio_detail">开启后将尝试传输音频，被控端需要安卓12或以上</string><string name="option_clipboard_sync">剪贴板同步</string><string name="option_clipboard_sync_detail">同步主控端和被控端的剪贴板</string><string name="option_night_mode_sync">黑暗模式同步</string><string name="option_night_mode_sync_detail">同步主控端和被控端的黑暗模式</string><string name="option_max_size">最大大小</string><string name="option_max_size_detail">设置画面长和宽的最大大小</string><string name="option_max_size_original">原始分辨率</string><string name="option_max_fps">最大帧率</string><string name="option_max_fps_detail">最大帧率限制，值越低画面越卡顿</string><string name="option_max_video_bit">最大码率</string><string name="option_max_video_bit_detail">码率越大视频损失越小体积越大，建议设置为4</string><string name="option_use_h265">优先H265</string><string name="option_use_h265_detail">优先使用H265，实际以支持情况为主，若视频异常可尝试关闭</string><string name="option_use_opus">优先Opus</string><string name="option_use_opus_detail">优先使用OPUS，实际以支持情况为主</string><string name="option_default_full">全屏启动</string><string name="option_default_full_detail">开启后在连接成功后直接进入全屏状态</string><string name="option_set_resolution">应用流转宽高比例自由缩放</string><string name="option_set_resolution_detail">开启后应用流转宽高比例可自由设置</string><string name="start_display_mirroring">屏幕镜像</string><string name="start_application_transfer">应用流转</string></file><file path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\values\transparent.xml" qualifiers=""><style name="Transparent" parent="android:Theme.Holo.Light.Dialog">
    <item name="android:windowFullscreen">true</item>
    <item name="android:fitsSystemWindows">true</item>
    <item name="android:gravity">center</item>
    <item name="android:layout_gravity">center</item>
    <item name="android:windowActionBar">false</item>
    <item name="android:windowMinWidthMinor">100%</item>
    <item name="android:windowMinWidthMajor">100%</item>
    <item name="android:backgroundDimEnabled">false</item>
    <item name="android:windowFrame">@null</item>
    <item name="android:windowIsFloating">true</item>
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowNoTitle">true</item>
    <item name="android:background">@null</item>
    <item name="android:windowBackground">@android:color/transparent</item>
  </style></file><file path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\values-en\strings.xml" qualifiers="en"><string name="app_name">EasyControl</string><string name="main_poem">Unregulated driving, loved ones end up in tears.</string><string name="car_version_message">If you like the car version\nYou can left a star on the car version GitHub repository🙇</string><string name="main_float_permission">Please grant floating window permission.\nOtherwise, the software functions are incomplete.</string><string name="main_float_permission_button">Go authorize</string><string name="main_no_float_mode_button">Always full</string><string name="pair_ip">IP address</string><string name="pair_ip_hint">IP address of the device to be connected on the same network</string><string name="pair_pair">Pair</string><string name="pair_pairing_port_hint">Pairing port</string><string name="pair_pairing_code_hint">Pairing code</string><string name="pair_open_port">Open port 5555</string><string name="pair_debug_port_hint">Debug port</string><string name="pair_run">Run</string><string name="pair_cert">Pairing certificate</string><string name="pair_generating_cert">Pairing certificate generating</string><string name="pair_no_cert">No Pairing Certificate, please try regenerate</string><string name="pair_cert_regenerate">Regenerate</string><string name="pair_success"> success</string><string name="pair_failed"> failed</string><string name="set_default">Default settings</string><string name="set_display">Display</string><string name="set_wake_up_screen_on_connect">Wake up after connection</string><string name="set_wake_up_screen_on_connect_detail">Automatically wake up the controlled end after the connection is successful</string><string name="set_light_off_on_connect">Turn off backlight after connection</string><string name="set_light_off_on_connect_detail">Turn off the screen backlight two seconds after the connection is successful (need to turn on \u0022Wake up after connection\u0022)</string><string name="set_light_off_on_connect_error">Please turn on \u0022Wake up after connection\u0022</string><string name="set_lock_screen_on_close">Lock the screen after disconnection</string><string name="set_lock_screen_on_close_detail">Automatically lock the controlled end screen after the connection is disconnected</string><string name="set_light_on_on_close">Restore backlight after disconnection</string><string name="set_light_on_on_close_detail">Restore the screen backlight after the connection is disconnected (need to turn off \u0022Lock screen after disconnection\u0022)</string><string name="set_light_on_on_close_error">Please turn off \u0022Lock the screen after disconnection\u0022</string><string name="set_display_keep_screen_awake">Keep the controlled end awake</string><string name="set_display_keep_screen_awake_detail">The controlled end does not lock during the connection process</string><string name="set_display_auto_back_on_start_default">Return to launcher after a connection</string><string name="set_display_auto_back_on_start_default_detail">Return to launcher automatically when default device connected</string><string name="set_display_default_show_nav_bar">Show navigation bar by default</string><string name="set_display_default_show_nav_bar_detail">Sets whether the navigation bar is displayed by default in small window and full screen modes</string><string name="set_display_default_mini_on_outside">Minimize when touching outside the small window</string><string name="set_display_default_mini_on_outside_detail">(Small window mode) Minimized when touching outside the small window</string><string name="set_display_mini_recover_on_timeout">Floating window auto minimize</string><string name="set_display_mini_recover_on_timeout_detail">Touch outside of floating window to minimize it</string><string name="set_display_full_to_mini_on_exit">Fullscreen auto minimize</string><string name="set_display_full_to_mini_on_exit_detail">When fullscreen not manually exits, auto minimize it (Auto mini to floating window when close)</string><string name="set_display_full_fill">Stretch and fill in Fullscreen mode</string><string name="set_display_full_fill_detail">Stretch the image to fill all available areas in full-screen mode (Note: No stretching if the aspect ratio difference is too large)</string><string name="set_other">Other</string><string name="set_audio_channel">Audio output channel</string><string name="set_audio_channel_detail">Please keep 0 if there is no special need</string><string name="set_enable_usb">Enable USB detection</string><string name="set_enable_usb_detail">If you do not want to connect via USB, you can turn this off</string><string name="set_set_full_screen">Immersive full screen mode</string><string name="set_set_full_screen_detail">If you do not want the native navigation bar to disappear when in full screen mode, you can turn this off</string><string name="set_always_full_mode">Always full screen mode</string><string name="set_always_full_mode_detail">Always connect to the device in full screen mode, ignoring \u0022Default full screen\u0022 settings in the device (Only one default device can be opened when the software starts)</string><string name="set_mirror_mode">Android 14 compatibility mode</string><string name="set_mirror_mode_detail">If display issues occur on Android 14 and above, try enabling this option</string><string name="set_force_desktop_mode">Force desktop mode when using application transfer</string><string name="set_force_desktop_mode_detail">Input method can be synchronized during application transfer (Not recommended to enable, this desktop mode is not Samsung dex mode or similar function)</string><string name="set_try_start_default_in_app_transfer">Try application transfer when opening default device</string><string name="set_try_start_default_in_app_transfer_detail">Try application transfer when opening default device (Note: When the device specifies the package name, the application transfer is automatically attempted without turning on this)</string><string name="set_reconnect">Show reconnect dialog</string><string name="set_reconnect_detail">Display a dialog to attempt reconnection when the device is unexpectedly disconnected</string><string name="set_connect_usb">Show connect to default USB device dialog</string><string name="set_connect_usb_detail">Display the automatic connection dialog when the default USB device is detected; if disabled, connect automatically (APP must be on the main page)</string><string name="set_auto_countdown">Automatic task waiting time (seconds)</string><string name="set_auto_countdown_detail">Waiting time before automatic reconnection after device disconnection or before connecting to default USB device</string><string name="set_no_auto_countdown">Do not auto confirm</string><string name="set_other_custom_key">Custom key (ADB reauthorization required)</string><string name="set_other_clear_key">Regenerate the key (ADB reauthorization required)</string><string name="set_other_clear_key_code">Regenerated</string><string name="set_other_locale">Change language(中文/English)</string><string name="set_other_locale_code">已切换，可能需要重启APP</string><string name="set_app_monitor">App monitor (experimental)</string><string name="set_app_monitor_detail">It can minimize all windows when opening surrounding image and restore them when closing</string><string name="set_create_startup_shortcut">Create default device shortcut</string><string name="set_other_log">View log</string><string name="set_about">About</string><string name="set_about_ip">View local IP and scan device</string><string name="set_about_website">Website</string><string name="set_about_how_to_use">Instructions for use</string><string name="set_about_privacy">Privacy Policy</string><string name="set_license">License</string><string name="set_about_version">Version: </string><string name="ip_title">Local Address</string><string name="ip_ipv4">IPv4(Click to copy)</string><string name="ip_ipv6">IPv6(Click to copy)</string><string name="ip_copy">Copied</string><string name="ip_scan_device_title">Scan device</string><string name="ip_scanning_device">Scanning…</string><string name="ip_scan_finish_copy">Scan finished(Click to copy)</string><string name="ip_scan_finish_none">Scan finished(No device)</string><string name="monitor_title">App monitor (experimental)</string><string name="monitor_permission_request">Click to grant permission for usage statistics</string><string name="monitor_enable">Enable monitor</string><string name="monitor_latency">Monitor latency (ms)</string><string name="monitor_latency_detail">The smaller the size, the faster the detection, but it may also cause lag</string><string name="monitor_android_version_not_support">The current Android version does not support</string><string name="monitor_capture_event">Capture event</string><string name="monitor_no_permission">Please grant permission first</string><string name="monitor_no_event">Please capture event first</string><string name="monitor_capture_event_start">Click to start capture event</string><string name="monitor_capture_event_stop">Please trigger the app, click to stop capture event</string><string name="monitor_show_event">Related events (If no events, capture first)</string><string name="monitor_add_event">Add current event to</string><string name="monitor_add_change_to_mini_event">trigger to collapse</string><string name="monitor_add_change_to_small_event">trigger to recover</string><string name="monitor_change_to_mini_event">trigger to collapse (click to delete)</string><string name="monitor_change_to_small_event">trigger to recover (click to delete)</string><string name="log_title">Log</string><string name="log_other_devices">Other devices</string><string name="log_notify">An error occurred on a device, please check the log</string><string name="adb_key_pub">Public key (Base64 encoded)</string><string name="adb_key_pri">Private key (Base64 encoded)</string><string name="adb_key_button">Save</string><string name="adb_key_button_code">Saved</string><string name="add_device_name">Name</string><string name="add_device_name_hint">Non-null</string><string name="add_device_address">Address:Port</string><string name="add_device_address_hint">IPv4/v6 addresses or domain</string><string name="add_device_specify_app">Transfer specified application (package name)</string><string name="add_device_specify_app_hint">Leave blank for recently-used app transfer</string><string name="add_device_scan">Scan</string><string name="add_device_scanning">Scanning…</string><string name="add_device_scan_finish">Scan finished</string><string name="add_device_scan_address_finish_none">Scan finished(No device)</string><string name="add_device_scan_specify_app_finish_error">Scan error, please check if the device can be connected</string><string name="add_device_option">Advanced options</string><string name="add_device_button">OK</string><string name="set_device_title">Do what◔ ‸◔?</string><string name="set_device_button_night_mode">Dark mode</string><string name="night_mode_auto">Auto</string><string name="night_mode_current">Current dark mode: </string><string name="night_mode_custom">Custom</string><string name="night_mode_yes">Open</string><string name="night_mode_no">Close</string><string name="change_night_mode_success">Request success</string><string name="change_night_mode_failed">Request failed</string><string name="set_device_button_recover_error">Connection failed, please check ADB and network</string><string name="set_device_button_get_uuid">Copy ID</string><string name="set_device_button_get_uuid_success">Copied</string><string name="set_device_button_create_shortcut">Create shortcut</string><string name="set_device_button_start_wireless">Enable wireless</string><string name="set_device_button_start_wireless_success">Successfully</string><string name="set_device_button_change">Settings</string><string name="set_device_button_delete">Delete</string><string name="loading_text">Loading</string><string name="error_no_browser">No default browser</string><string name="error_address_error">Address format error</string><string name="error_refused_back">Fullscreen mode intercepts return key event</string><string name="error_connect_server">Failed to connect</string><string name="error_stream_closed">Disconnected</string><string name="error_create_display">The controlled end does not support application transfer (requires Android 11 and above)</string><string name="error_transfer_app_failed">application transfer failed, screen mirroring has been started</string><string name="error_app_not_found">The specified package name application does not exist</string><string name="error_device_not_found">Device not found</string><string name="error_default_device_not_found">Default device not found</string><string name="error_mode_not_support">Not supported in always full screen mode</string><string name="tip_application_transfer">Application transfer is an experimental feature, and some mobile phone systems do not support it</string><string name="tip_default_device">Default device</string><string name="tip_reconnect">Device disconnected abnormally. Reconnect?</string><string name="tip_default_usb">Default USB device detected. Connect?</string><string name="confirm">Confirm</string><string name="cancel">Cancel</string><string name="option_startup_device">Open when software starts</string><string name="option_startup_device_detail">Automatically connect to this device when the software starts</string><string name="option_default_usb_device">Default USB device</string><string name="option_default_usb_device_detail">Automatically connect when the software starts or this wired device is detected</string><string name="option_is_audio">Enable audio</string><string name="option_is_audio_detail">Works with Android 12 and above</string><string name="option_clipboard_sync">Clipboard sync</string><string name="option_clipboard_sync_detail">Synchronize clipboard between devices</string><string name="option_night_mode_sync">Dark mode sync</string><string name="option_night_mode_sync_detail">Synchronize dark mode between devices</string><string name="option_max_size">Resolution</string><string name="option_max_size_detail">Set max resolution</string><string name="option_max_size_original">Original resolution</string><string name="option_max_fps">Max FPS</string><string name="option_max_fps_detail">Frame per seconds</string><string name="option_max_video_bit">Max Video Bit</string><string name="option_max_video_bit_detail">Set stream bitrate. Suggested = 4</string><string name="option_use_h265">Use H265</string><string name="option_use_h265_detail">Enable H265 when available, disable it when encountered display issues</string><string name="option_use_opus">Use Opus</string><string name="option_use_opus_detail">Enable Opus when available</string><string name="option_default_full">Default full screen</string><string name="option_default_full_detail">Automatically enter full screen after connection</string><string name="option_set_resolution">Application transfer aspect ratio free scaling</string><string name="option_set_resolution_detail">Application transfer aspect ratio can be freely set</string><string name="start_display_mirroring">Display mirroring</string><string name="start_application_transfer">Application transfer</string></file><file path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\main\res\values-night\color.xml" qualifiers="night-v8"><color name="background">#222325</color><color name="onBackground">#DEDEDE</color><color name="onBackgroundSecond">#A7A7A7</color><color name="cardBackground">#3D3D3D</color><color name="onCardBackground">#FFFFFF</color><color name="onCardBackgroundSecond">#8B8B8B</color><color name="onCardDivider">#80FFFFFF</color><color name="button">#FFFFFF</color><color name="onButton">#414141</color><color name="alert">#ca5357</color><color name="onAlert">#ded8d8</color><color name="translucent">#000000ff</color><color name="clientBar">#579CF9</color><color name="clientBarSecond">#c07e7e7e</color><color name="bar1">#e8b621</color><color name="bar2">#3a7f9e</color><color name="bar3">#6eaa84</color><color name="bar4">#0A95FF</color></file></source><source path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\build\generated\res\rs\debug"/><source path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Link\a55haiwai_ut\a55haiwai\avslink\app_scrcpy\src\debug\res"/></dataSet><mergedItems/></merger>
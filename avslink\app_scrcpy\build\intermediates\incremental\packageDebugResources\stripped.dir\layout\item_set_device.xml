<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:background="@drawable/background_cron"
  android:orientation="vertical">

  <TextView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="25dp"
    android:layout_marginTop="25dp"
    android:layout_marginEnd="25dp"
    android:layout_marginBottom="15dp"
    android:text="@string/set_device_title"
    android:textColor="@color/onCardBackground"
    android:textSize="@dimen/middleFont" />

  <GridLayout
    android:id="@+id/button_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    android:columnCount="3"
    android:paddingStart="12dp"
    android:paddingEnd="12dp"
    android:paddingTop="12dp">

    <Button
      android:id="@+id/button_change"
      android:layout_columnWeight="9"
      android:background="@drawable/background_cron"
      android:backgroundTint="@color/alert"
      android:gravity="center"
      android:text="@string/set_device_button_change"
      android:textColor="@color/onAlert"
      android:textSize="@dimen/smallFont" />

    <Space android:layout_columnWeight="1" />

    <Button
      android:id="@+id/button_delete"
      android:layout_columnWeight="9"
      android:background="@drawable/background_cron"
      android:backgroundTint="@color/alert"
      android:gravity="center"
      android:text="@string/set_device_button_delete"
      android:textColor="@color/onAlert"
      android:textSize="@dimen/smallFont" />

    <Space
      android:layout_height="10dp"
      android:layout_columnSpan="3" />

    <Button
      android:id="@+id/button_night_mode"
      android:layout_columnWeight="9"
      android:background="@drawable/background_cron"
      android:backgroundTint="@color/button"
      android:gravity="center"
      android:text="@string/set_device_button_night_mode"
      android:textColor="@color/onButton"
      android:textSize="@dimen/smallFont" />

    <Space android:layout_columnWeight="1" />

    <Button
      android:id="@+id/button_get_uuid"
      android:layout_columnWeight="9"
      android:background="@drawable/background_cron"
      android:backgroundTint="@color/button"
      android:gravity="center"
      android:text="@string/set_device_button_get_uuid"
      android:textColor="@color/onButton"
      android:textSize="@dimen/smallFont" />

  </GridLayout>

  <Button
    android:id="@+id/button_create_shortcut"
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    android:background="@drawable/background_cron"
    android:backgroundTint="@color/button"
    android:gravity="center"
    android:text="@string/set_device_button_create_shortcut"
    android:textColor="@color/onButton"
    android:textSize="@dimen/smallFont"
    android:layout_marginStart="12dp"
    android:layout_marginEnd="12dp"
    android:layout_marginBottom="12dp" />

  <Button
    android:id="@+id/button_start_wireless"
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    android:background="@drawable/background_cron"
    android:backgroundTint="@color/button"
    android:gravity="center"
    android:text="@string/set_device_button_start_wireless"
    android:textColor="@color/onButton"
    android:textSize="@dimen/smallFont"
    android:layout_marginStart="12dp"
    android:layout_marginEnd="12dp"
    android:layout_marginBottom="16dp" />

</LinearLayout>
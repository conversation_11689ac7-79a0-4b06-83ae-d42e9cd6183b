<!-- Generated by SpecStory -->

# 分析 iOS 校准流程的时序 (2025-06-21 07:40Z)

_**User**_

请分析整个工程根据关键词 CalibrationCallBack,  校准流程文档,  梳理整个 iOS 校准流程。 从互联成功开始。 到校准成功，或者校准失败各个步骤的时序。 请详细输出，等我确认。 需要补充说明的是，这个项目是 车机端，会与 iOS 端互联，所以是两个设备相互通讯和交互的一个过程。 请详细输出，等我确认。 请详细输出，等我确认。 需要输出相互交互的时序。 第一步，第二步骤，这样。

---

_**Assistant**_

我将分析整个工程中与 CalibrationCallBack 相关的校准流程，梳理 iOS 与车机端互联校准的完整时序。

---

<details>
            <summary>Searched codebase "CalibrationCallBack 校准 iOS" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/CalibrationCallBack.java` | L1-30 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidIosCalibrationUtil.java` | L1-59 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2105-2137 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L35-90 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1080-1122 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L215-274 |
| `app/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L268-289 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L384-423 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L90-137 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java` | L30-82 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L575-602 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L884-913 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L940-976 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidIosCalibrationUtil.java` | L59-106 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L913-940 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L178-215 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L469-491 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java` | L320-383 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/BaseCalibrationStrategy.java` | L1-59 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L464-514 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L1-35 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1010-1039 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L325-368 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/LinearCalibrationStrategy.java` | L1-73 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/CurveCalibrationStrategy.java` | L65-113 |

</details>

---

让我继续搜索更多相关信息：

---

<details>
            <summary>Searched codebase "iOS 校准流程 calibration process" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L90-137 |
| `app/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L268-289 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L178-215 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L913-940 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L35-90 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java` | L30-82 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1010-1039 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L215-274 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1080-1122 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L384-423 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L884-913 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java` | L320-383 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidIosCalibrationUtil.java` | L1-59 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L2105-2137 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/BaseCalibrationStrategy.java` | L1-59 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L940-976 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/BaseCalibrationStrategy.java` | L450-490 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L464-514 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L575-602 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L469-491 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L1-35 |
| `app/src/main/java/com/autoai/wdlink/hu/frame/utils/PopWinService.java` | L325-368 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java` | L1-30 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L228-283 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/BaseCalibrationStrategy.java` | L380-413 |

</details>

---

让我查看更多关于校准流程的细节：

---

<details>
            <summary>Searched codebase "iOS 校准 流程 时序 checkPoint" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L913-940 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L90-137 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1010-1039 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L137-178 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L940-976 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1080-1122 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L884-913 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L215-274 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L35-90 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1039-1080 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java` | L125-148 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L35-83 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java` | L275-320 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/BaseCalibrationStrategy.java` | L450-490 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java` | L320-383 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java` | L30-82 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/BaseCalibrationStrategy.java` | L113-149 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/LinearCalibrationStrategy.java` | L73-122 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L464-514 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1122-1164 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/LinearCalibrationStrategy.java` | L163-212 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L178-215 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/BaseCalibrationStrategy.java` | L1-59 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/CurveCalibrationStrategy.java` | L204-244 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L1-35 |

</details>

---

让我查看更多关于校准流程的文档：

---

<details>
            <summary>Listed directory docs • **2** results</summary>
        
| Name |
|-------|
| 📄 `ios_calibration_flow_sequence.md` |
| 📄 `bluetooth_calibration_icon_display_logic.md` |

</details>

---

Read file: docs/bluetooth_calibration_icon_display_logic.md

---

让我查看另一个文档：

---

Read file: docs/ios_calibration_flow_sequence.md

---

Read file: docs/ios_calibration_flow_sequence.md

---

<no message>

---

<details>
            <summary>Searched codebase "iOS 校准 流程 时序图" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1010-1039 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L913-940 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L884-913 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L35-83 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L464-514 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1080-1122 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L90-137 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/BaseCalibrationStrategy.java` | L450-490 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L178-215 |
| `app/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L268-289 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L940-976 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L228-283 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L384-423 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1039-1080 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java` | L30-82 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L215-274 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L35-90 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L334-384 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/RegularCalibrationStrategy.java` | L320-383 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L1122-1164 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L76-147 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/calibration/BaseCalibrationStrategy.java` | L380-413 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L855-876 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L1-35 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/param/HidConstants.java` | L1-79 |

</details>

---

基于我的研究，我将为您详细梳理iOS校准流程的完整时序。

# iOS校准流程时序分析

## 互联成功后的校准流程

### 第一步：互联成功，初始状态检查
1. 车机端与iOS设备完成互联（`LinkSdkModel.INSTANCE.isConnected()` 返回 true）
2. 车机端通过`updateBTPairedWithPhone(true)`确认蓝牙配对状态
3. `PopWinService`检查设备平台，发现是iOS设备
4. 检查校准状态，如果是`FAILED`、`APP_NOT_FOREGROUND`或`BT_NOT_CONNECTED`
5. 显示校准图标（`BluetoothAbnormalState.IOS_NEEDS_CALIBRATION`）

### 第二步：用户触发校准
1. 用户点击校准图标
2. `PopWinService.handleBluetoothButtonClick()`处理点击事件
3. 调用`LinkSdkModel.INSTANCE.startVerification()`开始校准流程
4. 重置重试次数：`mIOSCalibrationRetryTimes = 0`
5. 设置`HidIosCalibrationUtil.setBreakDown(false)`，确保校准不被中断
6. 调用`HidIosCalibrationUtil.requestCalibration()`请求校准

### 第三步：车机端请求校准确认
1. `BluetoothHidManager.requestCalibration()`检查蓝牙连接状态
2. 如果蓝牙已连接：设置状态为`HID_VERIFY_TYPE_CONNECT`
3. 触发`CalibrationCallBack.onTouchPointRequest(ON_TOUCH_POINT_REQ_SUCCESS)`回调
4. `SdkViewModel`收到回调，发送消息给iOS端：`mSdkApi.sendIosHidTouchPoint(ON_TOUCH_POINT_REQ_SUCCESS)`
5. 设置校准状态：`HID_VERIFY_TYPE_VERIFICATION_ING`
6. 启动6秒超时监控：`hidVerifyTimeoutRunnable`

### 第四步：iOS端响应和准备
1. iOS端收到校准请求
2. iOS端检查应用状态和蓝牙连接
3. iOS端发送响应消息，可能是以下几种情况之一：
   - `ON_TOUCH_POINT_READY`(1) + 上次校准点数据（如果有）
   - `ON_TOUCH_APP_FRONT`(4)（应用在前台，无历史校准点）
   - `ON_TOUCH_END_POINT`(3)（应用不在前台，无法校准）

### 第五步：车机端处理iOS响应
车机端根据iOS端的响应消息类型进行不同处理：

#### 情况A - 收到`ON_TOUCH_POINT_READY`（有历史校准点）：
1. 调用`onTouchPointReady(上次校准点数据)`
2. 清除缓存：`HidIosCalibrationUtil.cleanCache()`
3. 验证上次校准点：`HidIosCalibrationUtil.checkPoint(obj)`
4. 如果验证成功且非重试状态：启动预校准`startPrepareVerification()`
5. 如果验证失败或重试状态：启动正式校准`startVerification()`

#### 情况B - 收到`ON_TOUCH_APP_FRONT`（无历史校准点）：
1. 直接启动正式校准：`HidIosCalibrationUtil.startVerification()`

#### 情况C - 收到`ON_TOUCH_END_POINT`（应用不在前台）：
1. 调用`onTouchSendEnd()`处理校准失败
2. 设置`breakDown = true`中断校准
3. 设置状态：`HID_VERIFY_TYPE_VERIFICATION_FAIL`
4. 触发失败回调：`onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL)`

## 预校准流程（如果有历史校准点且验证通过）

### 第六步A：预校准执行
1. 调用`HidIosCalibrationManager.startPrepareVerification()`
2. `BluetoothHidManager.startPrepareVerificationThread()`创建预校准线程
3. 触发`CalibrationCallBack.onTouchPrepareBeginPoint()`回调
4. 发送消息给iOS端：`sendIosHidTouchPoint(ON_TOUCH_POINT_PREPARE)`
5. 获取预校准点：`calibrationStrategy.getPrepareCalibrationPoints()`
6. 依次发送HID鼠标移动和点击命令（少量校准点）
7. 触发`CalibrationCallBack.onTouchPrepareEndPoint()`回调
8. 发送消息给iOS端：`sendIosHidTouchPoint(ON_TOUCH_POINT_PREPARE_END)`

### 第七步A：iOS端预校准响应
1. iOS端收集实际生效的预校准点坐标
2. iOS端发送预校准结果：`ON_TOUCH_SEND_PREPARE_POINT` + 实际坐标数据

### 第八步A：预校准结果验证
1. 车机端收到`ON_TOUCH_SEND_PREPARE_POINT`消息
2. 调用`onTouchPointCheckPrepareCalibration(obj)`处理
3. 验证预校准点：`HidIosCalibrationUtil.checkPrepareCalibPoints(obj)`
4. 如果验证成功：
   - 直接结束校准：`onTouchSendPointCheckSucc()`
   - 发送成功消息：`sendIosHidTouchPoint(ON_TOUCH_POINT_END_SUCCESS)`
   - 设置状态：`HID_VERIFY_TYPE_VERIFICATION_SUCCESS`
   - 触发成功回调：`onCheckHiTouchEvent(ON_HID_SUCCESS)`
5. 如果验证失败：
   - 启动正式校准：`HidIosCalibrationUtil.startVerification()`

## 正式校准流程（如果无历史校准点或预校准失败）

### 第六步B：正式校准执行
1. 调用`HidIosCalibrationManager.startVerification()`
2. 清除缓存：`cleanCache()`
3. 初始化校准策略：`calibrationStrategy = new LinearCalibrationStrategy(sp)`
4. `BluetoothHidManager.startVerificationThread()`创建校准线程
5. 触发`CalibrationCallBack.onTouchBeginPoint()`回调
6. 发送消息给iOS端：`sendIosHidTouchPoint(ON_TOUCH_POINT_BEGIN)`
7. 获取校准点：`calibrationStrategy.getCalibrationPoints()`
8. 重置鼠标位置到右下角：`resetMouse()`
9. 依次发送校准点（移动+点击，通常5-8个点）
10. 触发`CalibrationCallBack.onTouchEndPoint()`回调
11. 发送消息给iOS端：`sendIosHidTouchPoint(ON_TOUCH_POINT_END)`

### 第七步B：iOS端校准响应
1. iOS端收集实际生效的校准点坐标
2. iOS端发送校准结果：`ON_TOUCH_SEND_POINT` + 实际坐标数据

### 第八步B：校准结果验证
1. 车机端收到`ON_TOUCH_SEND_POINT`消息
2. 调用`onTouchSendPoint(obj)`处理
3. 验证校准点：`HidIosCalibrationUtil.checkPoint(obj)`
   - 解析坐标数据：`setCheckPoint(points)`
   - 使用校准策略验证：`calibrationStrategy.parseCalibrationPoints()`
   - 如果线性校准失败，自动切换到曲线校准：`calibrationStrategy = new CurveCalibrationStrategy(sp)`
4. 如果验证成功：
   - 调用`onTouchSendPointCheckSucc()`
   - 发送成功消息：`sendIosHidTouchPoint(ON_TOUCH_POINT_END_SUCCESS)`
   - 设置状态：`HID_VERIFY_TYPE_VERIFICATION_SUCCESS`
   - 触发成功回调：`onCheckHiTouchEvent(ON_HID_SUCCESS)`
5. 如果验证失败：
   - 设置状态：`HID_VERIFY_TYPE_VERIFICATION_FAIL`
   - 触发失败回调：`onCheckHiTouchEvent(ON_HID_FAIL)`

## 校准结果处理

### 第九步：重试机制
1. `onCheckHiTouchEvent(state)`处理校准结果：

   如果成功(`ON_HID_SUCCESS`)：
   - 重置重试次数
   - 更新UI状态为正常
   - 隐藏校准图标
   - 启用HID控制功能

   如果失败且重试次数 < 2：
   - 增加重试计数：`mIOSCalibrationRetryTimes++`
   - 清除缓存：`cleanCache()`
   - 重新请求校准：`requestCalibration()`
   - 重新开始整个校准流程

   如果失败且重试次数 >= 2：
   - 重置重试次数：`mIOSCalibrationRetryTimes = 0`
   - 触发最终失败回调
   - 显示校准失败状态

### 第十步：校准状态回调和UI更新
1. `PopWinService`收到校准结果回调
2. `CalibrationMonitoringCallback`处理结果：
   - 0：校准成功 → `CalibrationStatus.SUCCESS`
   - 1：校准失败 → `CalibrationStatus.FAILED`
   - 2：蓝牙未连接 → `CalibrationStatus.BT_NOT_CONNECTED`
   - 3：应用不在前台 → `CalibrationStatus.APP_NOT_FOREGROUND`
3. `bluetoothStateManager.onCalibrationResult(status)`更新状态
4. 更新UI：
   - 成功：隐藏校准图标（`NORMAL`状态）
   - 失败：显示校准图标和失败提示（`IOS_NEEDS_CALIBRATION`状态）

## 异常情况处理

### 超时处理
- 如果iOS端在6秒内未响应校准请求：
  - 设置状态：`HID_VERIFY_TYPE_HID_CONNECT_VERIFICATION_FAIL`
  - 发送失败消息：`sendIosHidTouchPoint(ON_TOUCH_POINT_REQ_FAIL)`
  - 触发连接失败回调：`onCheckHiTouchEvent(ON_HID_CONNECT_FAIL)`

### 蓝牙断开处理
- 如果校准过程中蓝牙断开：
  - 设置`breakDown = true`（中断校准）
  - 重置鼠标位置：`resetMouse()`
  - 停止当前校准流程
  - 更新UI状态为蓝牙未连接

### 应用后台处理
- 如果iOS应用在校准过程中切换到后台：
  - iOS端发送`ON_TOUCH_END_POINT`
  - 车机端调用`onTouchSendEnd()`
  - 设置`breakDown = true`
  - 设置状态：`HID_VERIFY_TYPE_VERIFICATION_FAIL`
  - 触发后台失败回调：`onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL)`

## 校准成功后的状态

- 校准成功后，车机端会：
  1. 设置`controlAvailable = true`，允许HID控制
  2. 更新校准状态为`CalibrationStatus.SUCCESS`
  3. 隐藏校准图标
  4. 正常显示iOS投屏内容
  5. 启用HID反控功能

## 校准失败后的状态

- 校准失败后，车机端会：
  1. 设置`controlAvailable = false`，禁用HID控制
  2. 更新校准状态为`CalibrationStatus.FAILED`
  3. 显示校准图标
  4. 显示校准失败提示和重新校准选项
  5. 等待用户重新触发校准

以上就是完整的iOS校准流程时序，从互联成功开始到校准成功或失败的各个步骤。这个流程包含了车机端与iOS设备之间的交互，以及各种异常情况的处理机制。

---


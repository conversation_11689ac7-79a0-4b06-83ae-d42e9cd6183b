package com.autoai.wdlink.hu.view

import com.autoai.wdlink.hu.databinding.FragmentConnectingBinding
import com.autoai.wdlink.hu.frame.BaseFragment
import com.autoai.wdlink.hu.viewmodel.ConnectingViewModel

class ConnectingFragment : BaseFragment<ConnectingViewModel, FragmentConnectingBinding>(FragmentConnectingBinding::inflate) {

    override fun getViewModelClass() = ConnectingViewModel::class

    override fun getViewBindingClass() = FragmentConnectingBinding::class
}
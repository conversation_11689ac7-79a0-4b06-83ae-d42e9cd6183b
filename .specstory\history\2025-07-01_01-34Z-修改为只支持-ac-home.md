<!-- Generated by SpecStory -->

# 修改为只支持 AC HOME (2025-07-01 01:34Z)

_**User**_

改成只需要支持 AC HOME.

---

_**Assistant**_



---

_**User**_

  if(HidIosUtil.isClickBtn(x, y)){
                    if (!changedSDP) {
                        HidScreenConnectManager.getInstance().reRegisterApp();
                        changedSDP = true;
                    }
                    else {
                        hidSendHomeKey();
                    }
                 //

                } 将这部分替换成 发送手柄 ac home 按键呢。

---

_**Assistant**_

I'll help you modify the code to send a gamepad AC HOME button press instead of the current implementation. Let's search for the relevant files first.

---

<details>
            <summary>Searched codebase "hidSendHomeKey function implementation" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L886-942 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L306-373 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java` | L611-678 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/DeviceManager.java` | L233-288 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java` | L571-611 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/DeviceManager.java` | L170-233 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L249-306 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L228-285 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L716-758 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidConfig.java` | L107-138 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanIos.java` | L48-93 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/util/TouchUtil.java` | L1-39 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidAoa.java` | L1-67 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L169-228 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanAndroid.java` | L82-131 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidAoa.java` | L67-109 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/util/TouchUtil.java` | L367-406 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidUtil.java` | L129-189 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidConfig.java` | L35-59 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L115-155 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L30-81 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/util/TouchUtil.java` | L86-156 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L187-217 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidDescriptors.kt` | L45-74 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidApi.java` | L1-61 |

</details>

---

Now let's look at how we can implement sending the AC HOME button press. Let's first check the file where the code to be modified is located.

---

<details>
            <summary>Searched codebase "HidIosUtil.isClickBtn implementation" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/util/TouchUtil.java` | L367-406 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/util/HidIosUtil.java` | L128-194 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanIos.java` | L48-93 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L716-758 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanIos.java` | L1-48 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/util/TouchUtil.java` | L243-301 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenTouchManager.java` | L66-94 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/util/TouchUtil.java` | L1-39 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L271-313 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L548-594 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothGestureHandler.kt` | L1-70 |
| `app/src/main/java/com/autoai/wdlink/hu/module/hidscreen/HidScreenTouchListener.java` | L1-31 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidIosCalibrationUtil.java` | L59-106 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L1-29 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidIosCalibrationManager.java` | L36-91 |
| `app/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L404-464 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L199-245 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L498-548 |
| `app/src/main/java/com/autoai/wdlink/hu/module/hidscreen/HidScreenTouchDelegateHelper.kt` | L1-45 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/util/TouchUtil.java` | L301-367 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidAoa.java` | L1-67 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L418-457 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L77-117 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanAndroid.java` | L82-131 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L313-368 |

</details>

---

Let's now look for the implementation of sending the AC HOME key.

---

<details>
            <summary>Searched codebase "sendGamepadAcHomeKey implementation" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L306-373 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/DeviceManager.java` | L233-288 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L228-285 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L886-942 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanAndroid.java` | L82-131 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java` | L611-678 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L716-758 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L249-306 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidConfig.java` | L107-138 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L209-257 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java` | L571-611 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/DeviceManager.java` | L170-233 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothGestureHandler.kt` | L1-70 |
| `app/src/main/java/com/autoai/wdlink/hu/viewmodel/MainActivityViewModel.kt` | L55-118 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L187-217 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidAoa.java` | L67-109 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidAoa.java` | L1-67 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L778-814 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/MainActivity.kt` | L119-153 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanIos.java` | L48-93 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L169-228 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/view/FullActivity.java` | L208-246 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L147-208 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothGestureHandler.kt` | L70-78 |
| `avslink/linkSdk/src/main/java/com/autoai/welink/SdkInterface.java` | L304-345 |

</details>

---

Let's look for a method that can send a gamepad AC HOME button press:

---

<details>
            <summary>Searched codebase "send gamepad AC HOME button" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidConfig.java` | L107-138 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L306-373 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/DeviceManager.java` | L233-288 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L716-758 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/helper/MyBroadcastReceiver.java` | L81-129 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java` | L611-678 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/TouchManager.java` | L249-306 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L228-285 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanAndroid.java` | L82-131 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/DeviceManager.java` | L170-233 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidConfig.java` | L86-107 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java` | L886-942 |
| `avslink/testapp/src/main/java/com/autoai/wdlink/hu/sdk/LinkSdkModel.kt` | L147-208 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java` | L571-611 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/DeviceManager.java` | L169-228 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothGestureHandler.kt` | L70-78 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/HidConfig.java` | L34-61 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanIos.java` | L48-93 |
| `avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/view/SmallView.java` | L499-538 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/HidConfig.java` | L61-76 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidAoa.java` | L67-109 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidAoa.java` | L1-67 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/datatransfer/AOAManager.java` | L187-217 |
| `avslink/linkHost/src/main/java/com/autoai/welinkapp/model/SdkViewModel.java` | L778-814 |
| `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidConfig.java` | L138-165 |

</details>

---

Based on the code I've found, I need to create a method to send a gamepad AC HOME button press. Let's look at the HID descriptor to understand the report format:

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/HidConfig.java

---

Now let's look at the BluetoothHidManager class to understand the current implementation of hidSendHomeKey():

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java

---

Now let's check the HidScreenConnectManager class to understand how to send a report with the gamepad AC HOME button:

---

Read file: link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java

---

Based on the code review, I'll now implement a method to send a gamepad AC HOME button press. Let's create a new method in the BluetoothHidManager class and modify the code to use it.

---



---

Now let's add the sendGamepadAcHomeKey method to the BluetoothHidManager class:

---



---

Now let's also update the backHome method in the HidWlanIos class to use our new sendGamepadAcHomeKey method:

---



---

The changes have been successfully implemented. Here's a summary of what I did:

1. Created a new `sendGamepadAcHomeKey()` method in the BluetoothHidManager class that:
   - Creates a 1-byte report where 0x01 means "press" and 0x00 means "release"
   - Sends the report with reportId=3 (the gamepad device)
   - Logs the results for debugging

2. Modified the code in the ACTION_DOWN case to use the new `sendGamepadAcHomeKey()` method when the user clicks in the iOS home button area, replacing the previous implementation that used SDP registration and hidSendHomeKey().

3. Updated the `backHome()` method in HidWlanIos class to use the new `sendGamepadAcHomeKey()` method instead of calling hidSendHomeKey() twice.

These changes will ensure that when a user clicks in the iOS home button area or calls the backHome() method, the system will send an AC HOME button press using the gamepad HID device (reportId=3) as specified in the HID descriptor.

---


<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:background="@color/background"
  android:orientation="vertical"
  tools:context=".SetActivity">

  <ImageView
    android:id="@+id/back_button"
    android:layout_width="54dp"
    android:layout_height="54dp"
    android:padding="15dp"
    android:src="@drawable/chevron_left"
    android:tint="@color/onBackground" />

  <ScrollView
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="vertical"
      android:paddingStart="15dp"
      android:paddingEnd="15dp">

      <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="5dp"
        android:text="@string/set_default"
        android:textColor="@color/onBackground"
        android:textSize="@dimen/middleFont" />

      <LinearLayout
        android:id="@+id/set_default"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/background_cron"
        android:orientation="vertical"
        android:padding="15dp"
        android:divider="@drawable/divider_line"
        android:dividerPadding="5dp"
        android:showDividers="middle" />

      <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="5dp"
        android:text="@string/set_display"
        android:textColor="@color/onBackground"
        android:textSize="@dimen/middleFont" />

      <LinearLayout
        android:id="@+id/set_display"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/background_cron"
        android:orientation="vertical"
        android:padding="15dp"
        android:divider="@drawable/divider_line"
        android:dividerPadding="5dp"
        android:showDividers="middle" />

      <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="5dp"
        android:text="@string/set_other"
        android:textColor="@color/onBackground"
        android:textSize="@dimen/middleFont" />

      <LinearLayout
        android:id="@+id/set_other"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/background_cron"
        android:orientation="vertical"
        android:padding="15dp"
        android:divider="@drawable/divider_line"
        android:dividerPadding="5dp"
        android:showDividers="middle" />

      <TextView
        android:id="@+id/text_about"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="5dp"
        android:text="@string/set_about"
        android:textColor="@color/onBackground"
        android:textSize="@dimen/middleFont" />

      <LinearLayout
        android:id="@+id/set_about"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25dp"
        android:background="@drawable/background_cron"
        android:orientation="vertical"
        android:padding="15dp"
        android:divider="@drawable/divider_line"
        android:dividerPadding="5dp"
        android:showDividers="middle" />

    </LinearLayout>
  </ScrollView>
</LinearLayout>
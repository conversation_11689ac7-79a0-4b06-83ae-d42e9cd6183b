package com.autoai.wdlink.hu.view

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.autoai.avs.common.qrcode.QRCodeUtil
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.databinding.FragmentRunningBinding
import com.autoai.wdlink.hu.databinding.FragmentSuccessBinding
import com.autoai.wdlink.hu.databinding.FragmentTipsBinding
import com.autoai.wdlink.hu.frame.BaseFragment
import com.autoai.wdlink.hu.viewmodel.RunningViewModel
import com.autoai.wdlink.hu.viewmodel.SuccessViewModel
import com.autoai.wdlink.hu.viewmodel.TipsViewModel


class TipsFragment : BaseFragment<TipsViewModel, FragmentTipsBinding>(FragmentTipsBinding::inflate) {

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.qrCodeImageView.setImageBitmap(QRCodeUtil.createQRCodeBitmap("https://www.baidu.com",200))//创建大小为200px的二维码图片，内容为url)
    }

    override fun getViewModelClass() = TipsViewModel::class

    override fun getViewBindingClass() = FragmentTipsBinding::class



}
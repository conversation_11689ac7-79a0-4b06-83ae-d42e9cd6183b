<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:baselineAligned="false"
    android:orientation="horizontal"
    tools:background="@drawable/bg_color">

    <LinearLayout
        android:layout_width="232dp"
        android:layout_height="match_parent"
        android:background="@drawable/bg_color"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_help_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="40dp"
            android:paddingTop="34dp"
            android:paddingBottom="34dp"
            android:paddingRight="10dp"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/help_back"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end|center_vertical"
                android:text="@string/help_title"
                android:textColor="@color/text_color"
                android:textSize="@dimen/tips_text_size" />
        </LinearLayout>


        <TextView
            android:id="@+id/tv_help_android"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_help_tab"
            android:gravity="center_vertical|end"
            android:padding="50dp"
            android:text="@string/help_title_android"
            android:textColor="@color/text_color"
            android:textSize="@dimen/tips_text_size" />

        <TextView
            android:id="@+id/tv_help_ios"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_help_tab"
            android:gravity="center_vertical|end"
            android:padding="50dp"
            android:text="@string/help_title_ios"
            android:textColor="@color/text_color"
            android:textSize="@dimen/tips_text_size" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/phone_link_help"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@color/help_right_bg"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingStart="80dp"
        android:paddingEnd="80dp">

        <LinearLayout
            android:id="@+id/steps"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_centerInParent="true"
            tools:ignore="DisableBaselineAlignment">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                tools:ignore="UseCompoundDrawables">

                <ImageView
                    android:id="@+id/tv_help_phone_image_1"
                    android:layout_width="234dp"
                    android:layout_height="142dp"
                    android:src="@mipmap/help_android_1"
                    android:scaleType="fitCenter"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/tv_help_phone_text_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:lineSpacingExtra="@dimen/help_text_line_space"
                    android:text="@string/help_android_toast_step1"
                    android:textColor="@color/text_color"
                    android:textSize="@dimen/tips_text_help_size" />

            </LinearLayout>
            <View
                android:layout_width="80dp"
                android:layout_height="1dp"/>
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                tools:ignore="UseCompoundDrawables">

                <ImageView
                    android:id="@+id/tv_help_phone_image_2"
                    android:layout_width="248dp"
                    android:layout_height="142dp"
                    android:src="@mipmap/help_android_2"
                    android:scaleType="fitCenter"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/tv_help_phone_text_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:lineSpacingExtra="@dimen/help_text_line_space"
                    android:text="@string/help_android_toast_step2"
                    android:textColor="@color/text_color"
                    android:textSize="@dimen/tips_text_help_size" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/tv_help_phone_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:text="@string/help_phone_tip"
            android:textColor="@color/text_color"
            android:textSize="@dimen/tips_text_size" />
    </RelativeLayout>
</LinearLayout>
<!-- res/layout/custom_toast_layout.xml -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="492px"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/close_bg"
    android:paddingTop="25dp"
    android:paddingBottom="25dp"
    android:paddingLeft="17dp"
    android:paddingRight="17dp">

    <LinearLayout
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">
        <ImageView
            android:layout_width="52px"
            android:layout_gravity="center"
            android:layout_height="52px"
            android:src="@mipmap/toast_img" />

        <TextView
            android:layout_gravity="center"
            android:id="@+id/toast_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#222222"
            android:textSize="20px"
            android:layout_marginLeft="16px"
            android:layout_marginStart="8dp"
            android:text="This is a custom toast" />

    </LinearLayout>
    <TextView
        android:id="@+id/toast_context"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#222222"
        android:textSize="16px"
        android:layout_marginLeft="16px"
        android:layout_marginStart="60px"
        android:text="This is a custom toast" />
    <LinearLayout
        android:id="@+id/button_lin"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginTop="25px"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/button1"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="按钮1"
            android:gravity="center"
            android:padding="8dp"
            android:background="@drawable/btn_click_c"
            android:textColor="#222222"
            android:textSize="20px"
        />
        <TextView
            android:id="@+id/button2"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_marginLeft="5dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="按钮1"
            android:gravity="center"
            android:padding="8dp"
            android:background="@drawable/btn_click_c"
            android:textColor="#222222"
            android:textSize="20px"/>
    </LinearLayout>
</LinearLayout>

package com.autoai.wdlink.hu.module.hidscreen

import android.graphics.Rect
import android.util.Log
import android.view.MotionEvent
import android.view.TouchDelegate
import android.view.View
import com.autoai.wdlink.hu.MyApplication
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.castwindow.view.CastWindowStateManager

/**
 * 触摸投屏区域外的部分，处理边缘滑动返回，上滑回桌面
 */
class HidScreenOutsideTouchDelegate
    (val mBounds: Rect, val original: Rect, val mDelegateView: View, val callback: (()->Boolean)?)
    : TouchDelegate(mBounds, mDelegateView) {
    companion object{
        private const val TAG = "TouchDelegate"
        // 区分全屏和浮窗的边缘位置
        private var topMargin =
            MyApplication.application.resources.getDimensionPixelSize(R.dimen.extend_size2) +
                    MyApplication.application.resources.getDimensionPixelSize(R.dimen.dimen_40)

        private var leftMargin = 88

        private var viewWidth = 0;
        /**
         * delegateView --> texture 外部 parent
         * parentView  -->  window  主布局
         */
        @JvmStatic
        fun setTouchDelegateWhenReady(delegateView: View, parentView: View, insets: Rect, isFullScreen: Boolean, callback: (()->Boolean)?) {
            Log.i(TAG, "setTouchDelegateWhenReady: post: invoke")
            delegateView.post {
                // 获取textureView 原始边界Rect
                val originalBound = Rect()
                CastWindowStateManager.getTextureView()?.getHitRect(originalBound)

                //代理区域矩阵
                var delegate = Rect(originalBound)
                if (isFullScreen) delegate = matrixTransfor(delegate)
                // 拓展之后的边界
                val bound = Rect()
                delegateView.getHitRect(bound)


                if (isFullScreen) {
                    bound.apply {
                        Log.i(TAG, "befor left:  = $left")
                        left += (insets.left * 2)
                        Log.i(TAG, "after left:  = $left")
                        top += insets.top
                        right -= insets.right
                        Log.i(TAG, "befor bottom:  = $bottom")
                        bottom -= insets.bottom
                        Log.i(TAG, "after bottom:  = $bottom")
                    }
                    topMargin = 0
                    leftMargin = 88
                } else {
                    bound.apply {
                        left += insets.left
                        top += insets.top
                        right -= (insets.right * 2) // 因左侧为负值，故右侧需加上左右两侧的边界值
                        Log.i(TAG, "befor bottom:  = $bottom")
                        bottom -= insets.bottom - 100 // 可换成 insets.bottom+ 透明区域（60） + 顶部功能高度（40） + 边框的距离（10）
                        Log.i(TAG, "after bottom:  = $bottom")
                    }
                    topMargin =
                        MyApplication.application.resources.getDimensionPixelSize(R.dimen.extend_size2) +
                                MyApplication.application.resources.getDimensionPixelSize(R.dimen.dimen_40)
                    leftMargin = MyApplication.application.resources.getDimensionPixelSize(R.dimen.extend_size2)
                }
                Log.i(TAG, "setTouchDelegateWhenReady: topMargin = $topMargin, leftMargin = $leftMargin")

                viewWidth = delegateView.width
                Log.i(TAG, "setTouchDelegateWhenReady: touchDelegate: delegateW:" + delegateView.width +" ,delegateH:"+delegateView.height)

                Log.i(TAG, "setTouchDelegateWhenReady: touchDelegate: delegate = $delegate, bound = $bound")
                parentView.touchDelegate = HidScreenOutsideTouchDelegate(bound, delegate, delegateView, callback);
            }
        }
        fun matrixTransfor(rect : Rect) : Rect{
            rect.right = rect.right - rect.left
            rect.left = 0
            return rect;
        }
    }

    private var mDelegateTargeted = false
    private var isInDelegate = false

    /**
     * 发现这个点就在被代理view自身区域 ，标记flag
     */
    private var isInOriginalView = false
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val enable = callback?.invoke()
        Log.i(TAG, "onTouchEvent: enable = $enable, action = $event.actionMasked")
        if(enable != true){
            return false
        }
        val x = event.x.toInt()
        val y = event.y.toInt()
        // 透明区域 + 顶部功能高度 + 边框(宽度10)

//        val topMargin =
//            MyApplication.application.resources.getDimensionPixelSize(R.dimen.extend_size2) +
//                    MyApplication.application.resources.getDimensionPixelSize(R.dimen.dimen_40)
//
//        val leftMargin = 88
//            MyApplication.application.resources.getDimensionPixelSize(R.dimen.extend_size2)


        Log.i(TAG, "62 onTouch x：" + x +",y: "+ x )
        var locX = x - leftMargin
        // 顶部触摸点在外部
        var locY = y - topMargin
        // 顶部触摸点在外部，可支持顶部拓展区域下滑 1
//        var locY = y
        Log.i(TAG, "68 onTouch x：" + locX +",y: "+ locY )

        var sendToDelegate = false
        var handled = false
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                //--> 标记为需要分发
                mDelegateTargeted = mBounds.contains(locX, locY)
                Log.i(TAG, "onTouchEvent: ACTION_DOWN: x = $locX, y = $locY")
                //-->转化一次临时变量 ？？？
                sendToDelegate = mDelegateTargeted
                isInDelegate = false
                isInOriginalView = false
            }

            MotionEvent.ACTION_POINTER_DOWN,
            MotionEvent.ACTION_POINTER_UP-> {
                //move 事件跟着down 事件标识的情况走
                sendToDelegate = mDelegateTargeted
            }

            MotionEvent.ACTION_MOVE,
            MotionEvent.ACTION_UP -> {
                if (locX < 0 || locX > viewWidth) {
                    sendToDelegate = false
                }else {
                    sendToDelegate = mDelegateTargeted
                }
            }

            MotionEvent.ACTION_CANCEL -> {
                sendToDelegate = mDelegateTargeted
                mDelegateTargeted = false
            }
        }

        //--> 需要处理
        if (sendToDelegate) {

            //--> 发现这个点x,y就在自身区域
            if(original.contains(locX, locY)){
                //派发一次边缘 ACTION_DOWN 事件

                //--> 发现这个点就在被代理view自身区域 ，标记flag
                isInOriginalView = true

                if(isInDelegate){
                    isInDelegate = false
                    val downEvent = MotionEvent.obtain(event)
                    downEvent.action = MotionEvent.ACTION_DOWN
                    // 左侧触摸点在外部拓展区域拓展区域
                    if( locX < 0) locX = 0
                    if( locX > viewWidth) locX = viewWidth

                    // 顶部触摸点在外部，可支持顶部拓展区域下滑 2
//                    locY = y - topMargin
//                    if( locY < 0) locY = 0

                    Log.i(TAG, "108 onTouch x：" + locX +",y: "+ locY )
                    downEvent.setLocation((locX).toFloat(), (locY).toFloat())
                    mDelegateView.dispatchTouchEvent(downEvent)
                    Log.w(TAG, "onTouchEvent: sendToDelegate down to original view")
                }
                Log.i(TAG, "onTouchEvent: sendToDelegate move to original view")

                // 左侧触摸点在外部拓展区域
                if( locX < 0) locX = 0
                Log.i(TAG, "onTouchEvent: 152 viewWidth：" + viewWidth)
                if( locX > viewWidth) locX = viewWidth

                // 顶部触摸点在外部，可支持顶部拓展区域下滑 3
//                locY = y - topMargin
//                if( locY < 0) locY = 0

                Log.i(TAG, "122 onTouch x：" + locX +",y: "+ locY )
                event.setLocation((locX).toFloat(), (locY).toFloat())
                mDelegateView.dispatchTouchEvent(event)
            } else {
                // 为了处理在边缘滑动时 可以触发手机的边缘滑动（例如：抖音的从手机左侧边缘和右侧边缘滑动退出）
                isInDelegate = true
                if(isInOriginalView){
                    isInOriginalView = false
                    //从范围内划出边界，发送取消事件
                    val upEvent = MotionEvent.obtain(event)
                    upEvent.action = MotionEvent.ACTION_UP
                    mDelegateView.dispatchTouchEvent(upEvent)
                    //不再响应后续的划入事件了
                    mDelegateTargeted = false
                }
                Log.i(TAG, "onTouchEvent: sendToDelegate event in delegate")
            }
            handled = true
        }
        Log.i(TAG, "onTouchEvent: handled = $handled")

        return handled
    }
}
// Generated by view binder compiler. Do not edit!
package top.eiyooooo.easycontrol.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.GridLayout;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import top.eiyooooo.easycontrol.app.R;

public final class ItemSetDeviceBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button buttonChange;

  @NonNull
  public final Button buttonCreateShortcut;

  @NonNull
  public final Button buttonDelete;

  @NonNull
  public final Button buttonGetUuid;

  @NonNull
  public final GridLayout buttonLayout;

  @NonNull
  public final Button buttonNightMode;

  @NonNull
  public final Button buttonStartWireless;

  private ItemSetDeviceBinding(@NonNull LinearLayout rootView, @NonNull Button buttonChange,
      @NonNull Button buttonCreateShortcut, @NonNull Button buttonDelete,
      @NonNull Button buttonGetUuid, @NonNull GridLayout buttonLayout,
      @NonNull Button buttonNightMode, @NonNull Button buttonStartWireless) {
    this.rootView = rootView;
    this.buttonChange = buttonChange;
    this.buttonCreateShortcut = buttonCreateShortcut;
    this.buttonDelete = buttonDelete;
    this.buttonGetUuid = buttonGetUuid;
    this.buttonLayout = buttonLayout;
    this.buttonNightMode = buttonNightMode;
    this.buttonStartWireless = buttonStartWireless;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSetDeviceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSetDeviceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_set_device, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSetDeviceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_change;
      Button buttonChange = ViewBindings.findChildViewById(rootView, id);
      if (buttonChange == null) {
        break missingId;
      }

      id = R.id.button_create_shortcut;
      Button buttonCreateShortcut = ViewBindings.findChildViewById(rootView, id);
      if (buttonCreateShortcut == null) {
        break missingId;
      }

      id = R.id.button_delete;
      Button buttonDelete = ViewBindings.findChildViewById(rootView, id);
      if (buttonDelete == null) {
        break missingId;
      }

      id = R.id.button_get_uuid;
      Button buttonGetUuid = ViewBindings.findChildViewById(rootView, id);
      if (buttonGetUuid == null) {
        break missingId;
      }

      id = R.id.button_layout;
      GridLayout buttonLayout = ViewBindings.findChildViewById(rootView, id);
      if (buttonLayout == null) {
        break missingId;
      }

      id = R.id.button_night_mode;
      Button buttonNightMode = ViewBindings.findChildViewById(rootView, id);
      if (buttonNightMode == null) {
        break missingId;
      }

      id = R.id.button_start_wireless;
      Button buttonStartWireless = ViewBindings.findChildViewById(rootView, id);
      if (buttonStartWireless == null) {
        break missingId;
      }

      return new ItemSetDeviceBinding((LinearLayout) rootView, buttonChange, buttonCreateShortcut,
          buttonDelete, buttonGetUuid, buttonLayout, buttonNightMode, buttonStartWireless);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
